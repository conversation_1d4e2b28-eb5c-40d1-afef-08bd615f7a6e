<?php
/**
 * Validação das configurações multi-tenant para MySQL 8.0
 */

// Definir ROOT_SYS antes de incluir tenants.inc.php
define('ROOT_SYS', '/var/www/html');

require_once '/var/www/html/classes/tenants.inc.php';
require_once '/var/www/html/classes/ConfigManager.php';

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== VALIDAÇÃO CONFIGURAÇÕES MULTI-TENANT ===\n\n";

// Simular diferentes hosts para teste
$test_hosts = [
    'editor.ltr.lettore.com.br',
    'editor.biblos.lettore.com.br', 
    'editor.ibragesp.lettore.com.br',
    'localhost:8080',
    'lettore-app'  // Container name
];

echo "1. TESTANDO DETECÇÃO DE TENANT:\n";

foreach ($test_hosts as $host) {
    echo "\nHost: {$host}\n";
    
    // Simular SERVER variables
    $_SERVER['HTTP_HOST'] = $host;
    
    try {
        $config = ConfigManager::getInstance();
        
        if ($config->getTenant()) {
            $tenant_data = $config->getTenant();
            echo "✓ Tenant detectado: {$tenant_data['tenant']}\n";
            echo "  Nome: {$tenant_data['nome']}\n";
            echo "  Database: {$tenant_data['mysql']['base']}\n";
            echo "  Host DB: {$tenant_data['mysql']['host']}\n";
            
            // Verificar configurações específicas MySQL 8.0
            if (isset($tenant_data['mysql']['charset'])) {
                echo "  Charset: {$tenant_data['mysql']['charset']}\n";
            } else {
                echo "  ⚠ Charset não configurado (padrão: utf8mb4)\n";
            }
        } else {
            echo "❌ Tenant não detectado\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Erro: " . $e->getMessage() . "\n";
    }
}

echo "\n\n2. VALIDANDO CONFIGURAÇÕES DE TENANT:\n";

foreach ($TENANTS_CONFIG as $tenant_key => $tenant_config) {
    echo "\nTenant: {$tenant_key}\n";
    
    // Verificar estrutura básica
    $required_fields = ['tenant', 'nome', 'host', 'mysql'];
    foreach ($required_fields as $field) {
        if (isset($tenant_config[$field])) {
            echo "  ✓ {$field}: configurado\n";
        } else {
            echo "  ❌ {$field}: FALTANDO\n";
        }
    }
    
    // Verificar configurações MySQL específicas
    if (isset($tenant_config['mysql'])) {
        $mysql_config = $tenant_config['mysql'];
        
        echo "  MySQL Configuration:\n";
        echo "    Host: " . ($mysql_config['host'] ?? 'NÃO DEFINIDO') . "\n";
        echo "    User: " . ($mysql_config['user'] ?? 'NÃO DEFINIDO') . "\n";
        echo "    Database: " . ($mysql_config['base'] ?? 'NÃO DEFINIDO') . "\n";
        echo "    Port: " . ($mysql_config['port'] ?? '3306 (padrão)') . "\n";
        
        // Verificações de segurança para MySQL 8.0
        if (isset($mysql_config['ssl'])) {
            echo "    SSL: ✓ Configurado\n";
        } else {
            echo "    SSL: ⚠ Não configurado (recomendado para MySQL 8.0)\n";
        }
        
        if (isset($mysql_config['auth_plugin'])) {
            echo "    Auth Plugin: {$mysql_config['auth_plugin']}\n";
        } else {
            echo "    Auth Plugin: ⚠ Padrão (caching_sha2_password)\n";
        }
        
        // Verificar senha base64
        if (isset($mysql_config['pass'])) {
            $decoded = base64_decode($mysql_config['pass']);
            if ($decoded !== false && base64_encode($decoded) === $mysql_config['pass']) {
                echo "    Password: ✓ Base64 válido\n";
            } else {
                echo "    Password: ❌ Base64 inválido\n";
            }
        } else {
            echo "    Password: ❌ NÃO DEFINIDO\n";
        }
    }
}

echo "\n\n3. TESTANDO CONEXÃO POR TENANT (apenas para desenvolvimento):\n";

// Apenas para tenant de desenvolvimento (evitar produção)
if (file_exists('/var/www/html/classes/local.php')) {
    echo "Ambiente de desenvolvimento detectado.\n";
    
    // Configurar ambiente Docker
    putenv('DB_HOST=lettore-db');
    putenv('DB_USERNAME=lettore_user');
    putenv('DB_PASSWORD=lettore_password');
    
    require_once '/var/www/html/classes/conecta.docker.class.php';
    
    $test_tenant = [
        'tenant' => 'test_docker',
        'mysql' => [
            'host' => 'lettore-db',
            'port' => 3306,
            'user' => 'lettore_user',
            'pass' => base64_encode('lettore_password'),
            'base' => 'lettore_dev'
        ]
    ];
    
    // Simular $TENANT global
    $TENANT = $test_tenant;
    
    try {
        $conecta = new conecta();
        $result = $conecta->query("SELECT 'MySQL 8.0 + Multi-tenant OK' as status");
        
        if ($result) {
            $row = $conecta->fetch_assoc($result);
            echo "✓ Teste de conexão multi-tenant: {$row['status']}\n";
        }
    } catch (Exception $e) {
        echo "❌ Erro na conexão multi-tenant: " . $e->getMessage() . "\n";
    }
} else {
    echo "Ambiente de produção - pular teste de conexão.\n";
}

echo "\n\n4. RECOMENDAÇÕES PARA MySQL 8.0:\n";
$recommendations = [
    "Adicionar configuração 'charset' => 'utf8mb4' para todos os tenants",
    "Configurar 'collation' => 'utf8mb4_unicode_ci' para melhor suporte Unicode",
    "Considerar SSL para conexões de produção",
    "Validar auth_plugin compatibilidade (caching_sha2_password vs mysql_native_password)",
    "Configurar timeout específico para conexões",
    "Implementar connection pooling para alta concorrência",
    "Monitorar sql_mode para compatibilidade com queries existentes",
    "Backup das configurações antes de mudanças em produção"
];

foreach ($recommendations as $i => $rec) {
    echo ($i + 1) . ". {$rec}\n";
}

echo "\n=== VALIDAÇÃO CONCLUÍDA ===\n";
?>