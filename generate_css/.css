.bg-pc { background-color:  !important; }
.text-pc { color:  !important; }

.bg-sc { background-color:  !important; }
.text-sc { color:  !important; }

.text-base { color:  !important; }

/* Dashboard */
/* ********************************************************* */

    /* Tela */
    .dashboard-content .main-header {  background: ;  }
    .dashboard-content .btn-access-ebooks ul li:hover { background-color: ; } 
    .line-1 h2 .fa {  color:  }

    /* Abas */
    .dashboard-content .dashboard-abas > .nav { background-color:  }

    /* Loader */
    .dashboard-content .sk-circle .sk-child:before { background-color: ; } 

    /* Scrollbar */
    .dashboard-content .line-1 .ps-container > .ps-scrollbar-y-rail > .ps-scrollbar-y { background-color: ; opacity: 0.7;}

    /* Modal */
    .dashboard-content .be-modal h3 { color: ; }
    .dashboard-content .be-modal .info-resume { border-left-color: ;}
    .dashboard-content .be-modal .about-boxe-info h4 { color: ;}
    .dashboard-content .be-modal .btn-primary { background: ; border-bottom-color: ; }
    .dashboard-content  .be-modal .btn-primary:hover { background: ;}

    .dashboard-content span.checkbox.on { background-color:  } 


/* Login */
/* ********************************************************* */

    .login-content .jb-login-btn { background-color: ; }
    .login-content .jb-login-btn:hover { background-color: ; }

    .login-content.jb-login-bg { background: ; height: 100%; }

     .requisitos-content .center-line { border-bottom-color:;}
     .requisitos-content .center-line span { 
        background: ; 
    }
    .requisitos-content, .requisitos-content a, .requisitos-content h3, .requisitos-content h4, .requisitos-content p {
        color: ;
    }

    img.inverted { -webkit-filter: invert(100%); }


