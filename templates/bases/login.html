{% extends 'bases/base.html' %}

{% block page_title %} {{tenant.nome}} | Entrar {% endblock %}

{% block css %}
    {{ parent() }}
    <link type="text/css" rel="stylesheet" media="screen" href="{{media_dir}}/css/login.css?v={{version}}" />
{% endblock %}

{% block css_tenants %}{% endblock %}  

{% block javascript %}
    <script language="javascript" type="text/javascript" src="{{media_dir}}/js/login.js?v={{version}}"></script>    
{% endblock %}

{% block header_outer %}{% endblock %}

{% block body_class %}jb-login-bg login-content{% endblock %}

{% block main_outer %}
    <div class="container">

        <form class="jb-form-signin" name="login-form" method="post" action="/login/autenticar">

            <input type="hidden" name="action" value="autenticar" />

            <div class="jb-login-box text-center">

                <h3 class="text-pc"> Ambiente de edição </h3>

                <figure class="align-center"><img style="    width: 240px;" src="{{media_dir}}/img/logo-login.png"></figure>


                <div class="jb-error-yellow login-alerta hide">
                        <b>Estamos em manutenção!</b> Tente se conectar novamente mais tarde.
                </div>
                
                <input type="text" value="{{ COOKIE.loginemail|default('') }}" class="jb-login-email form-control" name="login-email" placeholder="Login">
                <input type="password" class="jb-login-password form-control" name="login-senha" placeholder="Senha">
                
                <button class="btn btn-lg btn-block jb-login-btn" data-loading-text='<span class="glyphicon glyphicon-refresh glyphicon-refresh-animate"></span> Entrando...' type="submit" name="login-submit">
                    Acessar o editor
                </button>                    

                <span class="clearfix"></span>
            </div>
            <p class="jb-software-version">
                {{tenant.nome}} v. {{ version }}
            </p>
        </form>
    </div>
 {% endblock %}
