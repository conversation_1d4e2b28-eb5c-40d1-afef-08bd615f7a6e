{% extends 'bases/base.html' %}


{% block javascript %}
    
    <!--
    <script src="{{media_dir}}/js/obra_ajustar.js?v={{version}}" type="text/javascript"></script>
    <script src="{{media_dir}}/js/obra_grifos.js?v={{version}}" type="text/javascript"></script>
    -->

    <script src="{{media_dir}}/js/ajustar.js?v={{version}}" type="text/javascript"></script>
{% endblock %}


{% block body_class %}dashboard-content grey{% endblock %}

{% block main_outer %}
    
    <input type="hidden" name="idobra" value="{{obra.id}}">
    <input type="hidden" name="paginaum" value="{{obra.paginaum}}">
    <input type="hidden" name="qtdpaginas" value="{{obra.qtdpaginas}}">

    <div class="controles">
    <div class="controles-inside">

        <div class="detalhes">
            {{obra.titulo}}<br/><br/>
            <small class="lighter">Autor: {{obra.autor}}</small> <br/>
            <small class="lighter">Ano: {{obra.ano}}</small> <br/>
            <small class="lighter">Edição: {{obra.edicao}}</small> <br/><br/>
	    <img src="/uploads/{{tenant.tenant}}/{{obra.id}}/capa.jpg"/>
	    <!--           <img src="data:image/png;base64,{{obra.capa|base64_encode}}" width="100px" />-->
        </div>

        <div class="form-inline">
              <button class="btn btn-cinza btn-sm pagina-anterior">&lt;&lt;</button>
              <input style="width:70px" type="text" name="pagina" class="form-control input-sm text-center" value="{{obra.paginaum}}">
              <button class="btn btn-cinza btn-sm pagina-proxima">&gt;&gt;</button>
        </div>  

        <hr/>

        Marcações <br/><br/>

            <label class="btn-marcacao btn btn-xs btn-success" data-color="#060" data-class="palavra" data-tipo="palavra">
                <input type="radio" name="options" id="option3" autocomplete="off" class="hide"> Palavra
            </label>

            <label class="btn-marcacao btn btn-xs btn-danger " data-class="nivel nivel1" data-tipo="sumario" data-nivel="1">
              <input type="radio" name="options" id="option1" autocomplete="off"  class="hide"> Sumário
            </label>

            <label class="btn-marcacao btn btn-xs btn-warning" data-color="#060" data-class="marcacao" data-tipo="marcacao">
                <input type="radio" name="options" id="option3" autocomplete="off" class="hide"> Outros
            </label>

        <hr/>

        <button class="btn-default btn btn-sm btn-cancelar-pagina">Desfazer</button>
        <button class="btn-success btn btn-sm btn-salvar-pagina disabled" data-alterado-text="Salvar Alterações">Salvar Alterações</button>

    </div>
    </div>


    <div class="pagina-obra ocultar-marcacao ocultar-palavra ocultar-sumario"></div>

    
    </script>


{% endblock %}

