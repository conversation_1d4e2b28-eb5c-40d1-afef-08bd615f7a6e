.bg-pc { background-color: {{tenant.style.primary_color}} !important; }
.text-pc { color: {{tenant.style.primary_color}} !important; }

.bg-sc { background-color: {{tenant.style.secondary_color}} !important; }
.text-sc { color: {{tenant.style.secondary_color}} !important; }

.text-base { color: {{tenant.style.text_color}} !important; }

/* Dashboard */
/* ********************************************************* */

    /* Tela */
    .dashboard-content .main-header {  background: {{tenant.style.primary_color}};  }
    .dashboard-content .btn-access-ebooks ul li:hover { background-color: {{tenant.style.secondary_color}}; } 
    .line-1 h2 .fa {  color: {{tenant.style.primary_color}} }

    /* Abas */
    .dashboard-content .dashboard-abas > .nav { background-color: {{tenant.style.primary_color}} }

    /* Loader */
    .dashboard-content .sk-circle .sk-child:before { background-color: {{tenant.style.primary_color}}; } 

    /* Scrollbar */
    .dashboard-content .line-1 .ps-container > .ps-scrollbar-y-rail > .ps-scrollbar-y { background-color: {{tenant.style.primary_color}}; opacity: 0.7;}

    /* Modal */
    .dashboard-content .be-modal h3 { color: {{tenant.style.primary_color}}; }
    .dashboard-content .be-modal .info-resume { border-left-color: {{tenant.style.primary_color}};}
    .dashboard-content .be-modal .about-boxe-info h4 { color: {{tenant.style.primary_color}};}
    .dashboard-content .be-modal .btn-primary { background: {{tenant.style.primary_color}}; border-bottom-color: {{tenant.style.secondary_color}}; }
    .dashboard-content  .be-modal .btn-primary:hover { background: {{tenant.style.secondary_color}};}

    .dashboard-content span.checkbox.on { background-color: {{tenant.style.secondary_color}} } 


/* Login */
/* ********************************************************* */

    .login-content .jb-login-btn { background-color: {{tenant.style.primary_color}}; }
    .login-content .jb-login-btn:hover { background-color: {{tenant.style.secondary_color}}; }

    .login-content.jb-login-bg { background: {{tenant.style.primary_color}}; height: 100%; }

     .requisitos-content .center-line { border-bottom-color:{{tenant.style.text_color}};}
     .requisitos-content .center-line span { 
        background: {{tenant.style.primary_color}}; 
    }
    .requisitos-content, .requisitos-content a, .requisitos-content h3, .requisitos-content h4, .requisitos-content p {
        color: {{tenant.style.text_color}};
    }

{% if tenant.style.text_color != '#FFFFFF' %}
    img.inverted { -webkit-filter: invert(100%); }
{% endif %}


