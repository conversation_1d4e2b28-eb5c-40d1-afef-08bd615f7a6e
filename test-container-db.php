<?php
/**
 * Teste de conexão com MySQL no container Docker
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== TESTE CONEXÃO MySQL CONTAINER ===\n\n";

// Configurações do container
$configs = [
    'docker_mysql' => [
        'host' => 'lettore-db',  // Nome do serviço no docker-compose
        'port' => 3306,
        'user' => 'lettore_user',
        'pass' => 'lettore_password',  // Conforme docker-compose.yml
        'db' => 'information_schema'
    ],
    'localhost_mysql' => [
        'host' => '127.0.0.1',  
        'port' => 3306,
        'user' => 'lettore_user',
        'pass' => 'lettore_password',
        'db' => 'information_schema'
    ]
];

foreach ($configs as $name => $config) {
    echo "Testando: {$name}\n";
    echo "Host: {$config['host']}:{$config['port']}\n";
    echo "User: {$config['user']}\n";
    echo "Database: {$config['db']}\n\n";
    
    try {
        $mysqli = new mysqli(
            $config['host'],
            $config['user'],
            $config['pass'], 
            $config['db'],
            $config['port']
        );
        
        if ($mysqli->connect_error) {
            echo "❌ Erro: " . $mysqli->connect_error . "\n\n";
            continue;
        }
        
        echo "✓ Conexão estabelecida!\n";
        
        // Testar versão
        $result = $mysqli->query("SELECT VERSION() as version");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "MySQL Version: " . $row['version'] . "\n";
        }
        
        // Testar charset
        $result = $mysqli->query("SELECT @@character_set_server as charset");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "Default Charset: " . $row['charset'] . "\n";
        }
        
        // Testar auth plugin
        $result = $mysqli->query("SHOW VARIABLES LIKE 'default_authentication_plugin'");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "Auth Plugin: " . $row['Value'] . "\n";
        }
        
        // Testar databases disponíveis
        $result = $mysqli->query("SHOW DATABASES");
        if ($result) {
            echo "Databases disponíveis:\n";
            while ($row = $result->fetch_assoc()) {
                echo "  - " . $row['Database'] . "\n";
            }
        }
        
        $mysqli->close();
        echo "\n";
        
    } catch (Exception $e) {
        echo "❌ Exceção: " . $e->getMessage() . "\n\n";
    }
}
?>