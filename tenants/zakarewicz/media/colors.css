
/* Dashboard */
/* ********************************************************* */

    /* Tela */
    .dashboard-content .main-header {  background: #ce171f;  }
    .dashboard-content .btn-access-ebooks ul li:hover { background-color: #a40f15; }
    .line-1 h2 .fa {  color: #ce171f }

    /* Icon home das abas de obras abertas */
    .dashboard-content .dashboard-abas > .nav-tabs > li > a.exibir_ambiente { background-color: #ce171f;  }
    .dashboard-content .dashboard-abas > .nav-tabs > li > a.exibir_ambiente:hover { background-color: #a40f15;  }

    /* Highlight da busca */
    .dashboard-content .search-box-result em,
    .dashboard-content .list-empty em  { color: #ce171f; }

    /* Abas */
    .dashboard-content .dashboard-abas > .nav { background-color: #ce171f }

    /* Loader */
    .dashboard-content .sk-circle .sk-child:before { background-color: #ce171f; }

    /* Scrollbar */
    .dashboard-content .line-1 .ps-container > .ps-scrollbar-y-rail > .ps-scrollbar-y { background-color: #ce171f; opacity: 0.7;}

    /* Modal */
    .dashboard-content .be-modal h3 { color: #ce171f; }
    .dashboard-content .be-modal .info-resume { border-left-color: #ce171f;}
    .dashboard-content .be-modal .about-boxe-info h4 { color: #ce171f;}
    .dashboard-content .be-modal .btn-primary { background: #ce171f; border-bottom-color: #a40f15; }
    .dashboard-content  .be-modal .btn-primary:hover { background: #a40f15;}


/* Ambiente de Leitura */
/* ********************************************************* */

    /* Página marcada e flag icon em cima da página */
    .read-content .mark-page.active a, .read-content .mark-page.active a { color: #ce171f; }
    .read-content .page-mark-active i { color: #ce171f; }

    /* Item realcado no sumário */
    .read-content .category-summary div.active > span { color: #ce171f;  }


/* Login */
/* ********************************************************* */

    .login-content .jb-login-btn { background-color: #ce171f; }
    .login-content .jb-login-btn:hover { background-color: #a40f15; }

    .login-content.jb-login-bg { background: #ce171f; height: 100%; }
