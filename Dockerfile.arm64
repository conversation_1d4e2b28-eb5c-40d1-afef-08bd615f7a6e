FROM php:8.2-cli

# Argumentos para multi-arquitetura
ARG TARGETPLATFORM
ARG BUILDPLATFORM

# Instalar dependências do sistema para ARM64
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libzip-dev \
    zip \
    unzip \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libmcrypt-dev \
    libgd-dev \
    libcurl4-openssl-dev \
    pkg-config \
    libssl-dev \
    autoconf \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Instalar extensões PHP
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
        pdo_mysql \
        mbstring \
        exif \
        pcntl \
        bcmath \
        gd \
        zip \
        mysqli

# Instalar Swoole para ARM64
# Usar versão específica que funciona bem com ARM64
RUN pecl install swoole-5.0.3 \
    && docker-php-ext-enable swoole

# Instalar Redis
RUN pecl install redis \
    && docker-php-ext-enable redis

# Instalar Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Configurar diretório de trabalho
WORKDIR /var/www/html

# Copiar arquivos da aplicação
COPY . .

# Instalar dependências do Composer
RUN composer install --no-dev --optimize-autoloader

# Criar diretórios necessários
RUN mkdir -p /var/www/html/obras_montadas \
    /var/www/html/indices_montados \
    /var/www/html/capas_temp \
    /var/www/html/downloads \
    /var/www/html/logs \
    && chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html

# Copiar configurações
COPY docker/php.ini /usr/local/etc/php/php.ini
COPY docker/swoole-server.php /var/www/html/swoole-server.php
COPY docker/start.sh /usr/local/bin/start.sh
RUN chmod +x /usr/local/bin/start.sh

# Expor porta
EXPOSE 9501

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:9501/health || exit 1

# Comando de inicialização
CMD ["/usr/local/bin/start.sh"]
