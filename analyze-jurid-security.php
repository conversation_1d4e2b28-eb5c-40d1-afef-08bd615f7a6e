<?php
/**
 * Análise de Segurança - Tenant Jurid
 * Foco: Tabela usuarios e criptografia de senhas
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== ANÁLISE DE SEGURANÇA - TENANT JURID ===\n\n";

// Configurar ambiente para tenant jurid
putenv('DB_HOST=lettore-db');
putenv('DB_PORT=3306');
putenv('DB_USERNAME=lettore_user');
putenv('DB_PASSWORD=lettore_password');
putenv('DB_NAME=lettore_dev');

require_once '/var/www/html/classes/conecta.docker.class.php';

echo "1. CONEXÃO COM TENANT JURID:\n";
echo "Host: lettore-db:3306\n";
echo "Database: lettore_dev\n";
echo "Tenant: jurid\n\n";

try {
    // Usar database lettore_dev (conforme docker-compose)
    $conecta = new conecta();
    echo "✓ Conexão estabelecida com sucesso\n\n";
    
    echo "2. ANALISANDO ESTRUTURA DA TABELA USUARIOS:\n";
    
    // Verificar se tabela usuarios existe
    $tables = $conecta->query("SHOW TABLES LIKE 'usuarios'");
    if ($conecta->num_rows($tables) > 0) {
        echo "✓ Tabela 'usuarios' encontrada\n";
        
        // Obter estrutura da tabela
        $structure = $conecta->query("DESCRIBE usuarios");
        echo "\nEstrutura da tabela usuarios:\n";
        echo "Campo                | Tipo                 | Null | Key | Default | Extra\n";
        echo "-------------------- | -------------------- | ---- | --- | ------- | -----\n";
        
        while ($row = $conecta->fetch_assoc($structure)) {
            printf("%-20s | %-20s | %-4s | %-3s | %-7s | %s\n", 
                $row['Field'], 
                $row['Type'], 
                $row['Null'], 
                $row['Key'], 
                $row['Default'] ?: 'NULL', 
                $row['Extra']
            );
        }
        
        echo "\n3. ANALISANDO DADOS DE USUÁRIOS (SEGURANÇA):\n";
        
        // Contar total de usuários
        $count_result = $conecta->query("SELECT COUNT(*) as total FROM usuarios");
        $count_row = $conecta->fetch_assoc($count_result);
        echo "Total de usuários: " . $count_row['total'] . "\n";
        
        if ($count_row['total'] > 0) {
            // Analisar tipos de hash/criptografia usados
            $sample_query = $conecta->query("
                SELECT 
                    id,
                    usuario as login,
                    LENGTH(senha) as senha_length,
                    CASE 
                        WHEN senha LIKE '$2y$%' THEN 'bcrypt'
                        WHEN senha LIKE '$2a$%' THEN 'bcrypt_old'
                        WHEN senha LIKE '$1$%' THEN 'md5_crypt'
                        WHEN senha LIKE '$6$%' THEN 'sha512_crypt'
                        WHEN LENGTH(senha) = 32 AND senha REGEXP '^[a-f0-9]+$' THEN 'md5_hex'
                        WHEN LENGTH(senha) = 40 AND senha REGEXP '^[a-f0-9]+$' THEN 'sha1_hex'
                        WHEN LENGTH(senha) = 64 AND senha REGEXP '^[a-f0-9]+$' THEN 'sha256_hex'
                        WHEN senha REGEXP '^[A-Za-z0-9+/=]+$' AND LENGTH(senha) % 4 = 0 THEN 'base64_possible'
                        ELSE 'plaintext_or_custom'
                    END as crypto_type,
                    senha
                FROM usuarios 
                ORDER BY id 
                LIMIT 10
            ");
            
            echo "\nAmostra de análise de senhas (primeiros 10 usuários):\n";
            echo "ID | Login           | Tamanho | Tipo Detectado      | Amostra da Senha\n";
            echo "---|-----------------|---------|---------------------|------------------\n";
            
            $crypto_stats = [];
            while ($row = $conecta->fetch_assoc($sample_query)) {
                $crypto_type = $row['crypto_type'];
                $crypto_stats[$crypto_type] = ($crypto_stats[$crypto_type] ?? 0) + 1;
                
                // Mascarar senha para apresentação (mostrar apenas alguns caracteres)
                $senha_sample = strlen($row['senha']) > 20 
                    ? substr($row['senha'], 0, 20) . '...' 
                    : $row['senha'];
                
                printf("%-2s | %-15s | %-7s | %-19s | %s\n", 
                    $row['id'],
                    substr($row['login'], 0, 15),
                    $row['senha_length'],
                    $crypto_type,
                    $senha_sample
                );
            }
            
            echo "\n4. ESTATÍSTICAS DE CRIPTOGRAFIA:\n";
            foreach ($crypto_stats as $type => $count) {
                $percentage = round(($count / array_sum($crypto_stats)) * 100, 1);
                echo "  {$type}: {$count} usuários ({$percentage}%)\n";
            }
            
            echo "\n5. AVALIAÇÃO DE SEGURANÇA:\n";
            
            $security_issues = [];
            $secure_methods = ['bcrypt', 'bcrypt_old', 'sha512_crypt'];
            $weak_methods = ['md5_hex', 'sha1_hex', 'plaintext_or_custom', 'base64_possible'];
            
            foreach ($crypto_stats as $type => $count) {
                if (in_array($type, $weak_methods)) {
                    $security_issues[] = "❌ CRÍTICO: {$count} senhas usando '{$type}' (inseguro)";
                } elseif (in_array($type, $secure_methods)) {
                    echo "✓ SEGURO: {$count} senhas usando '{$type}'\n";
                } else {
                    $security_issues[] = "⚠ DESCONHECIDO: {$count} senhas usando '{$type}' (verificar)";
                }
            }
            
            if (!empty($security_issues)) {
                echo "\nPROBLEMAS DE SEGURANÇA ENCONTRADOS:\n";
                foreach ($security_issues as $issue) {
                    echo $issue . "\n";
                }
                
                echo "\n6. QUERIES PARA DEMONSTRAÇÃO (APENAS SENHAS INSEGURAS):\n";
                
                // Query para senhas em texto plano ou facilmente descriptografáveis
                echo "\n-- Query para identificar senhas potencialmente em texto plano:\n";
                echo "SELECT id, usuario as login, senha, 'POSSÍVEL TEXTO PLANO' as alerta\n";
                echo "FROM usuarios \n";
                echo "WHERE LENGTH(senha) < 20 \n";
                echo "  AND senha NOT LIKE '$%' \n";
                echo "  AND senha NOT REGEXP '^[a-f0-9]{32,}$'\n";
                echo "ORDER BY id;\n";
                
                echo "\n-- Query para senhas MD5 (32 caracteres hex):\n"; 
                echo "SELECT id, usuario as login, senha, 'MD5 HASH - QUEBRADO' as alerta\n";
                echo "FROM usuarios \n";
                echo "WHERE LENGTH(senha) = 32 \n";
                echo "  AND senha REGEXP '^[a-f0-9]+$'\n";
                echo "ORDER BY id;\n";
                
                echo "\n-- Query para possível Base64 (reversível):\n";
                echo "SELECT id, usuario as login, senha, \n";
                echo "       FROM_BASE64(senha) as senha_decodificada,\n";
                echo "       'BASE64 - REVERSÍVEL' as alerta\n";
                echo "FROM usuarios \n";
                echo "WHERE senha REGEXP '^[A-Za-z0-9+/=]+$' \n";
                echo "  AND LENGTH(senha) % 4 = 0\n";
                echo "  AND LENGTH(senha) > 8\n";
                echo "  AND FROM_BASE64(senha) IS NOT NULL\n";
                echo "ORDER BY id;\n";
                
                // Executar query de demonstração se houver senhas inseguras
                if (isset($crypto_stats['plaintext_or_custom']) || 
                    isset($crypto_stats['base64_possible']) || 
                    isset($crypto_stats['md5_hex'])) {
                    
                    echo "\n7. DEMONSTRAÇÃO - SENHAS VULNERÁVEIS ENCONTRADAS:\n";
                    echo "⚠ EXECUTANDO QUERIES DE DEMONSTRAÇÃO PARA RELATÓRIO...\n\n";
                    
                    // Texto plano
                    $plain_check = $conecta->query("
                        SELECT id, usuario as login, senha, 'TEXTO PLANO' as tipo_vulnerabilidade
                        FROM usuarios 
                        WHERE LENGTH(senha) < 20 
                          AND senha NOT LIKE '$%' 
                          AND senha NOT REGEXP '^[a-f0-9]{32,}$'
                        LIMIT 5
                    ");
                    
                    if ($conecta->num_rows($plain_check) > 0) {
                        echo "SENHAS EM TEXTO PLANO ENCONTRADAS:\n";
                        while ($row = $conecta->fetch_assoc($plain_check)) {
                            echo "- ID {$row['id']} ({$row['login']}): '{$row['senha']}'\n";
                        }
                        echo "\n";
                    }
                    
                    // Base64
                    $b64_check = $conecta->query("
                        SELECT id, usuario as login, senha, FROM_BASE64(senha) as decodificada
                        FROM usuarios 
                        WHERE senha REGEXP '^[A-Za-z0-9+/=]+$' 
                          AND LENGTH(senha) % 4 = 0
                          AND LENGTH(senha) > 8
                          AND FROM_BASE64(senha) IS NOT NULL
                        LIMIT 5
                    ");
                    
                    if ($conecta->num_rows($b64_check) > 0) {
                        echo "SENHAS BASE64 (REVERSÍVEIS) ENCONTRADAS:\n";
                        while ($row = $conecta->fetch_assoc($b64_check)) {
                            echo "- ID {$row['id']} ({$row['login']}): '{$row['decodificada']}'\n";
                        }
                        echo "\n";
                    }
                }
            } else {
                echo "\n✅ RESULTADO: Todas as senhas parecem estar usando métodos seguros!\n";
            }
            
        } else {
            echo "⚠ Nenhum usuário encontrado na tabela\n";
        }
        
    } else {
        echo "❌ Tabela 'usuarios' não encontrada\n";
        
        // Verificar outras tabelas relacionadas
        echo "\nProcurando outras tabelas relacionadas a usuários:\n";
        $all_tables = $conecta->query("SHOW TABLES");
        while ($table = $conecta->fetch_assoc($all_tables)) {
            $table_name = current($table);
            if (stripos($table_name, 'user') !== false || 
                stripos($table_name, 'login') !== false ||
                stripos($table_name, 'auth') !== false) {
                echo "- Encontrada: {$table_name}\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ ERRO: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== ANÁLISE COMPLETA ===\n";
echo "💡 Use os resultados acima para sua apresentação à equipe de segurança.\n";
echo "⚠ IMPORTANTE: Remova este arquivo após a análise por segurança!\n";
?>