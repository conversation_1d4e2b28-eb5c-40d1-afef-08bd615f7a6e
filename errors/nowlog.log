25/08/2025 22:13 (ET: 0.13592) api.lettore.com.br/autores.listar2 ####### # CURL INFO ------  ####### {"url":"http:\/\/api.lettore.com.br\/autores.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":301,"header_size":295,"request_size":183,"filetime":-1,"ssl_verify_result":0,"redirect_count":0,"total_time":0.135596,"namelookup_time":0.075976,"connect_time":0.091396,"pretransfer_time":0.091599,"size_upload":0,"size_download":250,"speed_download":1843,"speed_upload":0,"download_content_length":250,"upload_content_length":0,"starttransfer_time":0.135523,"redirect_time":0,"redirect_url":"https:\/\/api.lettore.com.br\/autores.listar2","primary_ip":"************","certinfo":[],"primary_port":80,"local_ip":"**********","local_port":42472,"http_version":2,"protocol":1,"ssl_verifyresult":0,"scheme":"http","appconnect_time_us":0,"connect_time_us":91396,"namelookup_time_us":75976,"pretransfer_time_us":91599,"redirect_time_us":0,"starttransfer_time_us":135523,"total_time_us":135596,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>301 Moved Permanently</title>
</head><body>
<h1>Moved Permanently</h1>
<p>The document has moved <a href="https://api.lettore.com.br/autores.listar2">here</a>.</p>
</body></html>

25/08/2025 22:13 (ET: 0.07051) api.lettore.com.br/categorias.listar2 ####### # CURL INFO ------  ####### {"url":"http:\/\/api.lettore.com.br\/categorias.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":301,"header_size":298,"request_size":186,"filetime":-1,"ssl_verify_result":0,"redirect_count":0,"total_time":0.070265,"namelookup_time":0.030872,"connect_time":0.04359,"pretransfer_time":0.043826,"size_upload":0,"size_download":253,"speed_download":3600,"speed_upload":0,"download_content_length":253,"upload_content_length":0,"starttransfer_time":0.070135,"redirect_time":0,"redirect_url":"https:\/\/api.lettore.com.br\/categorias.listar2","primary_ip":"************","certinfo":[],"primary_port":80,"local_ip":"**********","local_port":42480,"http_version":2,"protocol":1,"ssl_verifyresult":0,"scheme":"http","appconnect_time_us":0,"connect_time_us":43590,"namelookup_time_us":30872,"pretransfer_time_us":43826,"redirect_time_us":0,"starttransfer_time_us":70135,"total_time_us":70265,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>301 Moved Permanently</title>
</head><body>
<h1>Moved Permanently</h1>
<p>The document has moved <a href="https://api.lettore.com.br/categorias.listar2">here</a>.</p>
</body></html>

25/08/2025 22:14 (ET: 0.03951) api.lettore.com.br/autores.listar2 ####### # CURL INFO ------  ####### {"url":"http:\/\/api.lettore.com.br\/autores.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":301,"header_size":295,"request_size":183,"filetime":-1,"ssl_verify_result":0,"redirect_count":0,"total_time":0.039166,"namelookup_time":0.008474,"connect_time":0.024382,"pretransfer_time":0.025381,"size_upload":0,"size_download":250,"speed_download":6383,"speed_upload":0,"download_content_length":250,"upload_content_length":0,"starttransfer_time":0.039103,"redirect_time":0,"redirect_url":"https:\/\/api.lettore.com.br\/autores.listar2","primary_ip":"************","certinfo":[],"primary_port":80,"local_ip":"**********","local_port":39134,"http_version":2,"protocol":1,"ssl_verifyresult":0,"scheme":"http","appconnect_time_us":0,"connect_time_us":24382,"namelookup_time_us":8474,"pretransfer_time_us":25381,"redirect_time_us":0,"starttransfer_time_us":39103,"total_time_us":39166,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>301 Moved Permanently</title>
</head><body>
<h1>Moved Permanently</h1>
<p>The document has moved <a href="https://api.lettore.com.br/autores.listar2">here</a>.</p>
</body></html>

25/08/2025 22:14 (ET: 0.03977) api.lettore.com.br/categorias.listar2 ####### # CURL INFO ------  ####### {"url":"http:\/\/api.lettore.com.br\/categorias.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":301,"header_size":298,"request_size":186,"filetime":-1,"ssl_verify_result":0,"redirect_count":0,"total_time":0.039592,"namelookup_time":0.009202,"connect_time":0.024059,"pretransfer_time":0.024498,"size_upload":0,"size_download":253,"speed_download":6390,"speed_upload":0,"download_content_length":253,"upload_content_length":0,"starttransfer_time":0.039503,"redirect_time":0,"redirect_url":"https:\/\/api.lettore.com.br\/categorias.listar2","primary_ip":"************","certinfo":[],"primary_port":80,"local_ip":"**********","local_port":39144,"http_version":2,"protocol":1,"ssl_verifyresult":0,"scheme":"http","appconnect_time_us":0,"connect_time_us":24059,"namelookup_time_us":9202,"pretransfer_time_us":24498,"redirect_time_us":0,"starttransfer_time_us":39503,"total_time_us":39592,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>301 Moved Permanently</title>
</head><body>
<h1>Moved Permanently</h1>
<p>The document has moved <a href="https://api.lettore.com.br/categorias.listar2">here</a>.</p>
</body></html>

25/08/2025 22:16 (ET: 0.03651) api.lettore.com.br/autores.listar2 ####### # CURL INFO ------  ####### {"url":"http:\/\/api.lettore.com.br\/autores.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":301,"header_size":295,"request_size":183,"filetime":-1,"ssl_verify_result":0,"redirect_count":0,"total_time":0.036294,"namelookup_time":0.01046,"connect_time":0.024718,"pretransfer_time":0.025362,"size_upload":0,"size_download":250,"speed_download":6888,"speed_upload":0,"download_content_length":250,"upload_content_length":0,"starttransfer_time":0.036222,"redirect_time":0,"redirect_url":"https:\/\/api.lettore.com.br\/autores.listar2","primary_ip":"************","certinfo":[],"primary_port":80,"local_ip":"**********","local_port":44402,"http_version":2,"protocol":1,"ssl_verifyresult":0,"scheme":"http","appconnect_time_us":0,"connect_time_us":24718,"namelookup_time_us":10460,"pretransfer_time_us":25362,"redirect_time_us":0,"starttransfer_time_us":36222,"total_time_us":36294,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>301 Moved Permanently</title>
</head><body>
<h1>Moved Permanently</h1>
<p>The document has moved <a href="https://api.lettore.com.br/autores.listar2">here</a>.</p>
</body></html>

25/08/2025 22:16 (ET: 0.02925) api.lettore.com.br/categorias.listar2 ####### # CURL INFO ------  ####### {"url":"http:\/\/api.lettore.com.br\/categorias.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":301,"header_size":298,"request_size":186,"filetime":-1,"ssl_verify_result":0,"redirect_count":0,"total_time":0.029026,"namelookup_time":0.006229,"connect_time":0.016549,"pretransfer_time":0.016757,"size_upload":0,"size_download":253,"speed_download":8716,"speed_upload":0,"download_content_length":253,"upload_content_length":0,"starttransfer_time":0.028937,"redirect_time":0,"redirect_url":"https:\/\/api.lettore.com.br\/categorias.listar2","primary_ip":"************","certinfo":[],"primary_port":80,"local_ip":"**********","local_port":44410,"http_version":2,"protocol":1,"ssl_verifyresult":0,"scheme":"http","appconnect_time_us":0,"connect_time_us":16549,"namelookup_time_us":6229,"pretransfer_time_us":16757,"redirect_time_us":0,"starttransfer_time_us":28937,"total_time_us":29026,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>301 Moved Permanently</title>
</head><body>
<h1>Moved Permanently</h1>
<p>The document has moved <a href="https://api.lettore.com.br/categorias.listar2">here</a>.</p>
</body></html>

25/08/2025 22:17 (ET: 0.03375) api.lettore.com.br/autores.listar2 ####### # CURL INFO ------  ####### {"url":"http:\/\/api.lettore.com.br\/autores.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":301,"header_size":295,"request_size":183,"filetime":-1,"ssl_verify_result":0,"redirect_count":0,"total_time":0.033319,"namelookup_time":0.012904,"connect_time":0.02142,"pretransfer_time":0.021556,"size_upload":0,"size_download":250,"speed_download":7503,"speed_upload":0,"download_content_length":250,"upload_content_length":0,"starttransfer_time":0.033241,"redirect_time":0,"redirect_url":"https:\/\/api.lettore.com.br\/autores.listar2","primary_ip":"************","certinfo":[],"primary_port":80,"local_ip":"**********","local_port":37438,"http_version":2,"protocol":1,"ssl_verifyresult":0,"scheme":"http","appconnect_time_us":0,"connect_time_us":21420,"namelookup_time_us":12904,"pretransfer_time_us":21556,"redirect_time_us":0,"starttransfer_time_us":33241,"total_time_us":33319,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>301 Moved Permanently</title>
</head><body>
<h1>Moved Permanently</h1>
<p>The document has moved <a href="https://api.lettore.com.br/autores.listar2">here</a>.</p>
</body></html>

25/08/2025 22:17 (ET: 0.02723) api.lettore.com.br/categorias.listar2 ####### # CURL INFO ------  ####### {"url":"http:\/\/api.lettore.com.br\/categorias.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":301,"header_size":298,"request_size":186,"filetime":-1,"ssl_verify_result":0,"redirect_count":0,"total_time":0.026665,"namelookup_time":0.006876,"connect_time":0.01541,"pretransfer_time":0.015674,"size_upload":0,"size_download":253,"speed_download":9488,"speed_upload":0,"download_content_length":253,"upload_content_length":0,"starttransfer_time":0.026592,"redirect_time":0,"redirect_url":"https:\/\/api.lettore.com.br\/categorias.listar2","primary_ip":"************","certinfo":[],"primary_port":80,"local_ip":"**********","local_port":37450,"http_version":2,"protocol":1,"ssl_verifyresult":0,"scheme":"http","appconnect_time_us":0,"connect_time_us":15410,"namelookup_time_us":6876,"pretransfer_time_us":15674,"redirect_time_us":0,"starttransfer_time_us":26592,"total_time_us":26665,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>301 Moved Permanently</title>
</head><body>
<h1>Moved Permanently</h1>
<p>The document has moved <a href="https://api.lettore.com.br/categorias.listar2">here</a>.</p>
</body></html>

25/08/2025 22:19 (ET: 0.12231) https://api.lettore.com.br/autores.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/autores.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":502,"header_size":772,"request_size":429,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.12198,"namelookup_time":0.018531,"connect_time":0.037016,"pretransfer_time":0.094336,"size_upload":0,"size_download":410,"speed_download":3361,"speed_upload":0,"download_content_length":410,"upload_content_length":0,"starttransfer_time":0.121444,"redirect_time":0.079731,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":35116,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":94142,"connect_time_us":37016,"namelookup_time_us":18531,"pretransfer_time_us":94336,"redirect_time_us":79731,"starttransfer_time_us":121444,"total_time_us":121980,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>502 Proxy Error</title>
</head><body>
<h1>Proxy Error</h1>
<p>The proxy server received an invalid
response from an upstream server.<br />
The proxy server could not handle the request <em><a href="/autores.listar2">POST&nbsp;/autores.listar2</a></em>.<p>
Reason: <strong>Error reading from remote server</strong></p></p>
</body></html>

25/08/2025 22:19 (ET: 0.09511) https://api.lettore.com.br/categorias.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/categorias.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":502,"header_size":772,"request_size":435,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.094656,"namelookup_time":0.005944,"connect_time":0.02374,"pretransfer_time":0.069591,"size_upload":0,"size_download":416,"speed_download":4394,"speed_upload":0,"download_content_length":416,"upload_content_length":0,"starttransfer_time":0.094026,"redirect_time":0.055717,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":35134,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":69233,"connect_time_us":23740,"namelookup_time_us":5944,"pretransfer_time_us":69591,"redirect_time_us":55717,"starttransfer_time_us":94026,"total_time_us":94656,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>502 Proxy Error</title>
</head><body>
<h1>Proxy Error</h1>
<p>The proxy server received an invalid
response from an upstream server.<br />
The proxy server could not handle the request <em><a href="/categorias.listar2">POST&nbsp;/categorias.listar2</a></em>.<p>
Reason: <strong>Error reading from remote server</strong></p></p>
</body></html>

25/08/2025 22:20 (ET: 0.1034) https://api.lettore.com.br/autores.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/autores.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":502,"header_size":772,"request_size":429,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.103086,"namelookup_time":0.00693,"connect_time":0.026444,"pretransfer_time":0.078239,"size_upload":0,"size_download":410,"speed_download":3977,"speed_upload":0,"download_content_length":410,"upload_content_length":0,"starttransfer_time":0.102832,"redirect_time":0.063898,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":36050,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":77422,"connect_time_us":26444,"namelookup_time_us":6930,"pretransfer_time_us":78239,"redirect_time_us":63898,"starttransfer_time_us":102832,"total_time_us":103086,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>502 Proxy Error</title>
</head><body>
<h1>Proxy Error</h1>
<p>The proxy server received an invalid
response from an upstream server.<br />
The proxy server could not handle the request <em><a href="/autores.listar2">POST&nbsp;/autores.listar2</a></em>.<p>
Reason: <strong>Error reading from remote server</strong></p></p>
</body></html>

25/08/2025 22:20 (ET: 0.11593) https://api.lettore.com.br/categorias.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/categorias.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":502,"header_size":772,"request_size":435,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.1154,"namelookup_time":0.005523,"connect_time":0.028051,"pretransfer_time":0.08402,"size_upload":0,"size_download":416,"speed_download":3604,"speed_upload":0,"download_content_length":416,"upload_content_length":0,"starttransfer_time":0.115091,"redirect_time":0.059507,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":36076,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":83855,"connect_time_us":28051,"namelookup_time_us":5523,"pretransfer_time_us":84020,"redirect_time_us":59507,"starttransfer_time_us":115091,"total_time_us":115400,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>502 Proxy Error</title>
</head><body>
<h1>Proxy Error</h1>
<p>The proxy server received an invalid
response from an upstream server.<br />
The proxy server could not handle the request <em><a href="/categorias.listar2">POST&nbsp;/categorias.listar2</a></em>.<p>
Reason: <strong>Error reading from remote server</strong></p></p>
</body></html>

25/08/2025 22:22 (ET: 0.10695) https://api.lettore.com.br/autores.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/autores.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":502,"header_size":772,"request_size":429,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.106163,"namelookup_time":0.01067,"connect_time":0.032791,"pretransfer_time":0.079909,"size_upload":0,"size_download":410,"speed_download":3861,"speed_upload":0,"download_content_length":410,"upload_content_length":0,"starttransfer_time":0.104539,"redirect_time":0.066399,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":39762,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":79087,"connect_time_us":32791,"namelookup_time_us":10670,"pretransfer_time_us":79909,"redirect_time_us":66399,"starttransfer_time_us":104539,"total_time_us":106163,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>502 Proxy Error</title>
</head><body>
<h1>Proxy Error</h1>
<p>The proxy server received an invalid
response from an upstream server.<br />
The proxy server could not handle the request <em><a href="/autores.listar2">POST&nbsp;/autores.listar2</a></em>.<p>
Reason: <strong>Error reading from remote server</strong></p></p>
</body></html>

25/08/2025 22:22 (ET: 0.10833) https://api.lettore.com.br/categorias.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/categorias.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":502,"header_size":772,"request_size":435,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.107665,"namelookup_time":0.006435,"connect_time":0.026809,"pretransfer_time":0.086636,"size_upload":0,"size_download":416,"speed_download":3863,"speed_upload":0,"download_content_length":416,"upload_content_length":0,"starttransfer_time":0.107313,"redirect_time":0.057385,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":39780,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":86405,"connect_time_us":26809,"namelookup_time_us":6435,"pretransfer_time_us":86636,"redirect_time_us":57385,"starttransfer_time_us":107313,"total_time_us":107665,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>502 Proxy Error</title>
</head><body>
<h1>Proxy Error</h1>
<p>The proxy server received an invalid
response from an upstream server.<br />
The proxy server could not handle the request <em><a href="/categorias.listar2">POST&nbsp;/categorias.listar2</a></em>.<p>
Reason: <strong>Error reading from remote server</strong></p></p>
</body></html>

25/08/2025 22:24 (ET: 0.1351) https://api.lettore.com.br/autores.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/autores.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":502,"header_size":772,"request_size":429,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.134811,"namelookup_time":0.021553,"connect_time":0.045674,"pretransfer_time":0.100514,"size_upload":0,"size_download":410,"speed_download":3041,"speed_upload":0,"download_content_length":410,"upload_content_length":0,"starttransfer_time":0.134033,"redirect_time":0.088661,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":59588,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":100006,"connect_time_us":45674,"namelookup_time_us":21553,"pretransfer_time_us":100514,"redirect_time_us":88661,"starttransfer_time_us":134033,"total_time_us":134811,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>502 Proxy Error</title>
</head><body>
<h1>Proxy Error</h1>
<p>The proxy server received an invalid
response from an upstream server.<br />
The proxy server could not handle the request <em><a href="/autores.listar2">POST&nbsp;/autores.listar2</a></em>.<p>
Reason: <strong>Error reading from remote server</strong></p></p>
</body></html>

25/08/2025 22:24 (ET: 0.12909) https://api.lettore.com.br/categorias.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/categorias.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":502,"header_size":772,"request_size":435,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.128453,"namelookup_time":0.008255,"connect_time":0.030547,"pretransfer_time":0.099627,"size_upload":0,"size_download":416,"speed_download":3238,"speed_upload":0,"download_content_length":416,"upload_content_length":0,"starttransfer_time":0.127988,"redirect_time":0.066628,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":59594,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":99255,"connect_time_us":30547,"namelookup_time_us":8255,"pretransfer_time_us":99627,"redirect_time_us":66628,"starttransfer_time_us":127988,"total_time_us":128453,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>502 Proxy Error</title>
</head><body>
<h1>Proxy Error</h1>
<p>The proxy server received an invalid
response from an upstream server.<br />
The proxy server could not handle the request <em><a href="/categorias.listar2">POST&nbsp;/categorias.listar2</a></em>.<p>
Reason: <strong>Error reading from remote server</strong></p></p>
</body></html>

25/08/2025 22:26 (ET: 0.14085) https://api.lettore.com.br/autores.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/autores.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":502,"header_size":772,"request_size":429,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.140587,"namelookup_time":0.01371,"connect_time":0.04015,"pretransfer_time":0.111433,"size_upload":0,"size_download":410,"speed_download":2916,"speed_upload":0,"download_content_length":410,"upload_content_length":0,"starttransfer_time":0.14021,"redirect_time":0.076452,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":55086,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":111131,"connect_time_us":40150,"namelookup_time_us":13710,"pretransfer_time_us":111433,"redirect_time_us":76452,"starttransfer_time_us":140210,"total_time_us":140587,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>502 Proxy Error</title>
</head><body>
<h1>Proxy Error</h1>
<p>The proxy server received an invalid
response from an upstream server.<br />
The proxy server could not handle the request <em><a href="/autores.listar2">POST&nbsp;/autores.listar2</a></em>.<p>
Reason: <strong>Error reading from remote server</strong></p></p>
</body></html>

25/08/2025 22:26 (ET: 0.11007) https://api.lettore.com.br/categorias.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/categorias.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":502,"header_size":772,"request_size":435,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.109586,"namelookup_time":0.005199,"connect_time":0.027781,"pretransfer_time":0.085511,"size_upload":0,"size_download":416,"speed_download":3796,"speed_upload":0,"download_content_length":416,"upload_content_length":0,"starttransfer_time":0.109294,"redirect_time":0.075707,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":55112,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":85302,"connect_time_us":27781,"namelookup_time_us":5199,"pretransfer_time_us":85511,"redirect_time_us":75707,"starttransfer_time_us":109294,"total_time_us":109586,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>502 Proxy Error</title>
</head><body>
<h1>Proxy Error</h1>
<p>The proxy server received an invalid
response from an upstream server.<br />
The proxy server could not handle the request <em><a href="/categorias.listar2">POST&nbsp;/categorias.listar2</a></em>.<p>
Reason: <strong>Error reading from remote server</strong></p></p>
</body></html>

25/08/2025 22:29 (ET: 0.33606) https://api.lettore.com.br/autores.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/autores.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":502,"header_size":772,"request_size":429,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.335702,"namelookup_time":0.203978,"connect_time":0.225155,"pretransfer_time":0.308634,"size_upload":0,"size_download":410,"speed_download":1221,"speed_upload":0,"download_content_length":410,"upload_content_length":0,"starttransfer_time":0.335306,"redirect_time":0.284661,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":33296,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":308317,"connect_time_us":225155,"namelookup_time_us":203978,"pretransfer_time_us":308634,"redirect_time_us":284661,"starttransfer_time_us":335306,"total_time_us":335702,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>502 Proxy Error</title>
</head><body>
<h1>Proxy Error</h1>
<p>The proxy server received an invalid
response from an upstream server.<br />
The proxy server could not handle the request <em><a href="/autores.listar2">POST&nbsp;/autores.listar2</a></em>.<p>
Reason: <strong>Error reading from remote server</strong></p></p>
</body></html>

25/08/2025 22:29 (ET: 0.15417) https://api.lettore.com.br/categorias.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/categorias.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":502,"header_size":772,"request_size":435,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.153698,"namelookup_time":0.010435,"connect_time":0.059745,"pretransfer_time":0.129368,"size_upload":0,"size_download":416,"speed_download":2706,"speed_upload":0,"download_content_length":416,"upload_content_length":0,"starttransfer_time":0.153304,"redirect_time":0.0989,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":33312,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":129080,"connect_time_us":59745,"namelookup_time_us":10435,"pretransfer_time_us":129368,"redirect_time_us":98900,"starttransfer_time_us":153304,"total_time_us":153698,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>502 Proxy Error</title>
</head><body>
<h1>Proxy Error</h1>
<p>The proxy server received an invalid
response from an upstream server.<br />
The proxy server could not handle the request <em><a href="/categorias.listar2">POST&nbsp;/categorias.listar2</a></em>.<p>
Reason: <strong>Error reading from remote server</strong></p></p>
</body></html>

25/08/2025 22:34 (ET: 0.15032) https://api.lettore.com.br/autores.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/autores.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":502,"header_size":772,"request_size":429,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.149989,"namelookup_time":0.02447,"connect_time":0.068581,"pretransfer_time":0.126774,"size_upload":0,"size_download":410,"speed_download":2733,"speed_upload":0,"download_content_length":410,"upload_content_length":0,"starttransfer_time":0.149721,"redirect_time":0.105125,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":57228,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":126451,"connect_time_us":68581,"namelookup_time_us":24470,"pretransfer_time_us":126774,"redirect_time_us":105125,"starttransfer_time_us":149721,"total_time_us":149989,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>502 Proxy Error</title>
</head><body>
<h1>Proxy Error</h1>
<p>The proxy server received an invalid
response from an upstream server.<br />
The proxy server could not handle the request <em><a href="/autores.listar2">POST&nbsp;/autores.listar2</a></em>.<p>
Reason: <strong>Error reading from remote server</strong></p></p>
</body></html>

25/08/2025 22:34 (ET: 0.10435) https://api.lettore.com.br/categorias.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/categorias.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":502,"header_size":772,"request_size":435,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.103582,"namelookup_time":0.006778,"connect_time":0.026647,"pretransfer_time":0.081555,"size_upload":0,"size_download":416,"speed_download":4016,"speed_upload":0,"download_content_length":416,"upload_content_length":0,"starttransfer_time":0.103281,"redirect_time":0.064039,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":57246,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":81283,"connect_time_us":26647,"namelookup_time_us":6778,"pretransfer_time_us":81555,"redirect_time_us":64039,"starttransfer_time_us":103281,"total_time_us":103582,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>502 Proxy Error</title>
</head><body>
<h1>Proxy Error</h1>
<p>The proxy server received an invalid
response from an upstream server.<br />
The proxy server could not handle the request <em><a href="/categorias.listar2">POST&nbsp;/categorias.listar2</a></em>.<p>
Reason: <strong>Error reading from remote server</strong></p></p>
</body></html>

25/08/2025 22:35 (ET: 0.11615) https://api.lettore.com.br/autores.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/autores.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":502,"header_size":772,"request_size":429,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.115834,"namelookup_time":0.011732,"connect_time":0.038831,"pretransfer_time":0.092815,"size_upload":0,"size_download":410,"speed_download":3539,"speed_upload":0,"download_content_length":410,"upload_content_length":0,"starttransfer_time":0.113854,"redirect_time":0.073511,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":59774,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":92200,"connect_time_us":38831,"namelookup_time_us":11732,"pretransfer_time_us":92815,"redirect_time_us":73511,"starttransfer_time_us":113854,"total_time_us":115834,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>502 Proxy Error</title>
</head><body>
<h1>Proxy Error</h1>
<p>The proxy server received an invalid
response from an upstream server.<br />
The proxy server could not handle the request <em><a href="/autores.listar2">POST&nbsp;/autores.listar2</a></em>.<p>
Reason: <strong>Error reading from remote server</strong></p></p>
</body></html>

25/08/2025 22:35 (ET: 0.09783) https://api.lettore.com.br/categorias.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/categorias.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":502,"header_size":772,"request_size":435,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.096901,"namelookup_time":0.007234,"connect_time":0.027603,"pretransfer_time":0.073742,"size_upload":0,"size_download":416,"speed_download":4293,"speed_upload":0,"download_content_length":416,"upload_content_length":0,"starttransfer_time":0.09642,"redirect_time":0.0588,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":59788,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":73416,"connect_time_us":27603,"namelookup_time_us":7234,"pretransfer_time_us":73742,"redirect_time_us":58800,"starttransfer_time_us":96420,"total_time_us":96901,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>502 Proxy Error</title>
</head><body>
<h1>Proxy Error</h1>
<p>The proxy server received an invalid
response from an upstream server.<br />
The proxy server could not handle the request <em><a href="/categorias.listar2">POST&nbsp;/categorias.listar2</a></em>.<p>
Reason: <strong>Error reading from remote server</strong></p></p>
</body></html>

25/08/2025 22:39 (ET: 0.12946) https://api.lettore.com.br/autores.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/autores.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":502,"header_size":772,"request_size":429,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.128795,"namelookup_time":0.020525,"connect_time":0.040054,"pretransfer_time":0.101425,"size_upload":0,"size_download":410,"speed_download":3183,"speed_upload":0,"download_content_length":410,"upload_content_length":0,"starttransfer_time":0.128374,"redirect_time":0.078478,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":59452,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":100847,"connect_time_us":40054,"namelookup_time_us":20525,"pretransfer_time_us":101425,"redirect_time_us":78478,"starttransfer_time_us":128374,"total_time_us":128795,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>502 Proxy Error</title>
</head><body>
<h1>Proxy Error</h1>
<p>The proxy server received an invalid
response from an upstream server.<br />
The proxy server could not handle the request <em><a href="/autores.listar2">POST&nbsp;/autores.listar2</a></em>.<p>
Reason: <strong>Error reading from remote server</strong></p></p>
</body></html>

25/08/2025 22:39 (ET: 0.11432) https://api.lettore.com.br/categorias.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/categorias.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":502,"header_size":772,"request_size":435,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.113049,"namelookup_time":0.008712,"connect_time":0.030693,"pretransfer_time":0.091686,"size_upload":0,"size_download":416,"speed_download":3679,"speed_upload":0,"download_content_length":416,"upload_content_length":0,"starttransfer_time":0.112447,"redirect_time":0.068198,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":59464,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":90490,"connect_time_us":30693,"namelookup_time_us":8712,"pretransfer_time_us":91686,"redirect_time_us":68198,"starttransfer_time_us":112447,"total_time_us":113049,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>502 Proxy Error</title>
</head><body>
<h1>Proxy Error</h1>
<p>The proxy server received an invalid
response from an upstream server.<br />
The proxy server could not handle the request <em><a href="/categorias.listar2">POST&nbsp;/categorias.listar2</a></em>.<p>
Reason: <strong>Error reading from remote server</strong></p></p>
</body></html>

25/08/2025 22:43 (ET: 0.12324) https://api.lettore.com.br/autores.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/autores.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":502,"header_size":772,"request_size":429,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.122786,"namelookup_time":0.017462,"connect_time":0.041405,"pretransfer_time":0.101606,"size_upload":0,"size_download":410,"speed_download":3339,"speed_upload":0,"download_content_length":410,"upload_content_length":0,"starttransfer_time":0.122513,"redirect_time":0.0837,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":53768,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":101183,"connect_time_us":41405,"namelookup_time_us":17462,"pretransfer_time_us":101606,"redirect_time_us":83700,"starttransfer_time_us":122513,"total_time_us":122786,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>502 Proxy Error</title>
</head><body>
<h1>Proxy Error</h1>
<p>The proxy server received an invalid
response from an upstream server.<br />
The proxy server could not handle the request <em><a href="/autores.listar2">POST&nbsp;/autores.listar2</a></em>.<p>
Reason: <strong>Error reading from remote server</strong></p></p>
</body></html>

25/08/2025 22:43 (ET: 0.09792) https://api.lettore.com.br/categorias.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/categorias.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":502,"header_size":772,"request_size":435,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.096193,"namelookup_time":0.0061,"connect_time":0.025795,"pretransfer_time":0.073704,"size_upload":0,"size_download":416,"speed_download":4324,"speed_upload":0,"download_content_length":416,"upload_content_length":0,"starttransfer_time":0.095743,"redirect_time":0.057006,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":53788,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":73416,"connect_time_us":25795,"namelookup_time_us":6100,"pretransfer_time_us":73704,"redirect_time_us":57006,"starttransfer_time_us":95743,"total_time_us":96193,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>502 Proxy Error</title>
</head><body>
<h1>Proxy Error</h1>
<p>The proxy server received an invalid
response from an upstream server.<br />
The proxy server could not handle the request <em><a href="/categorias.listar2">POST&nbsp;/categorias.listar2</a></em>.<p>
Reason: <strong>Error reading from remote server</strong></p></p>
</body></html>

25/08/2025 22:44 (ET: 0.20373) https://api.lettore.com.br/autores.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/autores.listar2","content_type":"application\/json; charset=UTF-8","http_code":200,"header_size":1033,"request_size":421,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.203057,"namelookup_time":0.02609,"connect_time":0.046528,"pretransfer_time":0.113863,"size_upload":0,"size_download":10227,"speed_download":50365,"speed_upload":0,"download_content_length":10227,"upload_content_length":0,"starttransfer_time":0.202088,"redirect_time":0.100158,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":51244,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":113650,"connect_time_us":46528,"namelookup_time_us":26090,"pretransfer_time_us":113863,"redirect_time_us":100158,"starttransfer_time_us":202088,"total_time_us":203057,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### {"resultado": [{"id": 1, "nome": "Tribunal de Justi\u00e7a do Estado de S\u00e3o Paulo (SPr 3.1.2) "}, {"id": 2, "nome": "Bruno S\u00e1 Freire Martins "}, {"id": 3, "nome": "TJSP - Boletim da Se\u00e7\u00e3o de Direito Criminal "}, {"id": 4, "nome": "0"}, {"id": 5, "nome": "Tribunal de Justi\u00e7a do Estado de S\u00e3o Paulo "}, {"id": 6, "nome": "TJSP - Boletim de Direito P\u00fablico "}, {"id": 7, "nome": "Tribunal Regional do Trabalho da 2\u00aa Regi\u00e3o"}, {"id": 8, "nome": "Tribunal Regional do Trabalho da 2\u00aa Regi\u00e3o - Coordenadoria de Gest\u00e3o Normativa e Jurisprudencial"}, {"id": 9, "nome": "Tribunal Regional do Trabalho da 2\u00aa Regi\u00e3o - Se\u00e7\u00e3o de Divulga\u00e7\u00e3o "}, {"id": 10, "nome": "Superior Tribunal de Justi\u00e7a - Secretaria de Jurisprud\u00eancia"}, {"id": 11, "nome": "Superior Tribunal de Justi\u00e7a - Se\u00e7\u00e3o de Informativo de Jurisprud\u00eancia"}, {"id": 12, "nome": "Superior Tribunal de Justi\u00e7a  - Secretaria de Jurisprud\u00eancia"}, {"id": 13, "nome": "Tribunal Regional do Trabalho da 15\u00aa Regi\u00e3o "}, {"id": 14, "nome": "Supremo Tribunal Federal - Secretaria de Documenta\u00e7\u00e3o - SDO"}, {"id": 15, "nome": "Supremo Tribunal Federal - Coordenadoria de Jurisprud\u00eancia Comparada e Divulga\u00e7\u00e3o de Julgados"}, {"id": 16, "nome": "Superior Tribunal de Justi\u00e7a - Coordenadoria de Divulga\u00e7\u00e3o de Jurisprud\u00eancia"}, {"id": 17, "nome": "Supremo Tribunal Federal "}, {"id": 18, "nome": "Tribunal Regional Federal da Terceira Regi\u00e3o "}, {"id": 19, "nome": "Murillo Jos\u00e9 Digi\u00e1como"}, {"id": 20, "nome": "Ildeara de Amorim Digi\u00e1como "}, {"id": 21, "nome": "TRF 3\u00aaR - Desembargadora Federal Lucia Ursaia"}, {"id": 22, "nome": "TRF 3\u00aaR - Simone de Alcantara Savazzoni"}, {"id": 23, "nome": "TRF 3\u00aaR - Maria Jos\u00e9 Lopes Leite"}, {"id": 24, "nome": "TRF 3\u00aaR - Renata Bataglia Garcia"}, {"id": 25, "nome": "TRF 3\u00aaR - Maz\u00e9 Leite"}, {"id": 26, "nome": "HS Editora "}, {"id": 27, "nome": "Tribunal de Justi\u00e7a do Estado do Rio de Janeiro "}, {"id": 28, "nome": "Tribunal Superior do Trabalho - Coordenadoria de Jurisprud\u00eancia"}, {"id": 29, "nome": "Tribunal Regional do Trabalho da 1a Regi\u00e3o "}, {"id": 30, "nome": "Superior Tribunal de Justi\u00e7a "}, {"id": 31, "nome": "JURID Publica\u00e7\u00f5es Eletr\u00f4nicas "}, {"id": 32, "nome": "Escola Nacional de Forma\u00e7\u00e3o e Aperfei\u00e7oamento de Magistrados (Enfam) "}, {"id": 33, "nome": "Ordem dos Advogados do Brasil - Se\u00e7\u00e3o do Rio Grande do Sul "}, {"id": 34, "nome": "C\u00e2mara dos Deputados"}, {"id": 35, "nome": "Robson Zanetti "}, {"id": 36, "nome": "Ordem dos Advogados do Brasil - Seccional do Paran\u00e1 "}, {"id": 37, "nome": "Centro de Estudos Jur\u00eddicos da Presid\u00eancia "}, {"id": 38, "nome": "Tribunal de Justi\u00e7a do Estado do Rio Grande do Sul - Desembargadora Liselena Schifi no Robles Ribeiro"}, {"id": 39, "nome": "Tribunal de Justi\u00e7a do Estado do Rio Grande do Sul - Prof. Adalberto J. Kaspary"}, {"id": 40, "nome": "Tribunal de Contas da Uni\u00e3o"}, {"id": 41, "nome": "Tribunal Regional do Trabalho da 1\u00aa Regi\u00e3o"}, {"id": 42, "nome": "Supremo Tribunal Federal - Maria Cristina Hil\u00e1rio da Silva"}, {"id": 43, "nome": ""}, {"id": 44, "nome": "Antoninho Marmo Trevisan "}, {"id": 45, "nome": "Tribunal Superior do Trabalho "}, {"id": 46, "nome": "Tribunal Superior Eleitoral"}, {"id": 47, "nome": "Tribunal Regional do Trabalho da 2\u00aa Regi\u00e3o "}, {"id": 48, "nome": "Lisandra Thom\u00e9 "}, {"id": 49, "nome": "Tribunal de Justi\u00e7a do estado do Rio de Janeiro "}, {"id": 50, "nome": "TJDFT - Guilherme de sousa juliano"}, {"id": 51, "nome": "TJDFT - Alice fabre figueiredo"}, {"id": 52, "nome": "TJDFT - Ana Cl\u00e1udia N. T. de Loureiro"}, {"id": 53, "nome": "TJDFT - Cynthia de Campos Aspesi"}, {"id": 54, "nome": "TJDFT - Priscilla Kelly Santos Duarte Romeiro"}, {"id": 55, "nome": "TJDFT - Renata Guerra Amorim Abdala"}, {"id": 56, "nome": "TJDFT - Risoneis Alvares Barros"}, {"id": 57, "nome": "TJDFT - Ticiana Ara\u00fajo Passos"}, {"id": 58, "nome": "TJDFT - Willian Madeira Alves"}, {"id": 59, "nome": "Ju\u00edza Neusa Regina Larsen de Alvarenga Leite "}, {"id": 60, "nome": "Armando Casimiro Costa"}, {"id": 61, "nome": "Armando Casimiro Costa Filho"}, {"id": 62, "nome": "Amauri Mascaro Nascimento"}, {"id": 63, "nome": "Irany Ferrari"}, {"id": 64, "nome": "Ives Gandra da Silva Martins"}, {"id": 65, "nome": "Maria Cristina Irigoyen Peduzzi"}, {"id": 66, "nome": "Celso Barroso Leite"}, {"id": 67, "nome": "Wagner Balera"}, {"id": 68, "nome": "Wladimir Novaes Filho"}, {"id": 69, "nome": "Wladimir Novaes Martinez"}, {"id": 70, "nome": "Nelson Mannrich"}, {"id": 71, "nome": "S\u00f4nia Mascaro Nascimento"}, {"id": 72, "nome": "Melch\u00edades Rodrigues Martins"}, {"id": 73, "nome": "Editora Zakarewicz "}, {"id": 74, "nome": "Fernando Guiraud de Brito"}, {"id": 75, "nome": "Paulo Pardo"}, {"id": 76, "nome": "Fabio Oliveira Vaz "}, {"id": 77, "nome": "V\u00e1rios "}, {"id": 78, "nome": "Tribunal Regional do Trabalho da 4a Regi"}, {"id": 79, "nome": "Camila Schwambach Azevedo "}, {"id": 80, "nome": "Anamatra "}, {"id": 81, "nome": "Ben-Hur Silveira Claus "}, {"id": 82, "nome": "Tereza Aparecida Asta Gemignani "}, {"id": 83, "nome": "Aline Veiga Borges "}, {"id": 84, "nome": "J\u00falio C\u00e9sar Bebber "}, {"id": 85, "nome": "0"}, {"id": 86, "nome": "Tribunal de Justi\u00e7a do Estado do Paran\u00e1 - Centro de Documenta\u00e7\u00e3o - CEDOC "}, {"id": 87, "nome": "Murilo Oliveira de Castro Coelho"}, {"id": 88, "nome": "Al\u00edpio Abreu Cara Neto"}, {"id": 89, "nome": "Alexandre Melo Franco Bahia"}, {"id": 90, "nome": "Andr\u00e9 L. Costa-Corr\u00eaa"}, {"id": 91, "nome": "Gisela Maria Bester"}, {"id": 92, "nome": "Josiane Petry Faria"}, {"id": 93, "nome": "Jos\u00e9 Eduardo Carreira Alvim"}, {"id": 94, "nome": "Tassos Lycurgo Galv\u00e3o Nunes"}, {"id": 95, "nome": "Tha\u00eds Alves Marinho"}, {"id": 96, "nome": "Amapar - Luiz Fernando de Queiroz"}, {"id": 97, "nome": "Amapar - Dulce de Queiroz Piacentini"}, {"id": 98, "nome": "Amapar - Noeli do Carmo Faria"}, {"id": 99, "nome": "Amapar - Josiane C. L. Martins"}, {"id": 100, "nome": "Luiz Fernando de Queiroz"}, {"id": 101, "nome": "Dulce de Queiroz Piacentini"}, {"id": 102, "nome": "Noeli do Carmo Faria"}, {"id": 103, "nome": "Josiane C. L. Martins"}, {"id": 104, "nome": "Tribunal Regional do Trabalho da 3\u00aa Regi\u00e3o"}, {"id": 105, "nome": " Associa\u00e7\u00e3o dos Advogados Trabalhistas de S\u00e3o Paulo "}, {"id": 106, "nome": "Associa"}, {"id": 107, "nome": "Renata Cortez"}, {"id": 108, "nome": "Rosalina Freitas"}, {"id": 109, "nome": "Lorena Guedes "}, {"id": 110, "nome": "Conselho Nacional de Justi\u00e7a"}, {"id": 111, "nome": "Jos\u00e9 Rog\u00e9rio Cruz e Tucci"}, {"id": 112, "nome": "Manoel Caetano Ferreira Filho"}, {"id": 113, "nome": "Ricardo de Carvalho Aprigliano"}, {"id": 114, "nome": "Rog\u00e9ria Fagundes Dotti"}, {"id": 115, "nome": "Sandro Gilbert Martins "}, {"id": 116, "nome": "Associa\u00e7\u00e3o dos Advogados Trabalhistas de S\u00e3o Paulo - AATSP "}, {"id": 117, "nome": "Ordem dos Advogados do Brasil - Conselho Federal "}, {"id": 118, "nome": "Escola Superior do Minist\u00e9rio P\u00fablico de S\u00e3o Paulo "}, {"id": 119, "nome": "Vigna - Advogados Associados "}, {"id": 120, "nome": "AATSP - Associa\u00e7\u00e3o dos Advogados Trabalhistas de S\u00e3o Paulo"}, {"id": 121, "nome": "Associa\u00e7\u00e3o dos Magistrados do Paran\u00e1 - Amapar"}, {"id": 122, "nome": "ASSOCIA\u00c7\u00c3O NACIONAL DOS MAGISTRADOS DA JUSTI\u00c7A DO TRABALHO"}, {"id": 123, "nome": "Conselho Nacional de Justi\u00e7a - CNJ"}, {"id": 124, "nome": "Jo\u00e3o Alfredo Nunes da Costa Filho"}, {"id": 125, "nome": "Diretor Respons\u00e1vel: Armando Casimiro Costa Filho"}, {"id": 126, "nome": "Escola da Defensoria P\u00fablica do Estado de S\u00e3o Paulo"}, {"id": 127, "nome": "Escola Paulista da Magistratura"}, {"id": 128, "nome": "Ordem dos Advogados do Brasil. Se\u00e7\u00e3o do Rio Grande do Sul."}, {"id": 129, "nome": "PODER JUDICI\u00c1RIO FEDERAL - TRIBUNAL REGIONAL DO TRABALHO DA 4\u00aa REGI\u00c3O"}, {"id": 130, "nome": "Superior Tribunal de Justi\u00e7a - STJ"}, {"id": 131, "nome": "Supremo Tribunal Federal - STF"}, {"id": 132, "nome": "Supremo Tribunal Federal STF"}, {"id": 133, "nome": "Tha\u00edsa Florinda Silva"}, {"id": 134, "nome": "TJRJ"}, {"id": 135, "nome": "TJSP"}, {"id": 136, "nome": "TJSP - Boletim de Direito Criminal - 02/2017"}, {"id": 137, "nome": "TJSP - \u00d3rg\u00e3o Especial"}, {"id": 138, "nome": "Tribunal de Contas da Uni\u00e3o - TCU"}, {"id": 139, "nome": "Tribunal de Contas da Uni\u00e3o TCU"}, {"id": 140, "nome": "Tribunal de Justi\u00e7a de S\u00e3o Paulo"}, {"id": 141, "nome": "Tribunal de Justi\u00e7a de S\u00e3o Paulo - TJSP"}, {"id": 142, "nome": "Tribunal de Justi\u00e7a do Distrito Federal e Territ\u00f3rios"}, {"id": 143, "nome": "Tribunal de Justi\u00e7a do Estado de S\u00e3o Paulo - TJSP"}, {"id": 144, "nome": "Tribunal de Justi\u00e7a do Estado do Rio Grande do Sul"}, {"id": 145, "nome": "Tribunal de Justi\u00e7a do Rio de Janeiro"}, {"id": 146, "nome": "Tribunal Regional do Trabalho da 1\u00aa Regi\u00e3o TRT 1"}, {"id": 147, "nome": "Tribunal Regional do Trabalho da 2\u00aa Regi\u00e3o - TRT2R"}, {"id": 148, "nome": "Tribunal Regional do Trabalho da 2\u00aa Regi\u00e3o TRT2"}, {"id": 149, "nome": "Tribunal Regional do Trabalho da 3\u00aa Regi\u00e3o (MG"}, {"id": 150, "nome": "Tribunal Regional do Trabalho da 3\u00aa Regi\u00e3o (MG)"}, {"id": 151, "nome": "Tribunal Regional do Trabalho da 4\u00aa Regi\u00e3o - Rio Grande do Sul"}, {"id": 152, "nome": "Tribunal Regional do Trabalho da Primeira Regi\u00e3o"}, {"id": 153, "nome": "Tribunal Regional do Trabalho da Segunda Regi\u00e3o"}, {"id": 154, "nome": "Tribunal Superior do Trabalho - TST"}, {"id": 155, "nome": "Tribunal Superior Eleitoral - TSE"}, {"id": 156, "nome": "TRT 1\u00aaR - Boletim de Jurisprud\u00eancia nov-dez/2014"}, {"id": 157, "nome": "Wemilton Ramos Teixeira J\u00fanior"}, {"id": 158, "nome": "Pedro Lima"}], "erro": 0}
25/08/2025 22:44 (ET: 0.14718) https://api.lettore.com.br/categorias.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/categorias.listar2","content_type":"application\/json; charset=UTF-8","http_code":200,"header_size":1032,"request_size":427,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.14692,"namelookup_time":0.00572,"connect_time":0.023685,"pretransfer_time":0.107005,"size_upload":0,"size_download":1479,"speed_download":10066,"speed_upload":0,"download_content_length":1479,"upload_content_length":0,"starttransfer_time":0.146593,"redirect_time":0.07726,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":51256,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":106798,"connect_time_us":23685,"namelookup_time_us":5720,"pretransfer_time_us":107005,"redirect_time_us":77260,"starttransfer_time_us":146593,"total_time_us":146920,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### {"resultado": [{"categoria": "Revistas Eletr\u00f4nicas", "idcategoria": 0, "id": 1, "omitir_em_listagem": 0}, {"categoria": "Boletins e Informativos", "idcategoria": 0, "id": 2, "omitir_em_listagem": 0}, {"categoria": "TJSP-Boletim de Direito Privado", "idcategoria": 0, "id": 15, "omitir_em_listagem": 0}, {"categoria": "TJSP-Boletim de Direito Criminal", "idcategoria": 0, "id": 14, "omitir_em_listagem": 0}, {"categoria": "Suplementos", "idcategoria": 0, "id": 13, "omitir_em_listagem": 0}, {"categoria": "Previd\u00eancia", "idcategoria": 0, "id": 12, "omitir_em_listagem": 0}, {"categoria": "Informativos", "idcategoria": 0, "id": 11, "omitir_em_listagem": 0}, {"categoria": "TJSP-Boletim de Direito Publico", "idcategoria": 0, "id": 16, "omitir_em_listagem": 0}, {"categoria": "TJSP-Boletim do \u00d3rg\u00e3o Especial", "idcategoria": 0, "id": 17, "omitir_em_listagem": 0}, {"categoria": "TJSP-Revista Eletr\u00f4nica", "idcategoria": 0, "id": 18, "omitir_em_listagem": 0}, {"categoria": "Boletins Trabalhistas", "idcategoria": 0, "id": 19, "omitir_em_listagem": 0}, {"categoria": "Legisla\u00e7\u00e3o", "idcategoria": 0, "id": 20, "omitir_em_listagem": 0}, {"categoria": "Revista LTr", "idcategoria": null, "id": 24, "omitir_em_listagem": 0}, {"categoria": "Revista de Previd\u00eancia Social", "idcategoria": null, "id": 27, "omitir_em_listagem": 0}, {"categoria": "Revista Vanguarda Jur\u00eddica", "idcategoria": null, "id": 28, "omitir_em_listagem": 0}], "erro": 0}
25/08/2025 23:17 (ET: 0.45765) https://api.lettore.com.br/autores.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/autores.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":502,"header_size":772,"request_size":429,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.4574,"namelookup_time":0.337137,"connect_time":0.365866,"pretransfer_time":0.43299,"size_upload":0,"size_download":410,"speed_download":896,"speed_upload":0,"download_content_length":410,"upload_content_length":0,"starttransfer_time":0.456988,"redirect_time":0.401791,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":44126,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":432744,"connect_time_us":365866,"namelookup_time_us":337137,"pretransfer_time_us":432990,"redirect_time_us":401791,"starttransfer_time_us":456988,"total_time_us":457400,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>502 Proxy Error</title>
</head><body>
<h1>Proxy Error</h1>
<p>The proxy server received an invalid
response from an upstream server.<br />
The proxy server could not handle the request <em><a href="/autores.listar2">POST&nbsp;/autores.listar2</a></em>.<p>
Reason: <strong>Error reading from remote server</strong></p></p>
</body></html>

25/08/2025 23:17 (ET: 0.1216) https://api.lettore.com.br/categorias.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/categorias.listar2","content_type":"text\/html; charset=iso-8859-1","http_code":502,"header_size":772,"request_size":435,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.121095,"namelookup_time":0.007887,"connect_time":0.028277,"pretransfer_time":0.087986,"size_upload":0,"size_download":416,"speed_download":3435,"speed_upload":0,"download_content_length":416,"upload_content_length":0,"starttransfer_time":0.120774,"redirect_time":0.070046,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":44134,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":87695,"connect_time_us":28277,"namelookup_time_us":7887,"pretransfer_time_us":87986,"redirect_time_us":70046,"starttransfer_time_us":120774,"total_time_us":121095,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>502 Proxy Error</title>
</head><body>
<h1>Proxy Error</h1>
<p>The proxy server received an invalid
response from an upstream server.<br />
The proxy server could not handle the request <em><a href="/categorias.listar2">POST&nbsp;/categorias.listar2</a></em>.<p>
Reason: <strong>Error reading from remote server</strong></p></p>
</body></html>

25/08/2025 23:18 (ET: 0.15142) https://api.lettore.com.br/autores.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/autores.listar2","content_type":"application\/json; charset=UTF-8","http_code":200,"header_size":1033,"request_size":421,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.151216,"namelookup_time":0.009059,"connect_time":0.045694,"pretransfer_time":0.11414,"size_upload":0,"size_download":10227,"speed_download":67631,"speed_upload":0,"download_content_length":10227,"upload_content_length":0,"starttransfer_time":0.150561,"redirect_time":0.082248,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":42432,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":113850,"connect_time_us":45694,"namelookup_time_us":9059,"pretransfer_time_us":114140,"redirect_time_us":82248,"starttransfer_time_us":150561,"total_time_us":151216,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### {"resultado": [{"id": 1, "nome": "Tribunal de Justi\u00e7a do Estado de S\u00e3o Paulo (SPr 3.1.2) "}, {"id": 2, "nome": "Bruno S\u00e1 Freire Martins "}, {"id": 3, "nome": "TJSP - Boletim da Se\u00e7\u00e3o de Direito Criminal "}, {"id": 4, "nome": "0"}, {"id": 5, "nome": "Tribunal de Justi\u00e7a do Estado de S\u00e3o Paulo "}, {"id": 6, "nome": "TJSP - Boletim de Direito P\u00fablico "}, {"id": 7, "nome": "Tribunal Regional do Trabalho da 2\u00aa Regi\u00e3o"}, {"id": 8, "nome": "Tribunal Regional do Trabalho da 2\u00aa Regi\u00e3o - Coordenadoria de Gest\u00e3o Normativa e Jurisprudencial"}, {"id": 9, "nome": "Tribunal Regional do Trabalho da 2\u00aa Regi\u00e3o - Se\u00e7\u00e3o de Divulga\u00e7\u00e3o "}, {"id": 10, "nome": "Superior Tribunal de Justi\u00e7a - Secretaria de Jurisprud\u00eancia"}, {"id": 11, "nome": "Superior Tribunal de Justi\u00e7a - Se\u00e7\u00e3o de Informativo de Jurisprud\u00eancia"}, {"id": 12, "nome": "Superior Tribunal de Justi\u00e7a  - Secretaria de Jurisprud\u00eancia"}, {"id": 13, "nome": "Tribunal Regional do Trabalho da 15\u00aa Regi\u00e3o "}, {"id": 14, "nome": "Supremo Tribunal Federal - Secretaria de Documenta\u00e7\u00e3o - SDO"}, {"id": 15, "nome": "Supremo Tribunal Federal - Coordenadoria de Jurisprud\u00eancia Comparada e Divulga\u00e7\u00e3o de Julgados"}, {"id": 16, "nome": "Superior Tribunal de Justi\u00e7a - Coordenadoria de Divulga\u00e7\u00e3o de Jurisprud\u00eancia"}, {"id": 17, "nome": "Supremo Tribunal Federal "}, {"id": 18, "nome": "Tribunal Regional Federal da Terceira Regi\u00e3o "}, {"id": 19, "nome": "Murillo Jos\u00e9 Digi\u00e1como"}, {"id": 20, "nome": "Ildeara de Amorim Digi\u00e1como "}, {"id": 21, "nome": "TRF 3\u00aaR - Desembargadora Federal Lucia Ursaia"}, {"id": 22, "nome": "TRF 3\u00aaR - Simone de Alcantara Savazzoni"}, {"id": 23, "nome": "TRF 3\u00aaR - Maria Jos\u00e9 Lopes Leite"}, {"id": 24, "nome": "TRF 3\u00aaR - Renata Bataglia Garcia"}, {"id": 25, "nome": "TRF 3\u00aaR - Maz\u00e9 Leite"}, {"id": 26, "nome": "HS Editora "}, {"id": 27, "nome": "Tribunal de Justi\u00e7a do Estado do Rio de Janeiro "}, {"id": 28, "nome": "Tribunal Superior do Trabalho - Coordenadoria de Jurisprud\u00eancia"}, {"id": 29, "nome": "Tribunal Regional do Trabalho da 1a Regi\u00e3o "}, {"id": 30, "nome": "Superior Tribunal de Justi\u00e7a "}, {"id": 31, "nome": "JURID Publica\u00e7\u00f5es Eletr\u00f4nicas "}, {"id": 32, "nome": "Escola Nacional de Forma\u00e7\u00e3o e Aperfei\u00e7oamento de Magistrados (Enfam) "}, {"id": 33, "nome": "Ordem dos Advogados do Brasil - Se\u00e7\u00e3o do Rio Grande do Sul "}, {"id": 34, "nome": "C\u00e2mara dos Deputados"}, {"id": 35, "nome": "Robson Zanetti "}, {"id": 36, "nome": "Ordem dos Advogados do Brasil - Seccional do Paran\u00e1 "}, {"id": 37, "nome": "Centro de Estudos Jur\u00eddicos da Presid\u00eancia "}, {"id": 38, "nome": "Tribunal de Justi\u00e7a do Estado do Rio Grande do Sul - Desembargadora Liselena Schifi no Robles Ribeiro"}, {"id": 39, "nome": "Tribunal de Justi\u00e7a do Estado do Rio Grande do Sul - Prof. Adalberto J. Kaspary"}, {"id": 40, "nome": "Tribunal de Contas da Uni\u00e3o"}, {"id": 41, "nome": "Tribunal Regional do Trabalho da 1\u00aa Regi\u00e3o"}, {"id": 42, "nome": "Supremo Tribunal Federal - Maria Cristina Hil\u00e1rio da Silva"}, {"id": 43, "nome": ""}, {"id": 44, "nome": "Antoninho Marmo Trevisan "}, {"id": 45, "nome": "Tribunal Superior do Trabalho "}, {"id": 46, "nome": "Tribunal Superior Eleitoral"}, {"id": 47, "nome": "Tribunal Regional do Trabalho da 2\u00aa Regi\u00e3o "}, {"id": 48, "nome": "Lisandra Thom\u00e9 "}, {"id": 49, "nome": "Tribunal de Justi\u00e7a do estado do Rio de Janeiro "}, {"id": 50, "nome": "TJDFT - Guilherme de sousa juliano"}, {"id": 51, "nome": "TJDFT - Alice fabre figueiredo"}, {"id": 52, "nome": "TJDFT - Ana Cl\u00e1udia N. T. de Loureiro"}, {"id": 53, "nome": "TJDFT - Cynthia de Campos Aspesi"}, {"id": 54, "nome": "TJDFT - Priscilla Kelly Santos Duarte Romeiro"}, {"id": 55, "nome": "TJDFT - Renata Guerra Amorim Abdala"}, {"id": 56, "nome": "TJDFT - Risoneis Alvares Barros"}, {"id": 57, "nome": "TJDFT - Ticiana Ara\u00fajo Passos"}, {"id": 58, "nome": "TJDFT - Willian Madeira Alves"}, {"id": 59, "nome": "Ju\u00edza Neusa Regina Larsen de Alvarenga Leite "}, {"id": 60, "nome": "Armando Casimiro Costa"}, {"id": 61, "nome": "Armando Casimiro Costa Filho"}, {"id": 62, "nome": "Amauri Mascaro Nascimento"}, {"id": 63, "nome": "Irany Ferrari"}, {"id": 64, "nome": "Ives Gandra da Silva Martins"}, {"id": 65, "nome": "Maria Cristina Irigoyen Peduzzi"}, {"id": 66, "nome": "Celso Barroso Leite"}, {"id": 67, "nome": "Wagner Balera"}, {"id": 68, "nome": "Wladimir Novaes Filho"}, {"id": 69, "nome": "Wladimir Novaes Martinez"}, {"id": 70, "nome": "Nelson Mannrich"}, {"id": 71, "nome": "S\u00f4nia Mascaro Nascimento"}, {"id": 72, "nome": "Melch\u00edades Rodrigues Martins"}, {"id": 73, "nome": "Editora Zakarewicz "}, {"id": 74, "nome": "Fernando Guiraud de Brito"}, {"id": 75, "nome": "Paulo Pardo"}, {"id": 76, "nome": "Fabio Oliveira Vaz "}, {"id": 77, "nome": "V\u00e1rios "}, {"id": 78, "nome": "Tribunal Regional do Trabalho da 4a Regi"}, {"id": 79, "nome": "Camila Schwambach Azevedo "}, {"id": 80, "nome": "Anamatra "}, {"id": 81, "nome": "Ben-Hur Silveira Claus "}, {"id": 82, "nome": "Tereza Aparecida Asta Gemignani "}, {"id": 83, "nome": "Aline Veiga Borges "}, {"id": 84, "nome": "J\u00falio C\u00e9sar Bebber "}, {"id": 85, "nome": "0"}, {"id": 86, "nome": "Tribunal de Justi\u00e7a do Estado do Paran\u00e1 - Centro de Documenta\u00e7\u00e3o - CEDOC "}, {"id": 87, "nome": "Murilo Oliveira de Castro Coelho"}, {"id": 88, "nome": "Al\u00edpio Abreu Cara Neto"}, {"id": 89, "nome": "Alexandre Melo Franco Bahia"}, {"id": 90, "nome": "Andr\u00e9 L. Costa-Corr\u00eaa"}, {"id": 91, "nome": "Gisela Maria Bester"}, {"id": 92, "nome": "Josiane Petry Faria"}, {"id": 93, "nome": "Jos\u00e9 Eduardo Carreira Alvim"}, {"id": 94, "nome": "Tassos Lycurgo Galv\u00e3o Nunes"}, {"id": 95, "nome": "Tha\u00eds Alves Marinho"}, {"id": 96, "nome": "Amapar - Luiz Fernando de Queiroz"}, {"id": 97, "nome": "Amapar - Dulce de Queiroz Piacentini"}, {"id": 98, "nome": "Amapar - Noeli do Carmo Faria"}, {"id": 99, "nome": "Amapar - Josiane C. L. Martins"}, {"id": 100, "nome": "Luiz Fernando de Queiroz"}, {"id": 101, "nome": "Dulce de Queiroz Piacentini"}, {"id": 102, "nome": "Noeli do Carmo Faria"}, {"id": 103, "nome": "Josiane C. L. Martins"}, {"id": 104, "nome": "Tribunal Regional do Trabalho da 3\u00aa Regi\u00e3o"}, {"id": 105, "nome": " Associa\u00e7\u00e3o dos Advogados Trabalhistas de S\u00e3o Paulo "}, {"id": 106, "nome": "Associa"}, {"id": 107, "nome": "Renata Cortez"}, {"id": 108, "nome": "Rosalina Freitas"}, {"id": 109, "nome": "Lorena Guedes "}, {"id": 110, "nome": "Conselho Nacional de Justi\u00e7a"}, {"id": 111, "nome": "Jos\u00e9 Rog\u00e9rio Cruz e Tucci"}, {"id": 112, "nome": "Manoel Caetano Ferreira Filho"}, {"id": 113, "nome": "Ricardo de Carvalho Aprigliano"}, {"id": 114, "nome": "Rog\u00e9ria Fagundes Dotti"}, {"id": 115, "nome": "Sandro Gilbert Martins "}, {"id": 116, "nome": "Associa\u00e7\u00e3o dos Advogados Trabalhistas de S\u00e3o Paulo - AATSP "}, {"id": 117, "nome": "Ordem dos Advogados do Brasil - Conselho Federal "}, {"id": 118, "nome": "Escola Superior do Minist\u00e9rio P\u00fablico de S\u00e3o Paulo "}, {"id": 119, "nome": "Vigna - Advogados Associados "}, {"id": 120, "nome": "AATSP - Associa\u00e7\u00e3o dos Advogados Trabalhistas de S\u00e3o Paulo"}, {"id": 121, "nome": "Associa\u00e7\u00e3o dos Magistrados do Paran\u00e1 - Amapar"}, {"id": 122, "nome": "ASSOCIA\u00c7\u00c3O NACIONAL DOS MAGISTRADOS DA JUSTI\u00c7A DO TRABALHO"}, {"id": 123, "nome": "Conselho Nacional de Justi\u00e7a - CNJ"}, {"id": 124, "nome": "Jo\u00e3o Alfredo Nunes da Costa Filho"}, {"id": 125, "nome": "Diretor Respons\u00e1vel: Armando Casimiro Costa Filho"}, {"id": 126, "nome": "Escola da Defensoria P\u00fablica do Estado de S\u00e3o Paulo"}, {"id": 127, "nome": "Escola Paulista da Magistratura"}, {"id": 128, "nome": "Ordem dos Advogados do Brasil. Se\u00e7\u00e3o do Rio Grande do Sul."}, {"id": 129, "nome": "PODER JUDICI\u00c1RIO FEDERAL - TRIBUNAL REGIONAL DO TRABALHO DA 4\u00aa REGI\u00c3O"}, {"id": 130, "nome": "Superior Tribunal de Justi\u00e7a - STJ"}, {"id": 131, "nome": "Supremo Tribunal Federal - STF"}, {"id": 132, "nome": "Supremo Tribunal Federal STF"}, {"id": 133, "nome": "Tha\u00edsa Florinda Silva"}, {"id": 134, "nome": "TJRJ"}, {"id": 135, "nome": "TJSP"}, {"id": 136, "nome": "TJSP - Boletim de Direito Criminal - 02/2017"}, {"id": 137, "nome": "TJSP - \u00d3rg\u00e3o Especial"}, {"id": 138, "nome": "Tribunal de Contas da Uni\u00e3o - TCU"}, {"id": 139, "nome": "Tribunal de Contas da Uni\u00e3o TCU"}, {"id": 140, "nome": "Tribunal de Justi\u00e7a de S\u00e3o Paulo"}, {"id": 141, "nome": "Tribunal de Justi\u00e7a de S\u00e3o Paulo - TJSP"}, {"id": 142, "nome": "Tribunal de Justi\u00e7a do Distrito Federal e Territ\u00f3rios"}, {"id": 143, "nome": "Tribunal de Justi\u00e7a do Estado de S\u00e3o Paulo - TJSP"}, {"id": 144, "nome": "Tribunal de Justi\u00e7a do Estado do Rio Grande do Sul"}, {"id": 145, "nome": "Tribunal de Justi\u00e7a do Rio de Janeiro"}, {"id": 146, "nome": "Tribunal Regional do Trabalho da 1\u00aa Regi\u00e3o TRT 1"}, {"id": 147, "nome": "Tribunal Regional do Trabalho da 2\u00aa Regi\u00e3o - TRT2R"}, {"id": 148, "nome": "Tribunal Regional do Trabalho da 2\u00aa Regi\u00e3o TRT2"}, {"id": 149, "nome": "Tribunal Regional do Trabalho da 3\u00aa Regi\u00e3o (MG"}, {"id": 150, "nome": "Tribunal Regional do Trabalho da 3\u00aa Regi\u00e3o (MG)"}, {"id": 151, "nome": "Tribunal Regional do Trabalho da 4\u00aa Regi\u00e3o - Rio Grande do Sul"}, {"id": 152, "nome": "Tribunal Regional do Trabalho da Primeira Regi\u00e3o"}, {"id": 153, "nome": "Tribunal Regional do Trabalho da Segunda Regi\u00e3o"}, {"id": 154, "nome": "Tribunal Superior do Trabalho - TST"}, {"id": 155, "nome": "Tribunal Superior Eleitoral - TSE"}, {"id": 156, "nome": "TRT 1\u00aaR - Boletim de Jurisprud\u00eancia nov-dez/2014"}, {"id": 157, "nome": "Wemilton Ramos Teixeira J\u00fanior"}, {"id": 158, "nome": "Pedro Lima"}], "erro": 0}
25/08/2025 23:18 (ET: 0.13574) https://api.lettore.com.br/categorias.listar2 ####### # CURL INFO ------  ####### {"url":"https:\/\/api.lettore.com.br\/categorias.listar2","content_type":"application\/json; charset=UTF-8","http_code":200,"header_size":1032,"request_size":427,"filetime":-1,"ssl_verify_result":0,"redirect_count":1,"total_time":0.135322,"namelookup_time":0.010206,"connect_time":0.043865,"pretransfer_time":0.100559,"size_upload":0,"size_download":1479,"speed_download":10929,"speed_upload":0,"download_content_length":1479,"upload_content_length":0,"starttransfer_time":0.13498,"redirect_time":0.070595,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":42444,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"https","appconnect_time_us":100352,"connect_time_us":43865,"namelookup_time_us":10206,"pretransfer_time_us":100559,"redirect_time_us":70595,"starttransfer_time_us":134980,"total_time_us":135322,"effective_method":"POST"} ####### # DATA SEND ------  ####### [] ####### # RESPONSE  ------  ####### {"resultado": [{"categoria": "Revistas Eletr\u00f4nicas", "idcategoria": 0, "id": 1, "omitir_em_listagem": 0}, {"categoria": "Boletins e Informativos", "idcategoria": 0, "id": 2, "omitir_em_listagem": 0}, {"categoria": "TJSP-Boletim de Direito Privado", "idcategoria": 0, "id": 15, "omitir_em_listagem": 0}, {"categoria": "TJSP-Boletim de Direito Criminal", "idcategoria": 0, "id": 14, "omitir_em_listagem": 0}, {"categoria": "Suplementos", "idcategoria": 0, "id": 13, "omitir_em_listagem": 0}, {"categoria": "Previd\u00eancia", "idcategoria": 0, "id": 12, "omitir_em_listagem": 0}, {"categoria": "Informativos", "idcategoria": 0, "id": 11, "omitir_em_listagem": 0}, {"categoria": "TJSP-Boletim de Direito Publico", "idcategoria": 0, "id": 16, "omitir_em_listagem": 0}, {"categoria": "TJSP-Boletim do \u00d3rg\u00e3o Especial", "idcategoria": 0, "id": 17, "omitir_em_listagem": 0}, {"categoria": "TJSP-Revista Eletr\u00f4nica", "idcategoria": 0, "id": 18, "omitir_em_listagem": 0}, {"categoria": "Boletins Trabalhistas", "idcategoria": 0, "id": 19, "omitir_em_listagem": 0}, {"categoria": "Legisla\u00e7\u00e3o", "idcategoria": 0, "id": 20, "omitir_em_listagem": 0}, {"categoria": "Revista LTr", "idcategoria": null, "id": 24, "omitir_em_listagem": 0}, {"categoria": "Revista de Previd\u00eancia Social", "idcategoria": null, "id": 27, "omitir_em_listagem": 0}, {"categoria": "Revista Vanguarda Jur\u00eddica", "idcategoria": null, "id": 28, "omitir_em_listagem": 0}], "erro": 0}
