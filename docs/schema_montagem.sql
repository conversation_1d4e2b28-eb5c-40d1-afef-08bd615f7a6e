/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `categorias`
--

DROP TABLE IF EXISTS `categorias`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `categorias` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `idcategoria` int(11) DEFAULT NULL,
  `categoria` varchar(150) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ix_idcategoria` (`idcategoria`),
  KEY `ix_nomecategoria` (`categoria`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `imagens`
--

DROP TABLE IF EXISTS `imagens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `imagens` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `idobra` bigint(20) NOT NULL,
  `nomearquivo` varchar(300) NOT NULL,
  `imagem` longblob,
  `sincronizar` smallint(6) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `idx_idobra` (`idobra`),
  KEY `ix_sincronizacao` (`sincronizar`,`idobra`,`id`)
) ENGINE=MyISAM DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `marcacoes`
--

DROP TABLE IF EXISTS `marcacoes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `marcacoes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `idobra` int(11) DEFAULT NULL,
  `json` longtext,
  `pagina` int(11) DEFAULT NULL,
  `html` longtext,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `obras`
--

DROP TABLE IF EXISTS `obras`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `obras` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `idcategoria` int(11) DEFAULT NULL,
  `titulo` varchar(200) COLLATE latin1_general_ci NOT NULL,
  `slug` varchar(500) COLLATE latin1_general_ci DEFAULT NULL,
  `autor` varchar(200) COLLATE latin1_general_ci NOT NULL,
  `dados_obra` longtext COLLATE latin1_general_ci,
  `dados_autor` longtext COLLATE latin1_general_ci,
  `capa` longblob,
  `preco` float DEFAULT '0',
  `qtdpaginas` smallint(6) NOT NULL,
  `edicao` varchar(50) COLLATE latin1_general_ci DEFAULT NULL,
  `ano` smallint(6) DEFAULT NULL,
  `tipo_ISBN` varchar(50) COLLATE latin1_general_ci DEFAULT 'ISBN',
  `ISBN` varchar(50) COLLATE latin1_general_ci DEFAULT NULL,
  `codigo_editora` varchar(32) COLLATE latin1_general_ci DEFAULT '',
  `status` tinyint(4) DEFAULT '0',
  `porcentagemautor` float NOT NULL DEFAULT '0.1',
  `paginaum` int(11) DEFAULT NULL,
  `capa_hi_res` longblob,
  `ebook` int(11) DEFAULT '0',
  `json_extra` text COLLATE latin1_general_ci,
  `sincronizar` int(11) DEFAULT '1',
  `assinatura` int(11) DEFAULT '0',
  `revisao` int(11) DEFAULT '0',
  `datapublicacao` datetime DEFAULT NULL,
  `datalancamento` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `mastigando` int(11) DEFAULT '0',
  `etapa` int(11) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `ix_slug` (`slug`)
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `paginas`
--

DROP TABLE IF EXISTS `paginas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `paginas` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `idobra` int(11) NOT NULL,
  `numeropagina` int(11) NOT NULL,
  `texto` longtext,
  `alteradaem` datetime DEFAULT NULL,
  `sincronizar` int(11) DEFAULT '1',
  `json_conteudos` longtext,
  PRIMARY KEY (`id`),
  KEY `idx_idobra` (`idobra`),
  KEY `idx_idobrapagina` (`idobra`,`numeropagina`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sessoes_usuarios`
--

DROP TABLE IF EXISTS `sessoes_usuarios`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sessoes_usuarios` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `idusuario` bigint(20) NOT NULL,
  `idobra` bigint(20) NOT NULL,
  `login` datetime NOT NULL,
  `ultimoacesso` datetime DEFAULT NULL,
  `fechada` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_usuario` (`idusuario`),
  KEY `idx_obra` (`idobra`),
  KEY `idx_usuarioobra` (`idusuario`,`idobra`)
) ENGINE=MyISAM DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sumarios`
--

DROP TABLE IF EXISTS `sumarios`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sumarios` (
  `idobra` int(11) NOT NULL,
  `indice` varchar(1000) DEFAULT NULL,
  `nivel` int(11) NOT NULL,
  `pagina` int(11) NOT NULL,
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pagina_ordem` int(11) DEFAULT '0',
  `html` text,
  PRIMARY KEY (`id`),
  KEY `ix_idobra_pagina_ordem` (`idobra`,`pagina_ordem`)
) ENGINE=MyISAM DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `usuarios`
--

DROP TABLE IF EXISTS `usuarios`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `usuarios` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `nome` varchar(150) DEFAULT NULL,
  `usuario` varchar(20) NOT NULL,
  `senha` varchar(64) DEFAULT NULL,
  `ativo` tinyint(4) NOT NULL DEFAULT '1',
  `admin` int(11) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `usuarios_obras`
--

DROP TABLE IF EXISTS `usuarios_obras`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `usuarios_obras` (
  `idusuario` bigint(20) NOT NULL,
  `idobra` bigint(20) NOT NULL,
  `permite_alterar` tinyint(4) DEFAULT '0',
  `permite_rascunho` tinyint(4) DEFAULT '0',
  `permite_publicacao` tinyint(4) DEFAULT '0',
  `permite_imagens` int(11) DEFAULT '0',
  PRIMARY KEY (`idusuario`,`idobra`)
) ENGINE=MyISAM DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
