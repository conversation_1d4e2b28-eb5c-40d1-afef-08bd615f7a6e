-- ⚠️ DEMONSTRAÇÃO DE QUEBRA DE SENHAS SHA-1 
-- ⚠️ USO EXCLUSIVO PARA APRESENTAÇÃO DE SEGURANÇA
-- ⚠️ NÃO USAR EM PRODUÇÃO SEM AUTORIZAÇÃO

-- =================================================================
-- QUERIES PARA DEMONSTRAR VULNERABILIDADE DAS SENHAS SHA-1
-- =================================================================

-- 1. IDENTIFICAR TODAS AS SENHAS SHA-1 INSEGURAS
SELECT 
    id,
    usuario,
    nome,
    senha,
    'HASH SHA-1 INSEGURO' as status_seguranca,
    'QUEBRAR IMEDIATAMENTE' as acao_requerida
FROM usuarios 
WHERE LENGTH(senha) = 40 
  AND senha REGEXP '^[a-f0-9]+$'
ORDER BY id;

-- 2. ENCONTRAR SENHAS DUPLICADAS (POSSÍVEL PADRÃO/FRACA)
SELECT 
    senha,
    COUNT(*) as total_usuarios_mesma_senha,
    GROUP_CONCAT(usuario ORDER BY usuario) as usuarios_afetados,
    'SENHA REUTILIZADA - ALTA PROBABILIDADE DE SER FRACA' as alerta
FROM usuarios 
WHERE LENGTH(senha) = 40
GROUP BY senha 
HAVING COUNT(*) > 1
ORDER BY total_usuarios_mesma_senha DESC;

-- 3. TESTAR SENHAS MAIS COMUNS (TOP 100 SENHAS FRACAS)
-- ⚠️ Esta query tentará identificar senhas fracas comuns
SELECT 
    u.id,
    u.usuario,
    u.nome,
    u.senha,
    CASE 
        -- Top 10 senhas mais usadas no mundo
        WHEN u.senha = SHA1('123456') THEN '123456'
        WHEN u.senha = SHA1('password') THEN 'password'
        WHEN u.senha = SHA1('12345678') THEN '12345678'
        WHEN u.senha = SHA1('qwerty') THEN 'qwerty'
        WHEN u.senha = SHA1('123456789') THEN '123456789'
        WHEN u.senha = SHA1('12345') THEN '12345'
        WHEN u.senha = SHA1('1234') THEN '1234'
        WHEN u.senha = SHA1('111111') THEN '111111'
        WHEN u.senha = SHA1('1234567890') THEN '1234567890'
        WHEN u.senha = SHA1('123123') THEN '123123'
        
        -- Senhas administrativas comuns
        WHEN u.senha = SHA1('admin') THEN 'admin'
        WHEN u.senha = SHA1('administrator') THEN 'administrator'
        WHEN u.senha = SHA1('admin123') THEN 'admin123'
        WHEN u.senha = SHA1('root') THEN 'root'
        WHEN u.senha = SHA1('system') THEN 'system'
        
        -- Senhas relacionadas ao sistema
        WHEN u.senha = SHA1('lettore') THEN 'lettore'
        WHEN u.senha = SHA1('editor') THEN 'editor'
        WHEN u.senha = SHA1('jurid') THEN 'jurid'
        WHEN u.senha = SHA1('ebook') THEN 'ebook'
        WHEN u.senha = SHA1('livro') THEN 'livro'
        
        -- Padrões de teclado
        WHEN u.senha = SHA1('qwertyui') THEN 'qwertyui'
        WHEN u.senha = SHA1('asdfgh') THEN 'asdfgh'
        WHEN u.senha = SHA1('zxcvbn') THEN 'zxcvbn'
        
        -- Datas comuns
        WHEN u.senha = SHA1('2023') THEN '2023'
        WHEN u.senha = SHA1('2024') THEN '2024'
        WHEN u.senha = SHA1('2025') THEN '2025'
        
        -- Números sequenciais
        WHEN u.senha = SHA1('1111') THEN '1111'
        WHEN u.senha = SHA1('2222') THEN '2222'
        WHEN u.senha = SHA1('0000') THEN '0000'
        
        -- Senhas em português
        WHEN u.senha = SHA1('senha') THEN 'senha'
        WHEN u.senha = SHA1('senha123') THEN 'senha123'
        WHEN u.senha = SHA1('brasil') THEN 'brasil'
        
        ELSE 'NÃO IDENTIFICADA AINDA (mas ainda é SHA-1 quebrável)'
    END as senha_descoberta,
    CASE 
        WHEN u.senha = SHA1('123456') OR u.senha = SHA1('password') OR u.senha = SHA1('admin') THEN 'CRÍTICO - QUEBRADA EM SEGUNDOS'
        WHEN u.senha = SHA1('12345678') OR u.senha = SHA1('qwerty') THEN 'ALTO - QUEBRADA EM MINUTOS'
        ELSE 'MÉDIO - QUEBRADA EM HORAS/DIAS (ferramentas hashcat, john)'
    END as nivel_risco
FROM usuarios u
WHERE LENGTH(u.senha) = 40 
  AND u.senha REGEXP '^[a-f0-9]+$'
ORDER BY 
    CASE 
        WHEN u.senha = SHA1('123456') OR u.senha = SHA1('password') OR u.senha = SHA1('admin') THEN 1
        WHEN u.senha = SHA1('12345678') OR u.senha = SHA1('qwerty') THEN 2
        ELSE 3
    END,
    u.id;

-- 4. ANÁLISE DE PADRÕES DE USUÁRIO (possíveis senhas baseadas em dados pessoais)
SELECT 
    id,
    usuario,
    nome,
    senha,
    -- Testar se senha é baseada no próprio usuário
    CASE 
        WHEN senha = SHA1(usuario) THEN CONCAT('SENHA = USUÁRIO: ', usuario)
        WHEN senha = SHA1(LOWER(nome)) THEN CONCAT('SENHA = NOME: ', LOWER(nome))
        WHEN senha = SHA1(CONCAT(usuario, '123')) THEN CONCAT('SENHA = USUÁRIO+123: ', usuario, '123')
        WHEN senha = SHA1(CONCAT(usuario, '1')) THEN CONCAT('SENHA = USUÁRIO+1: ', usuario, '1')
        ELSE 'Não baseada em dados óbvios do usuário'
    END as analise_padrao
FROM usuarios
WHERE LENGTH(senha) = 40;

-- 5. QUERY PARA DEMONSTRAÇÃO EM TEMPO REAL (EXECUTAR DURANTE APRESENTAÇÃO)
-- Esta query mostra o quão rápido um hash SHA-1 pode ser calculado
SELECT 
    'Demonstração de vulnerabilidade SHA-1' as titulo,
    SHA1('123456') as hash_de_123456,
    '770f64a50ce6d5474925d1c65b8def8f8fb75404' as hash_encontrado_no_sistema,
    CASE 
        WHEN SHA1('123456') = '770f64a50ce6d5474925d1c65b8def8f8fb75404' THEN '🚨 SENHA QUEBRADA: 123456'
        ELSE 'Hash não corresponde'
    END as resultado_quebra,
    'Em produção, atacantes usam rainbow tables e ferramentas como hashcat para quebrar milhões de hashes por segundo' as contexto_seguranca;

-- 6. ESTATÍSTICAS PARA APRESENTAÇÃO
SELECT 
    'RESUMO EXECUTIVO DA VULNERABILIDADE' as categoria,
    COUNT(*) as total_usuarios,
    COUNT(CASE WHEN LENGTH(senha) = 40 THEN 1 END) as usuarios_sha1,
    ROUND((COUNT(CASE WHEN LENGTH(senha) = 40 THEN 1 END) / COUNT(*)) * 100, 2) as percentual_vulneravel,
    '100% das senhas podem ser quebradas' as impacto,
    'SHA-1 foi quebrado pela Google em 2017' as contexto_tecnico,
    'Recomenda-se Argon2ID ou bcrypt com custo 12+' as solucao_recomendada
FROM usuarios;

-- 7. QUERY PARA GERAR RELATÓRIO DE MIGRAÇÃO
SELECT 
    COUNT(*) as usuarios_para_migrar,
    'Todos os usuários devem redefinir senhas' as acao_necessaria,
    'Implementar password_hash() PHP com PASSWORD_ARGON2ID' as implementacao_tecnica,
    'Forçar reset no próximo login' as processo_migracao,
    CURDATE() as data_analise,
    'CRÍTICO - Ação imediata requerida' as prioridade
FROM usuarios 
WHERE LENGTH(senha) = 40;

-- =================================================================
-- ⚠️ IMPORTANTE: 
-- Estas queries são para demonstração da vulnerabilidade.
-- Após a apresentação, implemente imediatamente:
-- 1. Migração para bcrypt/Argon2ID
-- 2. Reset forçado de senhas
-- 3. Política de senhas robusta
-- 4. Monitoramento de segurança
-- =================================================================