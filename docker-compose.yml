services:
  # Aplicação PHP com Swoole
  lettore-app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: lettore-app
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      # Código da aplicação
      - .:/var/www/html
      # Volumes persistentes para dados
      - obras_montadas:/var/www/html/obras_montadas
      - indices_montados:/var/www/html/indices_montados
      - capas_temp:/var/www/html/capas_temp
      - downloads:/var/www/html/downloads
      - logs:/var/www/html/logs
      # Configurações específicas do Docker
      - ./docker/swoole-server.php:/var/www/html/swoole-server.php
    command: php /var/www/html/docker/swoole-server.php
    environment:
      # Configurações do banco de dados para desenvolvimento local
      - DB_HOST=lettore-db
      - DB_PORT=3306
      - DB_USERNAME=lettore_user
      - DB_PASSWORD=lettore_password
      - DB_ENGINE=mysql
      - DB_CHARSET=utf8
      - DB_COLLATION=utf8_general_ci
      - DB_TIMEOUT=30
      - DB_MAX_CONNECTIONS=100
      # Configurações da aplicação para desenvolvimento
      - APP_ENV=development
      - APP_DEBUG=true
      - SWOOLE_WORKERS=2
      - SWOOLE_MAX_REQUESTS=1000
      # Timezone
      - TZ=America/Sao_Paulo
    networks:
      - lettore-network
    depends_on:
      - lettore-db
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9501/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Banco de dados MySQL para desenvolvimento
  lettore-db:
    image: mysql:8.0.35
    container_name: lettore-db
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: example
      MYSQL_DATABASE: lettore_dev
      MYSQL_USER: lettore_user
      MYSQL_PASSWORD: lettore_password
      TZ: America/Sao_Paulo
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - lettore-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "lettore_user", "-plettore_password"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Nginx como proxy reverso
  lettore-nginx:
    image: nginx:1.24-alpine
    container_name: lettore-nginx
    restart: unless-stopped
    ports:
      - "8080:80"
      - "443:443"
    volumes:
      # Configurações do Nginx
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      # Arquivos estáticos (para servir diretamente)
      - ./media:/var/www/html/media:ro
      - ./tenants:/var/www/html/tenants:ro
      # Logs
      - nginx_logs:/var/log/nginx
    environment:
      - TZ=America/Sao_Paulo
    networks:
      - lettore-network
    depends_on:
      - lettore-app
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis para cache e sessões
  redis:
    image: redis:7-alpine
    container_name: lettore-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    environment:
      - TZ=America/Sao_Paulo
    networks:
      - lettore-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  obras_montadas:
  indices_montados:
  capas_temp:
  downloads:
  logs:
  redis_data:
  nginx_logs:
  mysql_data:

# Rede personalizada
networks:
  lettore-network:
