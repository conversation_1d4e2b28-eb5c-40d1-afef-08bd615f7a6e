# Sistema de Gestão de E-books

## Sobre o Projeto
Este é um sistema web desenvolvido em PHP para gerenciamento e processamento de e-books. O sistema permite o upload, processamento e gerenciamento de obras digitais, incluindo funcionalidades para sumário, marcações e processamento de PDF.

## Funcionalidades Principais

### 1. Gestão de Obras
- Upload de PDFs
- Cadastro de informações básicas (título, autor, ISBN, etc.)
- Controle de status (rascunho, publicado, em venda)
- Visualização em lista com informações detalhadas

### 2. Processamento de Obras
- Conversão de PDF para formato processável
- Sistema de "mastigação" de conteúdo
- Geração de sumário automático
- Processamento de marcações e grifos
- Integração com ChatGPT para processamento de conteúdo

### 3. Gerenciamento de Conteúdo
- Editor de sumário
- Sistema de marcações
- Controle de níveis de conteúdo
- Gestão de páginas individuais

### 4. Recursos Técnicos
- Sistema multi-tenant
- Gestão de uploads por tenant
- Processamento em background
- Sistema de compactação/descompactação de obras
- Controle de versões e revisões

## Estrutura do Projeto

### Diretórios Principais
- `/views`: Controladores do sistema
- `/templates`: Templates HTML usando sistema de templates
- `/media`: Arquivos estáticos (JS, CSS)
- `/docs`: Documentação e schemas
- `/vendor`: Dependências (via Composer)

### Rotas Principais
- `/obras`: Listagem de obras
- `/obra/cadastro`: Cadastro de novas obras
- `/obra/mastigar`: Processamento de obras
- `/obra/sumario`: Gestão de sumário
- `/obra/publicar`: Publicação de obras

## Requisitos Técnicos
- PHP 7+
- MySQL/MariaDB
- Composer para gerenciamento de dependências
- Servidor web (Apache/Nginx)
- Suporte a processamento de PDF

## Dependências Principais
- AWS SDK para PHP
- Sistema de templates
- Bibliotecas de processamento de PDF

## Configuração do Ambiente
1. Clone o repositório
2. Execute `composer install`
3. Configure o banco de dados usando `docs/schema_montagem.sql`
4. Configure as variáveis de ambiente
5. Configure os diretórios de upload

## Fluxo de Processamento de Obras
1. Upload do PDF
2. Processamento inicial ("mastigação")
3. Geração de sumário
4. Processamento de marcações
5. Revisão e ajustes
6. Publicação

## Manutenção
- Backup regular do banco de dados
- Monitoramento do processamento de obras
- Gestão de espaço em disco para uploads
- Monitoramento de logs de erro

## Segurança
- Autenticação obrigatória
- Controle de acesso por tenant
- Validação de uploads
- Sanitização de dados

## Suporte
Para questões técnicas ou suporte, entre em contato com a equipe de desenvolvimento.