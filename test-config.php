<?php

/**
 * Script de teste para validar migração de configurações
 * Verifica se ConfigManager e SwooleBootstrap estão funcionando corretamente
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== TESTE DE CONFIGURAÇÕES LETTORE ===\n\n";

// Simular ambiente para teste
$_SERVER['HTTP_HOST'] = 'editor.ltr.lettore.com.br';
$_SERVER['DOCUMENT_ROOT'] = __DIR__;
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['REQUEST_URI'] = '/test';

try {
    echo "1. Testando ConfigManager...\n";
    
    require_once __DIR__ . '/classes/ConfigManager.php';
    
    $configManager = ConfigManager::getInstance();
    $configManager->initialize();
    
    $settings = $configManager->getSettings();
    $tenant = $configManager->getTenant();
    
    echo "   ✓ ConfigManager inicializado\n";
    echo "   ✓ Settings carregados: " . (is_array($settings) ? "SIM" : "NÃO") . "\n";
    echo "   ✓ Tenant carregado: " . (is_array($tenant) ? "SIM" : "NÃO") . "\n";
    
    if ($settings) {
        echo "   ✓ Versão: " . ($settings['version'] ?? 'N/A') . "\n";
    }
    
    if ($tenant) {
        echo "   ✓ Tenant: " . ($tenant['tenant'] ?? 'N/A') . "\n";
        echo "   ✓ Nome: " . ($tenant['nome'] ?? 'N/A') . "\n";
    }
    
    echo "\n2. Testando compatibilidade com variáveis globais...\n";
    
    $configManager->setGlobalVars();
    
    global $SETTINGS, $TENANT;
    
    echo "   ✓ \$SETTINGS global: " . (isset($SETTINGS) ? "SIM" : "NÃO") . "\n";
    echo "   ✓ \$TENANT global: " . (isset($TENANT) ? "SIM" : "NÃO") . "\n";
    
    echo "\n3. Testando classe Geral...\n";
    
    require_once __DIR__ . '/classes/geral.class.php';
    
    $geral = new Geral(false, false);
    
    echo "   ✓ Classe Geral instanciada\n";
    echo "   ✓ Settings na classe: " . (is_array($geral->settings) ? "SIM" : "NÃO") . "\n";
    echo "   ✓ Tenant na classe: " . ($geral->tenant ? "SIM" : "NÃO") . "\n";
    
    if ($geral->settings && isset($geral->settings['tenant'])) {
        echo "   ✓ Tenant nas settings: " . ($geral->settings['tenant']['tenant'] ?? 'N/A') . "\n";
    }
    
    echo "\n4. Testando SwooleBootstrap...\n";
    
    require_once __DIR__ . '/classes/SwooleBootstrap.php';
    
    echo "   ✓ Ambiente Swoole: " . (SwooleBootstrap::isSwooleEnvironment() ? "SIM" : "NÃO") . "\n";
    
    if (SwooleBootstrap::isSwooleEnvironment()) {
        $info = SwooleBootstrap::getSwooleInfo();
        echo "   ✓ Versão Swoole: " . ($info['version'] ?? 'N/A') . "\n";
        echo "   ✓ Inicializado: " . ($info['initialized'] ? "SIM" : "NÃO") . "\n";
        echo "   ✓ PID: " . ($info['process_id'] ?? 'N/A') . "\n";
        echo "   ✓ Memória: " . round(($info['memory_usage'] ?? 0) / 1024 / 1024, 2) . " MB\n";
    }
    
    echo "\n5. Testando mudança de host...\n";
    
    $_SERVER['HTTP_HOST'] = 'editor.hexag.lettore.com.br';
    
    $configManager->detectHostChange();
    
    $newTenant = $configManager->getTenant();
    echo "   ✓ Novo tenant: " . ($newTenant['tenant'] ?? 'N/A') . "\n";
    
    echo "\n6. Testando validação de configurações...\n";
    
    $isValid = $configManager->validateConfig();
    echo "   ✓ Configurações válidas: " . ($isValid ? "SIM" : "NÃO") . "\n";
    
    echo "\n=== TESTE CONCLUÍDO COM SUCESSO ===\n";
    
} catch (Exception $e) {
    echo "\n❌ ERRO: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
    exit(1);
} catch (Throwable $e) {
    echo "\n❌ ERRO FATAL: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
    exit(1);
}

echo "\n📊 ESTATÍSTICAS:\n";
echo "   • Uso de memória: " . round(memory_get_usage() / 1024 / 1024, 2) . " MB\n";
echo "   • Pico de memória: " . round(memory_get_peak_usage() / 1024 / 1024, 2) . " MB\n";
echo "   • Tempo de execução: " . round(microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'], 4) . "s\n";

?>