# Configurações do Lettore Editor Docker

# Arquitetura (amd64 ou arm64)
ARCH=auto

# Dockerfile a ser usado
DOCKERFILE=Dockerfile

# Ambiente (development, staging, production)
APP_ENV=production
APP_DEBUG=false

# Configurações do banco de dados
DB_HOST=lettorestage-db.cb0yc0yuuqot.us-west-2.rds.amazonaws.com
DB_PORT=3306
DB_USERNAME=admin
DB_PASSWORD=JASHDgvHAS%D$A412TFDSDASDJhgVADKJHASD

# Configurações do Swoole
SWOOLE_HOST=0.0.0.0
SWOOLE_PORT=9501
SWOOLE_WORKERS=4
SWOOLE_TASK_WORKERS=2
SWOOLE_MAX_REQUESTS=10000

# Configurações do Redis
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# Configurações do Nginx
NGINX_WORKER_PROCESSES=auto
NGINX_WORKER_CONNECTIONS=1024

# Configurações de timezone
TZ=America/Sao_Paulo

# Configurações de logs
LOG_LEVEL=info
LOG_CHANNEL=stack

# Configurações de cache
CACHE_DRIVER=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

# Configurações de upload
UPLOAD_MAX_SIZE=100M
POST_MAX_SIZE=100M

# Configurações de performance
PHP_MEMORY_LIMIT=512M
PHP_MAX_EXECUTION_TIME=300
PHP_MAX_INPUT_TIME=300

# Configurações de segurança
APP_KEY=base64:your-app-key-here
JWT_SECRET=your-jwt-secret-here

# Configurações de email (se necessário)
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls

# Configurações de monitoramento
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true

# Configurações específicas para ARM64
# (automaticamente detectadas pelo script de build)
ARM64_SWOOLE_VERSION=5.0.3
ARM64_OPTIMIZATIONS=true
