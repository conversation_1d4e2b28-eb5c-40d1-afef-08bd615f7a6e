<?php
/**
 * Teste das classes de conexão corrigidas para MySQL 8.0 + PHP 8.2
 */

require_once '/var/www/html/classes/conecta.docker.class.php';

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== TESTE CLASSES CORRIGIDAS MySQL 8.0 + PHP 8.2 ===\n\n";

// Configurar variáveis de ambiente
putenv('DB_HOST=lettore-db');
putenv('DB_PORT=3306');
putenv('DB_USERNAME=lettore_user');
putenv('DB_PASSWORD=lettore_password');
putenv('DB_NAME=lettore_dev');

echo "1. TESTANDO conecta.docker.class.php:\n";

try {
    // Testar classe conecta Docker
    $conecta = new conecta();
    
    echo "✓ Instância criada com sucesso\n";
    echo "Tenant detectado: {$conecta->tenant}\n";
    echo "Database: {$conecta->database}\n";
    
    // Testar query básica
    $result = $conecta->query("SELECT VERSION() as version, @@character_set_connection as charset");
    
    if ($result) {
        echo "✓ Query executada com sucesso\n";
        $row = $conecta->fetch_assoc($result);
        if ($row) {
            echo "MySQL Version: " . $row['version'] . "\n";
            echo "Charset Connection: " . $row['charset'] . "\n";
        }
    } else {
        echo "❌ Erro na query\n";
    }
    
    // Testar escape_string
    $test_string = "São Paulo - Ação 'teste' & \"quotes\"";
    $escaped = $conecta->escape_string($test_string);
    echo "String original: {$test_string}\n";
    echo "String escaped: {$escaped}\n";
    
    // Testar transação (se disponível)
    echo "\n2. TESTANDO FUNCIONALIDADES AVANÇADAS:\n";
    
    // Criar tabela temporária para teste
    $create_table = $conecta->query("
        CREATE TEMPORARY TABLE test_compat (
            id INT AUTO_INCREMENT PRIMARY KEY,
            nome VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    
    if ($create_table) {
        echo "✓ Tabela temporária criada\n";
        
        // Inserir dados de teste
        $insert_data = $conecta->query("
            INSERT INTO test_compat (nome) VALUES 
            ('São Paulo'),
            ('Configuração'),
            ('Test 👍 🚀 💯'),
            ('Coração ♥')
        ");
        
        if ($insert_data) {
            echo "✓ Dados inseridos com sucesso\n";
            echo "Linhas afetadas: " . $conecta->affected_rows() . "\n";
            echo "Último ID: " . $conecta->insert_id() . "\n";
            
            // Recuperar dados
            $select_data = $conecta->query("SELECT * FROM test_compat ORDER BY id");
            if ($select_data) {
                echo "✓ Dados recuperados:\n";
                while ($row = $conecta->fetch_assoc($select_data)) {
                    echo "  ID: {$row['id']} | Nome: {$row['nome']} | Data: {$row['data_criacao']}\n";
                }
                $conecta->free_result($select_data);
            }
        } else {
            echo "❌ Erro ao inserir dados\n";
        }
    } else {
        echo "❌ Erro ao criar tabela temporária\n";
    }
    
    // Testar health check
    echo "\n3. HEALTH CHECK:\n";
    $health = conecta::healthCheck();
    echo "Conexões no pool: {$health['total']}\n";
    echo "Conexões saudáveis: {$health['healthy']}\n";
    echo "Conexões atuais: {$health['current']}\n";
    
} catch (Exception $e) {
    echo "❌ ERRO CRÍTICO: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== TESTE COMPLETO ===\n";
?>