<?php

# Carregar configurações primeiro
require_once 'classes/comum.inc.php';
require_once 'classes/geral.class.php';
$app = new Geral();

# Rotas Gerais
$urls = [

    "/"                                                 =>  "\Views\Login\Login",
    "/login/autenticar"                                 =>  "\Views\Login\Autenticar",
    "/logout"                                           =>  "\Views\Login\Logout",

    "/obras"                                            =>  "\Views\Obras\Listar",
    "/obras/listar"                                     =>  "\Views\Obras\Listar",
    
    "/obra/aupdate"					=>  "\Views\Obras\Aupdate",
    "/obra/cadastro"                                    =>  "\Views\Obras\Cadastro",
    "/obra/cadastro/(?P<idobra>\d+)"                    =>  "\Views\Obras\Cadastro",

    
    "/obra/ajustar/(?P<idobra>\d+)"                     =>  "\Views\Obras\Ajustar",
    
    # Ajustar
    "/obra/pagina"                                      =>  "\Views\Obra\Pagina",
    "/obra/sumario/(?P<idobra>\d+)"                     =>  "\Views\Obra\Sumario",

    # Processos
    "/obra/mastigar/?(?P<idobra>\d+)?"                  => "\Views\Etapas\Mastigar",
    "/obra/tratar/?(?P<idobra>\d+)?"                    => "\Views\Etapas\Tratar",
    "/obra/gerar/?(?P<idobra>\d+)?"                     => "\Views\Etapas\Gerar",
    "/obra/pdf/?(?P<idobra>\d+)?"			=> "\Views\Etapas\Pdf",
    "/obra/sumarizar/?(?P<idobra>\d+)?"                 => "\Views\Etapas\Sumarizar",
    "/obra/jbe/(?P<idobra>\d+)"                         => "\Views\Etapas\JBE",

    "/obra/descompactar/(?P<idobra>\d+)"                         => "\Views\Obras\Descompactar",
    "/obra/compactar/(?P<idobra>\d+)"                         => "\Views\Obras\Compactar",
    "/obra/resetar/(?P<idobra>\d+)"                         => "\Views\Obras\Resetar",

    "/obra/imagens/(?P<idobra>\d+)"			    => "\Views\Obras\Imagens",
    "/obras/chatgpt/(?P<idobra>\d+)"			    => "\Views\Obras\ChatGPT",

    "/obra/excluir"					=> "\Views\Obras\Excluir",

    "/obra/download/(?P<idobra>\d+)"                         => "\Views\Obras\Download",


    "/obra/publicar/(?P<idobra>\d+)/(?P<status>\d+)"    => "\Views\Etapas\Publicar",
    "/obra/remontar/(?P<idobra>\d+)"                    => "\Views\Etapas\Remontar",


    "/obras/todas/?"                                    => "\Views\Obras\Todas", 

    "/teste"                                            => "\Views\Obras\Teste", 

    "/lightbox-nivel"                             => "modals/sumario.html", 
    "/lightbox-palavra"                           => "modals/palavra.html", 
    "/lightbox-marcacao"                          => "\Views\Obra\LightboxMarcacao", 

];


# Middlewares - Verificar se já foram carregados (para compatibilidade com Swoole)
if (!function_exists('Middlewares\is_auth')) {
    include("middlewares/before.php");
}

# Includando views - Verificar se já foram carregados (para compatibilidade com Swoole)
if (!class_exists('\Views\Login\Login')) {
    include("views/login.php");
    include("views/obras.php");
    include("views/obra.php");
    include("views/etapas.php");
}

$app->dispatch($urls);

?>

