#!/bin/bash

# Script de inicialização para o container Lettore

echo "=== Iniciando Lettore Editor ==="

# Verificar se as variáveis de ambiente estão definidas
if [ -z "$DB_HOST" ]; then
    echo "ERRO: DB_HOST não definido"
    exit 1
fi

echo "Configurações:"
echo "- Ambiente: ${APP_ENV:-production}"
echo "- Debug: ${APP_DEBUG:-false}"
echo "- DB Host: ${DB_HOST}"
echo "- Swoole Workers: ${SWOOLE_WORKERS:-2}"

# Aguardar o banco de dados estar disponível
echo "Aguardando banco de dados..."
while ! mysqladmin ping -h"$DB_HOST" -u"$DB_USERNAME" -p"$DB_PASSWORD" --silent; do
    echo "Banco de dados não disponível, aguardando..."
    sleep 2
done
echo "Banco de dados disponível!"

# Instalar dependências se necessário
if [ ! -d "vendor" ] || [ ! -f "vendor/autoload.php" ]; then
    echo "Instalando dependências do Composer..."
    composer install --no-dev --optimize-autoloader
fi

# Criar diretórios necessários
echo "Criando diretórios necessários..."
mkdir -p obras_montadas indices_montados capas_temp downloads logs

# Ajustar permissões
echo "Ajustando permissões..."
chown -R www-data:www-data obras_montadas indices_montados capas_temp downloads logs
chmod -R 755 obras_montadas indices_montados capas_temp downloads logs

# Iniciar servidor Swoole
echo "Iniciando servidor Swoole..."
exec php /var/www/html/docker/swoole-server.php
