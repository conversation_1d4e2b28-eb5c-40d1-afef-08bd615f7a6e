# Configuração para desenvolvimento local
server {
    listen 80;
    server_name localhost;
    
    # Logs específicos
    access_log /var/log/nginx/localhost_access.log main;
    error_log /var/log/nginx/localhost_error.log warn;
    
    # Arquivos estáticos - servir diretamente pelo Nginx
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
        
        # Tentar servir arquivo estático, senão proxy para PHP
        try_files $uri @php;
    }
    
    # Media files
    location /media/ {
        root /var/www/html;
        expires 1y;
        add_header Cache-Control "public";
        try_files $uri @php;
    }
    
    # Tenant specific media
    location /tenants/ {
        root /var/www/html;
        expires 1y;
        add_header Cache-Control "public";
        try_files $uri @php;
    }
    
    # Health check
    location /health {
        proxy_pass http://php_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Uploads e downloads
    location /uploads/ {
        client_max_body_size 500M;
        proxy_pass http://php_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
    }
    
    # Todas as outras requisições para PHP
    location / {
        try_files $uri @php;
    }
    
    # Proxy para PHP Swoole
    location @php {
        proxy_pass http://php_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Connection "";
        proxy_http_version 1.1;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }
    
    # Negar acesso a arquivos sensíveis
    location ~ /\. {
        deny all;
    }
    
    location ~ /(classes|vendor|docs|scripts)/ {
        deny all;
    }
    
    location ~ \.(sql|log|ini)$ {
        deny all;
    }
}
