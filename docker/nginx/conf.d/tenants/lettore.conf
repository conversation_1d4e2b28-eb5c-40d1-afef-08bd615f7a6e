# Configuração para todos os subdomínios *.lettore.com.br
server {
    listen 80;
    server_name *.lettore.com.br lettore.com.br;
    
    # Logs específicos
    access_log /var/log/nginx/lettore_access.log main;
    error_log /var/log/nginx/lettore_error.log warn;
    
    # Configurações de segurança
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Rate limiting para login
    location /login {
        limit_req zone=login burst=10 nodelay;
        proxy_pass http://php_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Rate limiting para API
    location /api {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://php_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Arquivos estáticos - servir diretamente pelo Nginx
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
        
        # Tentar servir arquivo estático, senão proxy para PHP
        try_files $uri @php;
    }
    
    # Media files
    location /media/ {
        expires 1y;
        add_header Cache-Control "public";
        try_files $uri @php;
    }
    
    # Tenant specific media
    location /tenants/ {
        expires 1y;
        add_header Cache-Control "public";
        try_files $uri @php;
    }
    
    # Uploads e downloads
    location /uploads/ {
        client_max_body_size 500M;
        proxy_pass http://php_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
    }
    
    # Todas as outras requisições para PHP
    location / {
        try_files $uri @php;
    }
    
    # Proxy para PHP Swoole
    location @php {
        proxy_pass http://php_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Connection "";
        proxy_http_version 1.1;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }
    
    # Negar acesso a arquivos sensíveis
    location ~ /\. {
        deny all;
    }
    
    location ~ /(classes|vendor|docs|scripts)/ {
        deny all;
    }
    
    location ~ \.(sql|log|ini)$ {
        deny all;
    }
}
