# Configurações do ambiente de desenvolvimento
# Use este arquivo para desenvolvimento local com Docker

# Arquitetura (auto-detectada)
ARCH=auto

# Dockerfile para desenvolvimento
DOCKERFILE=Dockerfile.dev

# Ambiente de desenvolvimento
APP_ENV=development
APP_DEBUG=true

# Configurações do banco de dados local
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=lettore_dev
DB_USERNAME=lettore_user
DB_PASSWORD=lettore_pass_2024

# Configurações do Swoole para desenvolvimento
SWOOLE_HOST=0.0.0.0
SWOOLE_PORT=9501
SWOOLE_WORKERS=2
SWOOLE_TASK_WORKERS=1
SWOOLE_MAX_REQUESTS=1000

# Configurações do Redis local
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# Configurações de timezone
TZ=America/Sao_Paulo

# Configurações de logs para desenvolvimento
LOG_LEVEL=debug
LOG_CHANNEL=daily

# Configurações de cache para desenvolvimento
CACHE_DRIVER=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

# Configurações de upload
UPLOAD_MAX_SIZE=100M
POST_MAX_SIZE=100M

# Configurações de performance para desenvolvimento
PHP_MEMORY_LIMIT=512M
PHP_MAX_EXECUTION_TIME=300
PHP_MAX_INPUT_TIME=300

# Configurações de segurança (desenvolvimento)
APP_KEY=base64:dev-key-lettore-2024
JWT_SECRET=dev-jwt-secret-lettore

# Configurações de monitoramento
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=false

# Hot reload habilitado para desenvolvimento
HOT_RELOAD=true
OPCACHE_VALIDATE_TIMESTAMPS=1
