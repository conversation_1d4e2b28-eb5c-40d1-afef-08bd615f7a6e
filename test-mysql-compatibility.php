<?php
/**
 * Script de teste de compatibilidade MySQL 5.7 → 8.0 
 * e PHP 5 → 8.2 para Editor Lettore
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== TESTE DE COMPATIBILIDADE MySQL 8.0 + PHP 8.2 ===\n\n";

// 1. Verificar extensões PHP necessárias
echo "1. VERIFICANDO EXTENSÕES PHP:\n";
$required_extensions = ['mysqli', 'mbstring', 'json', 'iconv'];
$missing = [];

foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "   ✓ {$ext}: " . phpversion($ext) . "\n";
    } else {
        echo "   ❌ {$ext}: NÃO ENCONTRADA\n";
        $missing[] = $ext;
    }
}

if (!empty($missing)) {
    die("\n❌ ERRO: Extensões faltando: " . implode(', ', $missing) . "\n");
}

// 2. Verificar MySQLi features para MySQL 8.0
echo "\n2. VERIFICANDO RECURSOS MySQLi:\n";
$mysqli_info = [
    'Client version' => mysqli_get_client_version(),
    'Client info' => mysqli_get_client_info()
];

foreach ($mysqli_info as $key => $value) {
    echo "   {$key}: {$value}\n";
}

// Verificar suporte a caching_sha2_password (MySQL 8.0 default)
$supports_sha2 = version_compare(mysqli_get_client_info(), '5.7', '>=');
echo "   SHA2 Password Support: " . ($supports_sha2 ? "✓ Sim" : "❌ Não") . "\n";

// 3. Testar conexão com diferentes configurações
echo "\n3. TESTANDO CONFIGURAÇÕES DE CONEXÃO:\n";

// Configurações de teste (usando variáveis de ambiente ou defaults)
$test_configs = [
    'local' => [
        'host' => $_ENV['DB_HOST'] ?? 'localhost',
        'port' => $_ENV['DB_PORT'] ?? 3306,
        'user' => $_ENV['DB_USERNAME'] ?? 'root',
        'pass' => $_ENV['DB_PASSWORD'] ?? '',
        'db' => $_ENV['DB_NAME'] ?? 'test'
    ]
];

foreach ($test_configs as $config_name => $config) {
    echo "\n   Testando configuração: {$config_name}\n";
    
    try {
        // Testar conexão básica
        $mysqli = new mysqli(
            $config['host'],
            $config['user'], 
            $config['pass'],
            $config['db'],
            $config['port']
        );
        
        if ($mysqli->connect_error) {
            echo "   ❌ Erro de conexão: " . $mysqli->connect_error . "\n";
            continue;
        }
        
        echo "   ✓ Conexão estabelecida\n";
        
        // Verificar versão do servidor
        $version_result = $mysqli->query("SELECT VERSION() as version");
        if ($version_result) {
            $version_row = $version_result->fetch_assoc();
            echo "   MySQL Server Version: " . $version_row['version'] . "\n";
            
            // Verificar se é MySQL 8.0+
            $is_mysql8 = strpos($version_row['version'], '8.') === 0;
            echo "   MySQL 8.0+: " . ($is_mysql8 ? "✓ Sim" : "⚠ Não") . "\n";
        }
        
        // Testar charset UTF8MB4
        $charset_test = $mysqli->set_charset('utf8mb4');
        echo "   UTF8MB4 Support: " . ($charset_test ? "✓ Sim" : "❌ Não") . "\n";
        
        if ($charset_test) {
            $charset_result = $mysqli->query("SELECT @@character_set_connection as charset");
            if ($charset_result) {
                $charset_row = $charset_result->fetch_assoc();
                echo "   Charset atual: " . $charset_row['charset'] . "\n";
            }
        }
        
        // Testar queries básicas compatíveis com MySQL 8.0
        echo "   Testando queries MySQL 8.0:\n";
        
        $test_queries = [
            "SELECT 1 as test" => "Consulta básica",
            "SELECT NOW() as current_time" => "Função de tempo", 
            "SELECT @@version_comment as version_comment" => "Comentário da versão",
            "SHOW VARIABLES LIKE 'default_authentication_plugin'" => "Plugin de autenticação padrão"
        ];
        
        foreach ($test_queries as $query => $description) {
            $result = $mysqli->query($query);
            if ($result) {
                echo "     ✓ {$description}\n";
                if ($query === "SHOW VARIABLES LIKE 'default_authentication_plugin'") {
                    $row = $result->fetch_assoc();
                    if ($row) {
                        echo "       Plugin: " . $row['Value'] . "\n";
                    }
                }
            } else {
                echo "     ❌ {$description}: " . $mysqli->error . "\n";
            }
        }
        
        // Testar transações
        echo "   Testando transações:\n";
        $mysqli->autocommit(false);
        $mysqli->begin_transaction();
        $insert_test = $mysqli->query("CREATE TEMPORARY TABLE test_trans (id INT PRIMARY KEY, name VARCHAR(50))");
        if ($insert_test) {
            $mysqli->query("INSERT INTO test_trans VALUES (1, 'test')");
            $mysqli->commit();
            echo "     ✓ Transações funcionando\n";
        } else {
            $mysqli->rollback();
            echo "     ❌ Erro em transações: " . $mysqli->error . "\n";
        }
        $mysqli->autocommit(true);
        
        $mysqli->close();
        
    } catch (Exception $e) {
        echo "   ❌ Exceção: " . $e->getMessage() . "\n";
    }
}

// 4. Testar encoding/charset compatibilidade
echo "\n4. TESTANDO ENCODING/CHARSET:\n";

$test_strings = [
    'ASCII' => 'Hello World',
    'UTF-8' => 'Olá Mundo - Ação Coração',
    'UTF-8 Emoji' => 'Test 👍 🚀 💯',
    'Portuguese' => 'Configuração São Paulo'
];

foreach ($test_strings as $type => $string) {
    echo "   {$type}:\n";
    echo "     Original: {$string}\n";
    echo "     Encoding: " . mb_detect_encoding($string) . "\n";
    echo "     UTF-8 Valid: " . (mb_check_encoding($string, 'UTF-8') ? '✓' : '❌') . "\n";
    
    // Simular processo de escape_string da aplicação
    if (function_exists('mysqli_real_escape_string')) {
        // Precisamos de uma conexão para teste
        $temp_mysqli = @new mysqli('localhost', 'root', '', 'information_schema');
        if (!$temp_mysqli->connect_error) {
            $escaped = $temp_mysqli->real_escape_string($string);
            echo "     Escaped: {$escaped}\n";
            $temp_mysqli->close();
        }
    }
    echo "\n";
}

// 5. Verificar problemas conhecidos MySQL 8.0
echo "5. VERIFICANDO PROBLEMAS CONHECIDOS MySQL 8.0:\n";

$known_issues = [
    'sql_mode' => "Verificar sql_mode (MySQL 8.0 mais restritivo)",
    'reserved_words' => "Palavras reservadas (rank, lead, etc.)",  
    'group_by' => "GROUP BY requer ONLY_FULL_GROUP_BY por padrão",
    'password_validation' => "Política de senhas mais rígida",
    'auth_plugin' => "caching_sha2_password como padrão"
];

foreach ($known_issues as $issue => $description) {
    echo "   ⚠ {$issue}: {$description}\n";
}

// 6. Recomendações
echo "\n6. RECOMENDAÇÕES PARA MIGRAÇÃO:\n";
$recommendations = [
    "Atualizar configurações de charset para utf8mb4",
    "Verificar e corrigir queries com GROUP BY",  
    "Testar todas as stored procedures",
    "Atualizar configurações de autenticação",
    "Implementar tratamento de erro adequado",
    "Configurar sql_mode apropriado",
    "Testar performance com novos índices",
    "Backup completo antes da migração"
];

foreach ($recommendations as $i => $rec) {
    echo "   " . ($i + 1) . ". {$rec}\n";
}

echo "\n=== TESTE CONCLUÍDO ===\n";
?>