<?php

namespace Views\Obra;

class Sumario {

      function GET($matches, $app) {
     
# Filtros
          $idobra = $matches["idobra"];
#file_put_contents("debug.log", "\nSumario e1: ".$idobra, FILE_APPEND);
          $obras = new \Obras();
          $obras->filtroID = $idobra;
          $obras->listarObrasObjetos();

          $obra =  $obras->lista[0];


	  $texto = '';
	   $sumarios = $obras->listarSumario( $obra->id );
         if(!count($sumarios) || (isset($_REQUEST['dev']) && $_REQUEST['dev'] == 1)) {
	  	 $template = $app->template("sumario-dev.html");
	      $template->set("sumarios",  $sumarios);
	  	if($obra->sumario_inicio && $obra->sumario_fim){
	  	for($i=$obra->sumario_inicio;$i<=$obra->sumario_fim;$i++){

		$sumario_page = new \Obras();
		$sumario_page = $sumario_page->getPagina($i,$idobra);
	        $texto .= $sumario_page ? $sumario_page[0]['texto'] : '';	
		$TEXT = $texto;  
		//SCRIPT
		$sumario = $texto;
		$retorno = [];
		
		$TEXTDISPLAY = str_replace('</div>',PHP_EOL,$sumario);	
		$summary = explode(PHP_EOL,$TEXTDISPLAY);	
		$test = explode('..........................................................................................',$sumario);	
		$recorte = '';
		foreach($test as $key2 => $linha){	
			$linhex = str_replace('</div>','</div> ',$linha);	//PALAVRA		
			$linhex = str_replace('"',"'",strip_tags($linhex));
#			$linhex = str_replace('>','',$linhex);
			$recorte.= trim($linhex);
		}
			
		$recorte2 = explode('.....',$recorte);
		$recorte = '';	
		$retexto = '';
		$cont2 = 0;
		$lastpage = '';
		foreach($recorte2 as $key2 => $linha){
			if($linha){		
				$nivel = 1;				
				$linha2= trim(str_replace('.','',$linha));					
	#			$linha2 = str_replace('>','', $linha2);
				$recorte .= $linha2.' '.PHP_EOL;	
				if($cont2 == 0){
					$retexto = $linha2;
				}else{			
					$page = substr($linha2,0,3);
					
				
					if($lastpage == $page){
					$nivel= 2;
					}
					
					$lastpage = (int)$lastpage;
					$retorno[] = [
						'nivel'=>$nivel,
						'pagina'=>(int) $page,
						'indice'=>str_replace($lastpage,'',$retexto)
					];
					$retexto = $linha2;
					$lastpage = $page;
				}		
				$cont2++;
				
				if(strpos($linha,'????') && $i == $obra->sumario_fim){ //ULTIMA PAGINA
					$page = substr($linha2,-3);
					$retorno[] = [
						'nivel'=>$nivel,
						'pagina'=>(int) $page,
						'indice'=>str_replace($lastpage,'',$retexto)
					];
				}		
		      }
		}
		
	     
	     //DEV - AUTO SUMARIO GPT

	      if(isset($TEXT)){
		$tabelaINDICE = '<table style="width:100%;">
			<thead>
			<tr style="background:black;">
			    <th style="background:black;color:white;width:10%;">Nível</th>
			    <th style="background:black;color:white;width:10%;">Página</th>
			    <th style="background:black;color:white;">Índice</th>
			    <th style="width:8%;">&nbsp;</th>
			</tr>
			</thead>
			<tbody>';
			     if(count($retorno)){
				foreach($retorno as $key => $node){ 
					$tabelaINDICE.='<tr class="indexes" data-idobra="'.$obra->id.'">
						<td style="text-align:center;"><input type="number" value="'.$node['nivel'].'" style="color:red;background:#cdcdcd;height:55px;text-align:center;font-weight:bolder;" class="nivel form-control"/></td>
					<td style="text-align:center;font-weight:bolder;"><input type="text" value="'. $node['pagina'].'" style="background:#cdcdcd;height:55px;color:black;text-align:center;" class="pagina form-control"/></td>
					<td style="text-align:left;"><textarea style="color:black;" class="indice form-control">'. trim($node['indice']).'</textarea></td>
					<td>
<a href="#" class="btn btn-danger btn-sm removeIndex" title="Remove índice">remover linha</a><br/>
<a href="#" class="btn btn-success btn-sm addIndex" title="Novo Índice">+ linha</a>
		</td>
				    </tr>';
				}} 
			$tabelaINDICE.='</tbody>
		    </table><br/><br/> <a href="#" id="saveIndex" class="btn btn-primary">GERAR SUMÀRIO</a>
		<style>
		    table, th, td {
			border: 1px solid black;
			border-collapse: collapse;
		    }
		</style>';  
		$tabelaINDICE = '<div style="text-align:center;"><h1>CONFIGURE OS NÍVEIS DAS PÁGINAS ACIMA NA TABELA ABAIXO</h1>
		'.$tabelaINDICE.'</div>';
			$TEXTDISPLAY = str_replace('</div>',PHP_EOL,$TEXT);
			$TEXTDISPLAY = addslashes(strip_tags($TEXTDISPLAY));
		}

			  $template->set('recorte',trim($recorte));
			  $template->set('TEXTDISPLAY',$TEXTDISPLAY);
			  $template->set('TEXT',$TEXT);
			  $template->set('tabelaINDICE',$tabelaINDICE);


        	}}else{
			exit('PARA CONFIGURAR ESTE SUMÁRIO CADASTRE A PÁGINA DE INÍCIO E TERMINO DE SUMÁRIO');
        	}
	}else{
		  $template = $app->template("sumario.html");
		  if(strpos(json_encode($sumarios),'--SUMARIO_BASICO--') !== FALSE){
			$template->set('clearSummary','<a href="#" id="clearIndex" class="btn btn-danger" data-idobra="'.$obra->id.'">RESETAR SUMÀRIO</a>');
		  }
		  $template->set("sumarios",  $sumarios);
	}


        $template->set("obra", $obra);
	file_put_contents("debug.log", "\nSumario e2: ".$idobra, FILE_APPEND);

        echo $template->render();

     }
     
     function POST($matches, $app) {
     
     		$Obra = new \Obras();
     		if(isset($_REQUEST['clearIndex']) && $_REQUEST['clearIndex'] == 1){
     			$Obra->clearIndexSumario($matches['idobra']);
     		}else{
			$summary = $Obra->basicoSumario($matches['idobra'],$_REQUEST['indice'],$_REQUEST['nivel'],$_REQUEST['pagina']);
		}
		$retorno = ['msg'=>'success'];
		
     		$app->retornaJson(0, "", $retorno);
     }
}


class Pagina {

      function GET($matches, $app) {
          # Filtros
          $idobra = $_REQUEST["idobra"];
          $idpagina = $_REQUEST["pagina"];

          $obras = new \Obras();
          $obras->filtroID = $idobra;
          $obras->listarObras();

          $obra =  $obras->lista[0];

          # Calculando o nome da imagem, baseado na pagina.
          $numero_pagina_imagem = ($idpagina - $obra["paginaum"]) + 1;

          $obras = new \Obras();
          $pagina = $obras->paginaObra($idobra, $idpagina);

          # Aqui vou verificar se existe o arquivo da pagina localmente
          # Se existir, eu nao vou pegar do banco, porque dá maior trampo.

            # Suposta imagens
          $arr = array(  
                         sprintf("obra_Page_%s.png", $numero_pagina_imagem),
                         sprintf("obra_Page_%s.png", str_pad($numero_pagina_imagem, 4, "0", STR_PAD_LEFT)),
                         sprintf("obra_Page_%s.png", str_pad($numero_pagina_imagem, 3, "0", STR_PAD_LEFT)),
                         sprintf("obra_Page_%s.png", str_pad($numero_pagina_imagem, 2, "0", STR_PAD_LEFT))  
                      );

          $found = null;
          
          foreach($arr as $p):
            if( file_exists("../uploads/".$app->settings["tenant"]["tenant"]."/".$obra["id"]."/imagens/".$p)  ) $found = $p;
          endforeach;
/*
          echo '<pre>';
          print_r("../uploads/".$app->settings["tenant"]["tenant"]."/".$obra["id"]."/imagens/".$found);
	  
	  print_r($arr);
	  var_dump($found);
	
          exit();
*/


          $imagem = array("imagem" => '', "imagem_src" => '');

          if($found):
            $imagem["imagem_src"] = "uploads/".$app->settings["tenant"]["tenant"]."/".$obra["id"]."/imagens/".$found;
          else:

            $imagem = $obras->imagemObra($idobra, $numero_pagina_imagem);

            $imagem["imagem"] = base64_encode($imagem["imagem"]);

          endif;


          $retorno = array();
          $retorno["pagina"] = $pagina;
          $retorno["imagem"] = $imagem;





$retorno['pagina']['texto'] = iconv('UTF-8', 'UTF-8//IGNORE', $retorno['pagina']['texto']);














          # Termina o script
          $app->retornaJson(0, "", $retorno);

      }

      function POST($matches, $app) {


          $pagina = json_decode($_REQUEST["pagina"], true);

          $obras = new \Obras();

          $salvar = $obras->atualizaPaginaObra( $pagina["id"], $pagina["texto"] );

          $retorno["pagina"] = $pagina;

          # Termina o script
          $app->retornaJson(0, "", $retorno);

      }

}

class LightboxMarcacao {

      function GET($matches, $app) {


          $template = $app->template("modals/marcacao.html");

          if (isset($_GET["marcacao"])) $template->set("marcacao",  $_GET["marcacao"] );
          else $template->set("marcacao",  array("tipo" => "link") );

          echo $template->render();

        }

}



?>
