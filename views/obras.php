<?PHP

namespace Views\Obras;

class Teste {

    public $before = array("is_auth");

    function GET($matches, $app) {


      $obras = new \Obras();
      $obras->atualizaSumarioObra(10493);
      exit;

          $obras = new \Obras();
          $pagina = $obras->paginaObra(10493, 463);

          $obras = new \Obras();

          $salvar = $obras->atualizaPaginaObra( $pagina["id"], $pagina["texto"] );

          $retorno["pagina"] = $pagina;

          # Termina o script
          $app->retornaJson(0, "", $retorno);
    }

}


class Todas {

    public $before = array("is_auth");

    function GET($matches, $app) {

        $obras = new \Obras();
        $obras->order = "ORDER BY datalancamento DESC";
        $obras->listarObras();

        $template = $app->template("todas.html");
        $template->set("obras", $obras->lista);

        $arr = array();
        foreach($obras->lista as $obra):

            $arr[] = array(

                "id" => $obra["id"],
                "titulo" => $obra["titulo"],
                "ano" => $obra["ano"],
                "status" => $obra["status"],
                "data" => ConverteData($obra["datalancamento"]),
            );

        endforeach;


        $nomeArquivo = 'Ebooks - '.date('dmYHs');

        header('Content-Type: text/csv; charset=utf-8');
        header("Content-Disposition: attachment; filename=".$nomeArquivo.".csv");

        # Escrevendo o cabecalho
        echo "sep=,\n";
        echo \Encoding::toISO8859(implode(",", array_keys($arr[0]) ) );
        echo "\n";

        # Escrevendo cada tupa
        foreach($arr as $a):

        $linha = "";

        foreach($a as $v):
            $linha .= '"'.$v.'",';
        endforeach;

        $linha = trim($linha, ",");

        echo \Encoding::toISO8859($linha);
        echo "\n";

        endforeach;

        #echo $template->render();

    }

}

/*
 * Listar todas as obras na prateleira.
 *
 * Esta chamada é AJAX.
 *
 * @return html_lista: Template da lista de obras, para ser appendado no dashboard.
 * @return html_paginacao: Template dos botoes de paginação para ser appendado na dashboard.
 */
class Listar {


    # Before
    public $before = array("is_auth");

    function GET($matches, $app) {

        $template = $app->template("obras.html");
        echo $template->render();

    }

    function POST($matches, $app) {

        $data = $_REQUEST;


        # Inicia o tratamento da páginação
        $porpagina = isset($data["porpagina"]) ? $data["porpagina"] : 10;
        $pagina = isset($data["pagina"]) ? $data["pagina"] : 1;
        if($porpagina < 0) $porpagina = 4;


        $paginacao = retornaPaginacao($pagina, $porpagina);

        # Objeto de obras
        $obras = new \Obras();

        # Filtros
        $obras->filtroExibirCapa = false;
        $obras->filtroExibirInfo = false;

        $obras->filtroStatus = isset($data["status"]) ? $data["status"] : null;
        $obras->filtroEtapa = isset($data["etapa"]) ? $data["etapa"] : null;

        $obras->filtroBusca = isset($data["busca"]) ? $data["busca"] : null;


        if($data["grupo_status"] == 0)
            $obras->filtroEmMontagem = true;

        if($data["grupo_status"] == 1)
            $obras->filtroEmAguardo = true;

        if($data["grupo_status"] == 2):
            $obras->filtroEtapa = 10;
            $obras->order = 'ORDER by datapublicacao DESC, id DESC';
        endif;


        # Paginação
        $obras->registro_inicial = $paginacao["registro_inicial"] - 1;
        $obras->registro_quantidade = $paginacao["pagina_quantidade"];



        # Listando as obras.
        $obras->listarObras();


        # Dados da paginação
        $total_registros = $obras->registro_total;
        $total_paginas = ceil($total_registros/$porpagina);
        $registro_inicial = ($pagina-1) * $porpagina;
        $paginas = smart_pagination($total_registros, $porpagina, $pagina, 4);


        $obras_lista = $obras->lista;

        foreach($obras_lista as &$o) {

            if (!$o["qtdpaginas"]) $o["qtdpaginas"] = 1;

            if($o["etapa"] == 2) {
                $mastigador = ROOT_SYS . "/uploads/" . $app->settings["tenant"]["tenant"] ."/" . $o["id"] . "/mastigador.ini";
                if(file_exists($mastigador)) {
                    $content = file_get_contents($mastigador);
                    $o["mastigador"] = json_decode($content, true);
                    $o["mastigador"]["progress"] = number_format(($o["mastigador"]["pagina"] / $o["qtdpaginas"] ) * 100, 2);
                }
            }

            if($o["etapa"] == 4) {
                $tratador = ROOT_SYS . "/uploads/" . $app->settings["tenant"]["tenant"] ."/" . $o["id"] . "/tratador.ini";
                if(file_exists($tratador)) {
                    $content = file_get_contents($tratador);
                    $o["tratador"] = json_decode($content, true);
                }
            }

        }

        # Criando o template
        $template = $app->template("obras/lista.html");
        $template->set("sincronizador", verifica_processo_montagem() );

        $template->set("obras", $obras_lista);
        $template->set("filtros", $data);

        $template->set("paginas", $paginas);
        $template->set("total_paginas", $total_paginas);
        $template->set("total_registros", $total_registros);
        $template->set("proxima", $pagina + 1);
        $template->set("anterior", $pagina - 1);
        $template->set("current", $pagina);

        $t =  $template->render();

        $app->retornaJson(0, "Ok", array( "html" => $template->render() ));

    }

}


class Ajustar {

      function GET($matches, $app) {

        $template = $app->template("ajustar.html");

        $obras = new \Obras();
        $obras->filtroID = $matches["idobra"];
        $obras->filtroExibirCapa = true;
        $obras->listarObras();
        $obra =  $obras->lista[0];

	$template->set("obra", $obra);
	$template->set("tenant", $app);

        echo $template->render();

      }

      function POST($matches, $app) {

      }

}


class Aupdate{
	public $before = array("is_auth");

	function GET($matches, $app){
		$logado = $app->session['usuario_logado'];			
		$squery = sprintf("SELECT auto_update FROM usuarios where id = %d",$logado['id']);
		$Conecta = new \Conecta();
		$query = $Conecta->query($squery);
		$user = $query->fetchobj();
		exit($user[0]->auto_update);
	}

	function POST($matches, $app){
		$aupdate = $_REQUEST["aupdate"];
		$logado = $app->session['usuario_logado'];
		$squery = sprintf("UPDATE usuarios SET auto_update = '%s' WHERE id = %d", $aupdate, $logado['id']);
		$Conecta = new \Conecta();
		$query = $Conecta->query($squery);
		exit($query->result());
	}
}

class Cadastro {

    # Before
    public $before = array("is_auth");

    function GET($matches, $app) {	  
        
        $template = $app->template("cadastro.html");
	// Tentar buscar categorias da API com fallback para cache
	$cache_file_categorias = $_SERVER["DOCUMENT_ROOT"] . "/cache/categorias.json";
	$cache_file_autores = $_SERVER["DOCUMENT_ROOT"] . "/cache/autores.json";

	// Criar diretório de cache se não existir
	if (!file_exists(dirname($cache_file_categorias))) {
		mkdir(dirname($cache_file_categorias), 0755, true);
	}

	try {
		$api_autores = $app->acessaAPI("autores.listar2", [], true, false); // não lançar erro
		$api_categorias = $app->acessaAPI("categorias.listar2", [], true, false); // não lançar erro

		error_log("Cadastro Debug: API autores = " . print_r($api_autores, true));
		error_log("Cadastro Debug: API categorias = " . print_r($api_categorias, true));

		$autores_db = $api_autores['resultado'] ?? [];
		$lista_categorias = $api_categorias['resultado'] ?? [];

		// Salvar no cache se obtivemos dados válidos
		if (!empty($autores_db)) {
			file_put_contents($cache_file_autores, json_encode($autores_db));
		}
		if (!empty($lista_categorias)) {
			file_put_contents($cache_file_categorias, json_encode($lista_categorias));
		}

	} catch (Exception $e) {
		error_log("Cadastro Debug: Erro na API, tentando usar cache: " . $e->getMessage());

		// Usar cache se a API falhar
		$autores_db = [];
		$lista_categorias = [];

		if (file_exists($cache_file_autores)) {
			$autores_cache = json_decode(file_get_contents($cache_file_autores), true);
			if ($autores_cache) {
				$autores_db = $autores_cache;
				error_log("Cadastro Debug: Usando autores do cache");
			}
		}

		if (file_exists($cache_file_categorias)) {
			$categorias_cache = json_decode(file_get_contents($cache_file_categorias), true);
			if ($categorias_cache) {
				$lista_categorias = $categorias_cache;
				error_log("Cadastro Debug: Usando categorias do cache");
			}
		}

		// Fallback para categorias básicas se nem API nem cache funcionarem
		if (empty($lista_categorias)) {
			$lista_categorias = [
				['id' => 1, 'categoria' => 'Revistas Eletrônicas', 'idcategoria' => 0, 'omitir_em_listagem' => 0],
				['id' => 2, 'categoria' => 'Boletins e Informativos', 'idcategoria' => 0, 'omitir_em_listagem' => 0],
				['id' => 11, 'categoria' => 'Informativos', 'idcategoria' => 0, 'omitir_em_listagem' => 0],
				['id' => 12, 'categoria' => 'Previdência', 'idcategoria' => 0, 'omitir_em_listagem' => 0],
				['id' => 20, 'categoria' => 'Legislação', 'idcategoria' => 0, 'omitir_em_listagem' => 0]
			];
			error_log("Cadastro Debug: Usando categorias padrão (fallback)");
		}
	}

	error_log("Cadastro Debug: Autores processados = " . print_r($autores_db, true));
	error_log("Cadastro Debug: Categorias processadas = " . print_r($lista_categorias, true));

	if(is_array($lista_categorias) && count($lista_categorias)){
		foreach($lista_categorias as &$catdb) {
			$catdb['selected'] = false;
		}
		}

		if(is_array($autores_db) && count($autores_db)){
			foreach($autores_db as &$autordb) {
				$autordb['selected'] = false;
			}
		}

		// Opcoes de faixa, padrao, para cadastro novo (em caso de edicao as opcoes serao trazidas do banco)
		$opcoes_faixa['faixalancamento'] = "checked";
		$opcoes_faixa['faixaatualizada'] = "";
		$opcoes_faixa['faixaatualizadaprimeiro'] = "";
        if(isset($matches["idobra"])) {

            $obras = new \Obras();
            $obras->filtroID = $matches["idobra"];
            $obras->filtroExibirCapa = true;
            $obras->listarObras();
            $obra =  $obras->lista[0];
	    $obra['preco'] = number_format($obra['preco'], 2, ',', '.');
	    $opcoes_faixa = $app->acessaAPI("obra.listar_opcoes_faixa", array("idobra"=>$matches['idobra']))['resultado'];	   
		if (isset($opcoes_faixa[0])) $opcoes_faixa = $opcoes_faixa[0];
		## if (isset($opcoes_faixa['faixalancamento']) && $opcoes_faixa['faixalancamento'] == 1) $opcoes_faixa['faixalancamento'] = "checked";
 		// faixa lancamento deve vir checada/sim por padrao
 		if (isset($opcoes_faixa['faixalancamento']) && $opcoes_faixa['faixalancamento'] == 0) $opcoes_faixa['faixalancamento'] = "";
 		else $opcoes_faixa['faixalancamento'] = "checked";

		if (isset($opcoes_faixa['faixaatualizada']) && $opcoes_faixa['faixaatualizada'] == 1) $opcoes_faixa['faixaatualizada'] = "checked";
		if (isset($opcoes_faixa['faixaatualizadaprimeiro']) && $opcoes_faixa['faixaatualizadaprimeiro'] == 1) $opcoes_faixa['faixaatualizadaprimeiro'] = "checked";

			$pdf_defs = $app->acessaAPI("obra.pdf_defs", array("idobra"=>$matches['idobra']));
			$usarmarcadagua = 0;
			$usarcripto = 0;
			$usarmarcadaguacripto = "";
			if (isset($pdf_defs['resultado'][0])) {
				$pdf_defs = $pdf_defs['resultado'][0];
				$usarmarcadagua = $pdf_defs['marcadagua'];
				$usarcripto = $pdf_defs['cripto'];
			}
			if ($usarmarcadagua == 1 && $usarcripto == 1) $usarmarcadaguacripto = "checked";

			$autores_obra = $app->acessaAPI("obra.autores_listar", array("idobra"=>$matches['idobra']))['resultado'];
			$autores_obra_IDs = array_column($autores_obra, "idautor");
			foreach($autores_db as &$autordb) {
				if (in_array($autordb['id'], $autores_obra_IDs)) {
					$autordb['selected'] = true;
				}
			}
			$categorias_obra = $app->acessaAPI("obra.categorias_listar", array("idobra"=>$matches['idobra']))['resultado'];
			$categorias_obra_IDs = array_column($categorias_obra, "idcategoria");
			foreach($lista_categorias as &$catdb) {
				if (in_array($catdb['id'], $categorias_obra_IDs)) {
					$catdb['selected'] = true;
				}
			}

            $template->set("obra", $obra );
            $template->set("usarmarcadaguacripto", $usarmarcadaguacripto );
            $template->set("autores_obra", $autores_obra );
            $template->set("autores_obra_IDs", $autores_obra_IDs );
            $template->set("selected_".$obra["status"], 'selected="selected"' );
            $template->set("selected_assinatura_".$obra["assinatura"], 'selected="selected"' );
            $template->set("selected_revisao_".$obra["revisao"], 'selected="selected"' );

        } else {
            // Para cadastro novo, definir obra como null e valores padrão
            $template->set("obra", null);

            // Status padrão: Rascunho (0)
            $template->set("selected_0", 'selected="selected"');
            $template->set("selected_1", '');
            $template->set("selected_2", '');

            // Assinatura padrão: Não (0)
            $template->set("selected_assinatura_0", 'selected="selected"');
            $template->set("selected_assinatura_1", '');

            // Marcadagua e cripto padrão: não marcado
            $template->set("usarmarcadaguacripto", "");

            // Arrays vazios para autores
            $template->set("autores_obra", []);
            $template->set("autores_obra_IDs", []);
        }
        $template->set("opcoes_faixa", $opcoes_faixa );
        $template->set("autores_db", $autores_db );
        $template->set("categorias", $lista_categorias );

        error_log("Cadastro Debug: Passando para template - Autores: " . count($autores_db) . " itens");
        error_log("Cadastro Debug: Passando para template - Categorias: " . count($lista_categorias) . " itens");

        //$categorias = new \categorias();
        //$lista_categorias = $categorias->listarCategorias();
		// listar categorias por api (do banco de producao)

        echo $template->render();
    }

    function POST($matches, $app) {

	$obras = new \Obras();
  
        # Caso tenha recebido um ID obra
        if(isset($matches["idobra"])):

          $obras = new \Obras();
          $obras->filtroID = $matches["idobra"];
          $obras->filtroExibirCapa = true;
          $obras->listarObrasObjetos();

          $obra = $obras->lista[0];

          $obra->status = $_REQUEST["status"];

          # Se eu colocar status 0, entao tenho que voltar a etapa
          # para obra em montagem.
          if($obra->status == 0): $obra->etapa = 5; endif;

        else:

          $obra = new \Obra();

          $obra->status = 0;

        endif;
        # Atualizando os campos todos
        $obra->titulo = $_REQUEST["titulo"];
        $obra->autor = $_REQUEST["autor"];
        $obra->dados_obra = $_REQUEST["dados_obra"];
	$obra->dados_autor = $_REQUEST["dados_autor"];
#	$obra->tipo_ISBN = $_REQUEST["tipo_ISBN"];
        $obra->ISBN = $_REQUEST["ISBN"];
        $obra->codigo_editora = $_REQUEST["codigo_editora"];
        $obra->edicao = $_REQUEST["edicao"];
        $obra->ano = $_REQUEST["ano"];
		if (is_array($_REQUEST['idcategorias'])) $idcategoria_0 = $_REQUEST['idcategorias'][0];
		else $idcategoria_0 = $_REQUEST['idcategorias'];
        $obra->idcategoria = $idcategoria_0;
        $obra->paginaum = 0;
        $obra->preco = str_replace(',', '.', str_replace('.', '', $_REQUEST["preco"]));
        $obra->qtdpaginas = $_REQUEST["qtdpaginas"];
        $obra->porcentagemautor = $_REQUEST["porcentagemautor"];
        $obra->assinatura = $_REQUEST["assinatura"];
	$obra->revisao = 0;
	$obra->sumario_inicio = $_REQUEST['sumario_inicio'];
	$obra->sumario_fim = $_REQUEST['sumario_fim'];
        $obra->datalancamento = $_REQUEST["datalancamento"] ? ConverteData($_REQUEST["datalancamento"]) : ConverteData(date('d/m/Y'));

        /*
        # Antes de gravar a obra, vou verificar todos os uploads.
        $arquivo_capa = (isset($_FILES["capa"]) && $_FILES["capa"]["error"] == 0) ? $_FILES["capa"] : false;

        $arquivo_capa_hi_res = (isset($_FILES["capa_hi_res"]) && $_FILES["capa_hi_res"]["error"] == 0) ? $_FILES["capa_hi_res"] : false;
        $arquivo_pdf = (isset($_FILES["pdf"]) && $_FILES["pdf"]["error"] == 0) ? $_FILES["pdf"] : false;

        # Verificando se foram recebidos com sucesso.
        if(!$arquivo_capa) $app->retornaJson(-1, "Envie a capa da obra em resolução pequena.");
        if(!$arquivo_capa_hi_res) $app->retornaJson(-1, "Envie a capa da obra em alta resolução");
        if(!$arquivo_pdf) $app->retornaJson(-1, "Envie o arquivo em PDF completo da obra.");

        # Verificando o tipo dos arquivos
        if(!verifica_imagem_upload($arquivo_capa, array("maxw" => 165, "minw" => 165, "maxh" => 237, "minh" => 236)))
            $app->retornaJson(-1, "Dimensoes da imagem pequena estão erradas.");

        if(!verifica_imagem_upload($arquivo_capa_hi_res, array("maxw" => 2000, "minw" => 200, "maxh" => 200, "minh" => 2000)))
            $app->retornaJson(-1, "Dimensoes da imagem alta estão erradas.");

        */


        $json_extra = json_decode($obra->json_extra, true);
        if (!$json_extra) $json_extra = [];


        if (isset($_REQUEST["ajuste_vertical"]) && $_REQUEST["ajuste_vertical"] != 0) {
            $json_extra["ajuste_vertical"] = $_REQUEST["ajuste_vertical"];
            $obra->etapa = 3;
        }

        $obra->json_extra = json_encode($json_extra);


        # Grava a OBRA no banco de dados (Agora ja possui um ID)
        $gravar = $obras->gravarObra($obra);

		/*** SALVAR/Redefinir opcoes de faixa da obra ***/
		$faixalancamento = $faixaatualizada = $faixaatualizadaprimeiro = 0;
		if (isset($_REQUEST['mostrarFaixaLancamento']) && $_REQUEST['mostrarFaixaLancamento'] == 1)
			$faixalancamento = 1;
		if (isset($_REQUEST['mostrarFaixaAtualizada']) && $_REQUEST['mostrarFaixaAtualizada'] == 1)
			$faixaatualizada = 1;
		if (isset($_REQUEST['mostrarFaixaAtualizadaPrimeiro']) && $_REQUEST['mostrarFaixaAtualizadaPrimeiro'] == 1)
			$faixaatualizadaprimeiro = 1;
		$app->acessaAPI("obra.mostrar_faixa", Array("idobra"=>$obra->id, "faixalancamento"=>$faixalancamento, "faixaatualizada"=>$faixaatualizada,"faixaatualizadaprimeiro"=>$faixaatualizadaprimeiro));

		/*** SALVAR/Redefinir autores da obra ***/
		if (isset($_REQUEST['idautores']) && count($_REQUEST['idautores']) > 0)
		{
			$idAutores = implode(",",$_REQUEST['idautores']);
			$app->acessaAPI("obra.autores_redefinir", Array("idobra"=>$obra->id, "idautor"=>$idAutores));
		}
		/*** SALVAR/Redefinir categorias da obra ***/
		if (isset($_REQUEST['idcategorias']) && count($_REQUEST['idcategorias']) > 0)
		{
			$idCategorias = implode(",",$_REQUEST['idcategorias']);
			$app->acessaAPI("obra.categorias_redefinir", Array("idobra"=>$obra->id, "idcategoria"=>$idCategorias));
		}
		/*** Define se deve ser utilizado marcadagua e cripto no arquivo pdf ***/
		if (isset($_REQUEST['usar_marcadaguacripto']) && $_REQUEST['usar_marcadaguacripto'] == 1)
			$app->acessaAPI("obra.pdf_usarmarcadaguacripto", Array("idobra"=>$obra->id, "marcadagua"=>1, "cripto"=>1));
		else
			$app->acessaAPI("obra.pdf_usarmarcadaguacripto", Array("idobra"=>$obra->id, "marcadagua"=>0, "cripto"=>0));


            # Agora que ja gravei a obra, e possuo um ID, eu vou verificar os uploads e
            # também criar todos os diretórios

            # Verificando se o diretório de uploads dessa obra existe.
            $dirname = $_SERVER["DOCUMENT_ROOT"]."/uploads/" . $app->tenant ."/" . $obra->id . "/";
            if (!file_exists($dirname)) mkdir($dirname, 0777, true);

            function verifica_imagem_upload($arquivo, $config) {

               if(!preg_match("/image\/(pjpeg|jpeg|png|gif|bmp)/", $arquivo["type"]))
                  return array(-1, "Tipo do arquivo inválido. O capa deve ser uma imagem.");

                /*
                if($arquivo["size"] > $config["tamanho"])
                  return array(-1, "Imagem muito grande, deve ser menor que 100kb");
                    */
                $tamanhos = getimagesize($arquivo["tmp_name"]);

                if($tamanhos[0] > $config["largura"])
                  return array(-1, "Imagem muito larga.");

                if($tamanhos[1] > $config["altura"])
                  return array(-1, "Imagem muito alta.");

                return false;

            }

            # Upload da capa
            if(isset($_FILES["capa"]) && $_FILES["capa"]["error"] == 0):

                $arquivo = $_FILES["capa"];

                # Configurações para a imagem.
                $config["tamanho"] = 1106883;
                $config["largura"] = 12566;
                $config["altura"]  = 12566;

		$verifica = verifica_imagem_upload($arquivo, $config);
		
                if($verifica) $app->retornaJson(-1, $verifica[1]);

                # Coloco o conteudo no campo
                $obra->capa = file_get_contents( $arquivo['tmp_name'] ) ;

                # Movo o upload
                move_uploaded_file($arquivo['tmp_name'], $dirname."capa.jpg");

            endif;

            # Upload da capa hi res
            if(isset($_FILES["capa_hi_res"]) && $_FILES["capa_hi_res"]["error"] == 0):

                $arquivo = $_FILES["capa_hi_res"];

                # Configurações para a imagem.
                $config["tamanho"] = 110688300;
                $config["largura"] = 33566;
                $config["altura"]  = 33566;

		$verifica = verifica_imagem_upload($arquivo, $config);
	
                if($verifica) $app->retornaJson(-1, $verifica[1]);

                # Coloco o conteudo no campo
                $obra->capa_hi_res = file_get_contents( $arquivo['tmp_name'] ) ;

                # Movo o upload
                move_uploaded_file($arquivo['tmp_name'], $dirname."capa_hi_res.jpg");


            endif;

            # Gravo as capas
            $gravar = $obras->gravarObra($obra);

	    # UPLOAD DO PDF
            if(isset($_FILES["pdf"])):
              if ($_FILES["pdf"]["error"] == 0):
                $arquivo = $_FILES["pdf"];

 	 	move_uploaded_file($arquivo['tmp_name'], $dirname.$obra->id.".pdf");	   
                chmod($dirname.$obra->id.".pdf", 0777);

		// marcar atualizar pdf
		$api_res = $app->acessaAPI("obra.pdf_marcar_atualizar", Array("idobra"=>$obra->id));		

                # Vou remover o mastigador.ini
                if(file_exists($dirname."mastigador.ini")) {
                    unlink($dirname."mastigador.ini");
                }

                if(file_exists($dirname."tratador.ini")) {
                    unlink($dirname."tratador.ini");
                }

                $obras->atualizaEtapa($obra->id, 1);

                # URL da API de e-books
                # $url = sprintf("http://$HOST/obra-mastigar?tenant=%s&ids=%d&token=q1w2e3r4", $app->tenant, $obra->id);

                # Monto o POST
                #$cmd = sprintf("$BIN/curl --connect-timeout 9000 --max-time 9000 \"%s\" &",  $url);

                # Executo e já fecho. :)
                #$handle = pclose(popen($cmd, "r"));

              endif;
            endif;

            $retorno = array();
            $retorno["obra"] = $obra;

            $app->retornaJson(0, "");


    }

}

class Excluir {
	public $before = array("is_auth");

	function POST($matches, $app) {
		$data = $_REQUEST;
		if (!isset($data['idobra']) OR !is_numeric($data['idobra']))
			$app->retornaJson(-1, "Requisição inválida");

		$obras = new \Obras;
		$obras->app = $app;
		if ($obras->excluirObra($data['idobra']))
			$app->retornaJson(0, "Ok");

		$app->retornaJSon(-1, "Erro ao tentar excluir obra");
	}
}


    class Download {

        public $before = array("is_auth");

        function GET($matches, $app) {

            $obras = new \Obras;
            $paginas = $obras->listarPaginas( $matches["idobra"] );

            # Crio a pasta
            $dirname = ROOT_SYS . "downloads/".$matches["idobra"] . "/";
            if (!file_exists($dirname))
            mkdir($dirname, true);
            chmod($dirname, 0777);

            foreach($paginas as $pagina):

                $o = $obras->imagemObra( $matches["idobra"], $pagina["numeropagina"] );

                if ($o)
                    file_put_contents($dirname . $o["nomearquivo"], $o["imagem"] );

            endforeach;



        }
    }









class Descompactar {
      function GET($matches, $app) {
        # Filtros
        $tenant = $app->tenant;

        $idobra = $matches["idobra"];

        $path = "/mnt/data/editor/uploads/$tenant";
        
        chdir("/mnt/data/editor/uploads/$tenant/");
        
        exec("tar -xzvf $idobra.tar.gz" );
header("Location: /obras");
        // exec("rm $idobra.tar.gz" );
    }
}

class Compactar {
      function GET($matches, $app) {
        # Filtros
        $tenant = $app->tenant;

        $idobra = $matches["idobra"];
        $path = "/mnt/data/editor/uploads/$tenant";
        
        chdir("/mnt/data/editor/uploads/$tenant/");
        
        exec("tar -cvzf $idobra.tar.gz $idobra" );
	exec("rm -rf $idobra" );

header("Location: /obras");
      }
}


class Resetar{
     function GET($matches, $app){
        # Filtros
        $tenant = $app->tenant;

        $idobra = $matches["idobra"];
        $obras = new \Obras();
	$obras->atualizaEtapa($idobra,1);
	header("Location: /obras");	
    }
}

class Imagens{
	function GET($matches, $app){
		$tenant = $app->tenant;
		$idobra = $matches["idobra"];
		$obras = new \Obras();
		$obras->atualizaEtapa($idobra,6);
		header("Location: /obras");
	}
}

class ChatGPT{
	function GET($matches, $app){
		$tenant = $app->tenant;
		$idobra = $matches["idobra"];

		$target_url = "http://gpt.codell.com.br/";

        	$fpath = '/mnt/data/editor/uploads/'.$tenant.'/'.$idobra.'/'.$idobra.'.pdf';
		$pdf = base64_encode(file_get_contents($fpath));

		
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL,$target_url);
        curl_setopt($ch, CURLOPT_POST, true);
	curl_setopt($ch, CURLOPT_VERBOSE, 1);
	curl_setopt($ch, CURLOPT_VERBOSE, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS,
            http_build_query(array(
                'idobra' => $idobra,
                'tenant'=>$tenant,
                'pdf64'=>$pdf
            )));

// Receive server response ...
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $server_output = curl_exec($ch);
        print_r($server_output);
        exit();
        curl_close($ch);

		exit($idobra);
	}
}
