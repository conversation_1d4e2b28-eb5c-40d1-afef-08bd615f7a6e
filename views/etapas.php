<?php

    namespace Views\Etapas;

    class Mastigar {

        function GET($matches, $app) {

              if(!isset($matches["idobra"])):

                  # Busco qualquer obra com status de: Aguardando mastigaçao
                  $obras = new \Obras();
                  $obras->filtroEtapa = 1;
                  $obras->listarObrasObjetos();

              else:

                  # Busco a obra no banco de dados
                  $obras = new \Obras();
                  $obras->filtroID = $matches["idobra"];
                  $obras->filtroExibirCapa = true;
                  $obras->listarObrasObjetos();

              endif;

              if(!count($obras->lista)) $app->retornaJson(0, "Nenhuma obra foi encontrada para tratar!");
              $obra =  $obras->lista[0];

              # Mastigando...
              $obras->atualizaEtapa($obra->id, 2);

              # Tento tratar a obra
	      # Caso aconteça algum problema, retorno a obra para a etapa anterior	      
              try {
                  $obras->mastigarObra($obra);
              } catch (Exception $e) {
                  $obras->atualizaEtapa($obra->id, 1);
                  $app->retornaJson(-1, "Ocorreu um erro na mastigação!", array("idobra" => $obra->id) );
              }

              # Mastigo a obra

              # Mastigada!
              $obras->atualizaEtapa($obra->id, 3);

              $app->retornaJson(0, "Mastigacao completa!", array("idobra" => $obra->id) );
        }

    }

    class Sumarizar {

        function GET($matches, $app) {

              # Busco qualquer obra com status de: Aguardando mastigaçao
              $obras = new \Obras();
              $obras->filtroID = $matches["idobra"];
              $obras->listarObrasObjetos();

              $obra = $obras->lista[0];
              $obras->atualizaSumarioObra($obra->id);

              $app->retornaJson(0, "Sumário atualizado!", array("idobra" => $obra->id) );

        }

    }

    class Tratar {

        function GET($matches, $app) {

              if(!isset($matches["idobra"])):

                    # Busco qualquer obra com status de: Aguardando tratamento
                    $obras = new \Obras();
                    $obras->filtroEtapa = 3;
                    $obras->listarObrasObjetos();

              else:

                  # Busco a obra no banco de dados
                  $obras = new \Obras();
                  $obras->filtroID = $matches["idobra"];
                  $obras->listarObrasObjetos();

              endif;

              if(!count($obras->lista)) $app->retornaJson(0, "Nenhuma obra foi encontrada para tratar!");
              $obra =  $obras->lista[0];

              # Tratando...
              $obras->atualizaEtapa($obra->id, 4);

              # Tento tratar a obra
              # Caso aconteça algum problema, retorno a obra para a etapa anterior
              # Poderia avisar o pessoal também
              try {
                  $obras->tratarArquivos($obra);
              } catch (Exception $e) {
                  $obras->atualizaEtapa($obra->id, 3);
                  $app->retornaJson(-1, "Ocorreu um erro no tratamento!", array("idobra" => $obra->id) );
              }

              # Tratada!
              $obras->atualizaEtapa($obra->id, 5);

              $app->retornaJson(0, "Tratamento completo!", array("idobra" => $obra->id) );

        }

    }

    class Pdf {
	    function GET($matches, $app) {

		if(isset($matches["idobra"])):
			$tenant = $app->tenant;
			$pdf_path = '/mnt/data/editor/uploads/'.$tenant.'/'.$matches["idobra"].'/'.$matches["idobra"].'.pdf';
#			$cmd = "sudo -u admin /home/<USER>/.local/bin/aws s3 cp ".$pdf_path." s3://lettore-ebooks/".$tenant."/".$matches["idobra"].".pdf";

			$cmd = "aws s3 ls lettore-ebooks/".$tenant;
			exec($cmd,$output);
			print_r($output);
			exit;

		endif;

		$app->retornaJson(0, "Envio de PDF completo!");


	}
    }

    class Gerar {

        function GET($matches, $app) {


              if(!isset($matches["idobra"])):

                    # Busco qualquer obra com status de: Aguardando tratamento
                    $obras = new \Obras();
                    $obras->filtroEtapa = 6;
                    $obras->filtroExibirCapa = true;
                    $obras->listarObrasObjetos();

              else:

                  # Busco a obra no banco de dados
                  $obras = new \Obras();
                  $obras->filtroID = $matches["idobra"];
                  $obras->filtroExibirCapa = true;
                  $obras->listarObrasObjetos();

              endif;

              if(!count($obras->lista)) $app->retornaJson(0, "Nenhuma obra foi encontrada para gerar!");
              $obra =  $obras->lista[0];

              # Indexando a obra...
              $obras->atualizaEtapa($obra->id, 7);
              # Marco todas as imagens para resincronizar.
              $obras->resincronizarImagens($obra->id);

              # Gero o JBE
              gerar_jbe($app->settings["tenant"]["tenant"], $obra->id);

              # Gero os IDX
              $IndiceFts = new \ArquivoIndiceFts(null, $app->settings["tenant"]["tenant"], $obra->id, true);

              # Obra atualizada...
              $obras->atualizaEtapa($obra->id, 8);
              $app->retornaJson(0, "Geracao completa!", array("idobra" => $obra->id) );

        }

    }

    class Publicar {

        function GET($matches, $app) {

            # Busco a obra no banco de dados
            $obras = new \Obras();
            $obras->filtroID = $matches["idobra"];
            $obras->filtroExibirCapa = true;
            $obras->listarObrasObjetos();

            if(!count($obras->lista)) $app->retornaJson(0, "Nenhuma obra foi encontrada para publicar!");
            $obra = $obras->lista[0];

            # Coloca a obra no status certo (publicado ou venda)
            $obra->status = $matches["status"];

            # Etapa de indexar
            $obra->etapa = 6;

            $obras->gravarObra($obra);

            $app->retornaJson(0, "Status alterado", array("idobra" => $obra->id) );

        }

    }

    class Remontar {

        function GET($matches, $app) {

            # Busco a obra no banco de dados
            $obras = new \Obras();
            $obras->filtroID = $matches["idobra"];
            $obras->filtroExibirCapa = true;
            $obras->listarObrasObjetos();

            if(!count($obras->lista)) $app->retornaJson(0, "Nenhuma obra foi encontrada para remontar!");
            $obra = $obras->lista[0];

            # Etapa de montagem.
            $obra->etapa = 5;

            $obras->gravarObra($obra);

            $app->retornaJson(0, "Etapa alterado", array("idobra" => $obra->id) );

        }

    }

    class JBE {

        function GET($matches, $app) {

            # Busco a obra no banco de dados
            $obras = new \Obras();
            $obras->filtroID = $matches["idobra"];
            $obras->filtroExibirCapa = true;
            $obras->listarObrasObjetos();

            if(!count($obras->lista)) $app->retornaJson(0, "Nenhuma obra foi encontrada para gerar!");
            $obra =  $obras->lista[0];

            # Gero o JBE
            gerar_jbe($app->settings["tenant"]["tenant"], $obra->id);

            # Gero os IDX
            $IndiceFts = new \ArquivoIndiceFts(null, $app->settings["tenant"]["tenant"], $obra->id, true);

            $app->retornaJson(0, "Geracao completa!", array("idobra" => $obra->id) );

        }
    }



?>
