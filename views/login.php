<?php

namespace Views\Login;

/* Método para autenticar o usuário via login e senha */
class Logout {

    function GET($matches, $app) {
         $app->finalizaSessao();
    }

}

class Login {

    function GET($matches, $app) {

        if(isset($app->session["logado"]) && $app->session["logado"] == true) $app->redir("/obras");

        error_log("Login Debug: Tentando carregar template login.html");
        $template = $app->template("login.html");
        if ($template === null) {
            error_log("Login Debug: Template retornou null");
            return;
        }
        error_log("Login Debug: Template carregado com sucesso, tentando renderizar");
        echo $template->render();
        error_log("Login Debug: Template renderizado com sucesso");
    }

}

/* Método para autenticar o usuário via login e senha */
class Autenticar {

    # Before 
    public $before = array("is_ajax");
    
    function POST($matches, $app) {	    
        $email = trim($_REQUEST["login-email"]);
        $senha = trim($_REQUEST["login-senha"]);
        
        # Verifica os campos recebidos
        if(empty($email)) {
            $app->retornaJson(-1, 'Campo email não deve estar vazio.');
            return;
        }
        if(empty($senha)) {
            $app->retornaJson(-2, 'Campo senha não deve estar vazio.');
            return;
        }


        # Objeto de Login
        $login = new \Login();
        
        # Informações do Usuario
        $login->setUsuario($email);
        $login->setSenha($senha);

        # Tenta autenticar
        $usuario = null;
        $auth = $login->login($usuario);

        # Usuario autenticado.
        if($auth == 0):
            $logado = $app->autorizaUsuarioLogado($usuario);
            $app->retornaJson(0, "Usuário autenticado.");
            return;
        endif;

        $app->retornaJson(-3, "Usuário e/ou senha incorretos.");
        return;

    }

}



?>
