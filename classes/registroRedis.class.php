<?php

# Classe Redis
class RegistroRedis {

  private $stringSistema = 'bliblioteca-web-';
  private $host = '************'; // '************';
  private $port = 6379;
  private $password = ''; //'f8f8f82e4cd9e28c6e0c5c9d32080b6e';
  private $db = 0;

  private $link = null;

  function __construct($tenant) {

    if(file_exists($_SERVER["DOCUMENT_ROOT"]."/classes/local.php")):

      $this->host = '127.0.0.1';
      $this->port = 6379;
      $this->password = '';
      $this->db = 15;
    endif;

    $this->stringSistema = sprintf("%s-web-", $tenant );

    # Instanciando o objeto de Redis
    $this->link = new Redis();

    # Realizando a conexão
    $conecta = $this->link->connect($this->host, $this->port);

    if($conecta):
      $conecta = $this->link->auth($this->password);
      $conecta = $this->link->select($this->db);
    endif;

    if(!$conecta) trigger_error("Não foi possivel conectar no Redis");

  }

  function __destruct() {

    if($this->link):
      $this->link->close();
      $this->link = null;
    endif;

  }

  # Listar todas as chaves do redis que possuvem o mesmo identificador de sistema.
  function listarRegistros($stringRegistro = "") {
    $key = sprintf("%s%s*", $this->stringSistema, $stringRegistro);
    return $this->link->keys($key);
  }

  # Lista uma chave em especifico
  function listarRegistro($stringRegistro, $fill = true) {

    if($fill)
      $key = sprintf("%s%s", $this->stringSistema, $stringRegistro);
    else
      $key = $stringRegistro;

    return json_decode($this->link->get($key), true);
  }



  # Salva um registro no redis
  function salvarRegistro($stringRegistro, $dados, $expire = 60) {
    $key = sprintf("%s%s", $this->stringSistema, $stringRegistro);
    $this->link->set($key, json_encode($dados));
    $this->link->expire($key, $expire);
    return $this->listarRegistro($key);
  }

  function expireRegistro($stringRegistro, $expire = 60) {
    $key = sprintf("%s%s", $this->stringSistema, $stringRegistro);
    $this->link->expire($key, $expire);
    return $this->listarRegistro($key);
  }

  # Remove um registro no redis
  function removeRegistro($stringRegistro) {
    $key = sprintf("%s%s", $this->stringSistema, $stringRegistro);
    $this->link->delete($key);
  }

}

?>
