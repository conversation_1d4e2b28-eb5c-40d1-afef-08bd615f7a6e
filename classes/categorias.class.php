<?php

  require_once "conecta.class.php";

  class Categoria {

    public $id = 0;
    public $idcategoria = 0;
    public $categoria = "";

  }

  class Categorias extends conecta {

    function __construct($link = NULL, $tenant = NULL) {
      parent::__construct($link, $tenant);
    }

    function __destruct() {
      parent::__destruct();
    }

    public function listarCategorias() {

      # Monta a query completamente
      $squery = sprintf("SELECT * FROM categorias a");

      $query = $this->query($squery);
      $fetch = $query->fetch();

      # Percorro todos os lançamentos fixos encontrados.
      return $fetch;

    }

    
  }

?>