<?php

# Headers - apenas se não estiver rodando no Swoole
# Detecta se está rodando no Swoole verificando se headers já foram enviados
if (!headers_sent() && !function_exists('swoole_version')) {
    header('Content-Type: text/html; charset=utf-8');
    header('X-UA-Compatible: IE=edge');
}

# Erros php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

# Timexone
date_default_timezone_set("America/Sao_Paulo");

# Constantes
define('IS_AJAX', isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest');

$root_sys =  isset($_SERVER["DOCUMENT_ROOT"]) ? $_SERVER["DOCUMENT_ROOT"] : '';
$root_sys = trim($root_sys, "/");
$root_sys = "/" . $root_sys . "/";



// $root_sys = "/mnt/dados/www/";

define('ROOT_SYS', $root_sys);


# Carregar ConfigManager
require_once __DIR__ . '/ConfigManager.php';

# Inicializar configurações usando singleton
$configManager = ConfigManager::getInstance();
$configManager->initialize();

# Definir variáveis globais para compatibilidade com código existente
$configManager->setGlobalVars();

# Referenciar as variáveis globais para compatibilidade
$SETTINGS = $configManager->getSettings();
$TENANT = $configManager->getTenant();

?>

