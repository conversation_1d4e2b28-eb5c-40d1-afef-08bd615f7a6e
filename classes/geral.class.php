<?php

require_once __DIR__ . "/conecta.class.php";
require_once __DIR__ . "/functions.inc.php";
require_once __DIR__ . "/comum.inc.php";
// Carregar autoloader do Composer para Twig
if (file_exists(__DIR__ . "/../vendor/autoload.php")) {
    require_once __DIR__ . "/../vendor/autoload.php";
} elseif (file_exists("/var/www/html/vendor/autoload.php")) {
    require_once "/var/www/html/vendor/autoload.php";
}
require_once __DIR__ . "/TwigWrapper.php";
require_once __DIR__ . "/handleerrors.class.php";
require_once __DIR__ . "/registroRedis.class.php";
require_once __DIR__ . "/masterroboto.class.php";
require_once __DIR__ . "/encoding.class.php";
require_once __DIR__ . "/login.class.php";
require_once __DIR__ . "/categorias.class.php";
require_once __DIR__ . "/dom.class.php";
require_once __DIR__ . "/obras.class.php";
require_once __DIR__ . "/canvas.class.php";
require_once __DIR__ . "/indicefts.class.php";

class Geral {

  public $isJson = false;
  public $settings = NULL;
  public $tenant = null;

  public $usuarioLogado = NULL;
  public $usuarioLogadoChave = NULL;

  private $handleErrors;
  public $redis = NULL;

  /**
  * Construtor da classe
  * @param $isJson Se o retorno da página é em json ou em html
  * @param $isLogin Se a pagina precisa de autenticação ou não.
  */
  function __construct($isJson = false, $isLogin = false) {

    error_log("Geral Debug: Construtor iniciado");

    # Carregando as configurações usando ConfigManager
    require_once __DIR__ . '/ConfigManager.php';
    
    # Inicializar com Swoole se disponível
    if (extension_loaded('swoole')) {
        require_once __DIR__ . '/SwooleBootstrap.php';
        SwooleBootstrap::initialize();
    }
    
    $configManager = ConfigManager::getInstance();

    # Garantir que as configurações estão inicializadas
    $configManager->initialize();

    # Obter configurações do singleton
    $this->settings = $configManager->getSettings();
    $tenant_data = $configManager->getTenant();

    # Debug: verificar se as configurações existem
    error_log("Geral Debug: SETTINGS carregado? " . (isset($this->settings) ? "SIM" : "NÃO"));
    error_log("Geral Debug: TENANT carregado? " . (isset($tenant_data) ? "SIM" : "NÃO"));

    if ($tenant_data) {
        error_log("Geral Debug: TENANT data = " . print_r($tenant_data, true));
    }

    error_log("Geral Debug: Settings carregados via ConfigManager");

    $this->tenant = $tenant_data["tenant"] ?? null;
    error_log("Geral Debug: Tenant = " . ($this->tenant ?? 'null'));

    # Definir variáveis globais para compatibilidade
    $configManager->setGlobalVars();

    # Tratamento do erros.
    if (class_exists('HandleErrors')) {
      $this->handleErrors = new HandleErrors($this, $isJson);
    } else {
      error_log("Classe HandleErrors não encontrada");
      $this->handleErrors = null;
    }

    # Verifico se a pagina é um AJAX ou se foi requisitado resposta em JSON
    if(IS_AJAX || $isJson)
      $this->setJsonRetorno(true);
    else
      $this->setJsonRetorno(false);

    # Iniciando a sessão
    error_log("Geral Debug: Iniciando sessão");
    $this->iniciaSessao();

    # Gera o CSS personalizado da tenant
    error_log("Geral Debug: Gerando CSS");
    $this->generate_css();

    # Verifica o login na sessão.
    error_log("Geral Debug: Verificando usuário logado");
    $this->verificaUsuarioLogado($isLogin);
    error_log("Geral Debug: Construtor finalizado");


  }

  public function generate_css() {

      # Verificar se tenant está definido
      if (!isset($this->settings["tenant"]) || !isset($this->settings["tenant"]["tenant"])) {
          error_log("Geral Debug: Tenant não definido, pulando geração de CSS");
          return;
      }

      # Criando o diretório de CSS na pasta média da TENANT
      $dirname = $_SERVER["DOCUMENT_ROOT"].$this->settings["media_dir"]."/generate_css/";
      $filename = $dirname.$this->settings["tenant"]["tenant"].".css";

      # Caso o arquivo já existe, beleza.
      # if(file_exists($filename)) return;

      # Verifico se o diretório CSS já existe, dai eu crio ele.
      if (!file_exists($dirname)) mkdir($dirname, 0777, true);

      # Gerando o CSS baseado no template e com as cores do config
      $template = $this->template("css/colors.css");
      if ($template === null) {
          error_log("Geral Debug: Template css/colors.css não pôde ser carregado");
          return;
      }

      $content = $template->render();

      # Salvo o arquivo CSS
      file_put_contents($filename, $content);
      //chmod($filename, 0777);

  }

  /**
  * Método que constrói um template com um contexto padrão
  * @param $file caminho do template
  *
  */
  public function template($file, $template_dir = false) {

    # Load template html
    # Diretorio normal, dos templates gerais.
    $dir = $_SERVER["DOCUMENT_ROOT"] . $this->settings["work_dir"] . ($template_dir ? $template_dir : $this->settings["template_dir"]);
    $dir2 = $_SERVER["DOCUMENT_ROOT"] . $this->settings["work_dir"] . ($template_dir ? $template_dir : $this->settings["tenant"]["template_dir"] ?? $this->settings["template_dir"]);

    error_log("Geral Debug: dir = " . $dir);
    error_log("Geral Debug: dir2 = " . $dir2);
    error_log("Geral Debug: template_dir = " . ($template_dir ?: 'false'));
    error_log("Geral Debug: settings[template_dir] = " . ($this->settings["template_dir"] ?? 'null'));
    error_log("Geral Debug: tenant[template_dir] = " . ($this->settings["tenant"]["template_dir"] ?? 'null'));

    try {
        # Primeiro ele procura no diretorio do tenant, depois no geral
        $template = new TwigWrapper($file, ["searchpath" => [$dir2, $dir]]);
    } catch (Exception $e) {
        error_log("Swoole Debug: Erro ao criar template Twig: " . $e->getMessage());
        trigger_error( $e->getMessage());
        return null;
    }


    # Path
    $template->set("work_dir", $this->settings["work_dir"] );
    $template->set("media_dir", $this->settings["work_dir"]  . $this->settings["media_dir"] );
    $template->set("version", $this->settings["version"]);
    $template->set("base_url", $this->settings["base_url"]);
    $template->set("debug", $this->settings["debug"]);

    # Debug: verificar se tenant está sendo passado corretamente
    if (isset($this->settings["tenant"])) {
        error_log("Geral Debug: Tenant existe nos settings: " . print_r($this->settings["tenant"], true));
        $template->set("tenant", $this->settings["tenant"]);
    } else {
        error_log("Geral Debug: Tenant NÃO existe nos settings!");
        # Usar tenant padrão para evitar erro
        $template->set("tenant", [
            "tenant" => "default",
            "nome" => "Sistema",
            "style" => [
                "primary_color" => "#308ccb",
                "secondary_color" => "#2879b0",
                "text_color" => "#FFFFFF"
            ]
        ]);
    }

    # PHP ENV
    $template->set("SESSION", $_SESSION ?? []);
    $template->set("SERVER", $_SERVER ?? []);
    $template->set("COOKIE", $_COOKIE ?? []);
    $template->set("POST", $_POST ?? []);
    $template->set("GET", $_GET ?? []);

    # Debug: verificar se COOKIE tem loginemail
    error_log("Geral Debug: COOKIE = " . print_r($_COOKIE, true));

    # Usuario Logado?
    $template->set("usuario", $this->usuarioLogado);

    return $template;
  }

  public function error($err) {
    $set_code_error_handler = $err;
    trigger_error($err);
  }

  public function redir($url, $extra = NULL) {
    if (!headers_sent()) {
      if($extra) header($extra);
      header("Location: $url");
    }
    exit();
  }

  /**
  * Seta o retorno em JSON ou HTML
  * @param $isJson Se o retorno da página é em json ou em html
  */
  public function setJsonRetorno($isJson = true) {
    $this->isJson = $isJson;
    if($this->handleErrors) $this->handleErrors->setJsonRetorno($isJson);

  }

  /**
  * Retorno de qualquer página em JSON
  * @param $erro Número do erro ( Menor que 0 é erro )
  * @param $mensagem Mensagem de erro a ser exibida no retorno.
  * @param $extra Array nominal contendo qualquer parametros extra.
  */
  public function retornaJson($erro, $mensagem, $extra = []) {

    if (!headers_sent()) {
      header('Content-Type: application/json; charset=utf-8');
    }

    # Formata o retorno
    $retorno = [];
    $retorno["erro"] = $erro;
    $retorno["mensagem"] = $mensagem;

    # Adiciona as chaves dos valores $extra
    foreach($extra as $k=>$v) {
      $retorno[$k] = $v;
    }

    # Retorna e finaliza o script
    echo json_encode($retorno);

    # No ambiente Swoole, não podemos usar exit()
    if (!extension_loaded('swoole')) {
        exit;
    }
  }

  /**
  * Inicializa a sessão do cliente
  *
  */
  public function iniciaSessao() {

    error_log("Geral Debug: iniciaSessao() - início");

    # Inicio a sessão apenas se não estiver ativa (PHP 8 compatibility)
    if (session_status() === PHP_SESSION_NONE) {
        error_log("Geral Debug: iniciaSessao() - iniciando sessão");
        session_start();
    }

    error_log("Geral Debug: iniciaSessao() - copiando sessão");
    # Copio para a minha classe
    # Agora todos os testes de sessao, devem ser feitos aqui.
    error_log("Geral Debug: iniciaSessao() - antes de atribuir session");
    $this->session = $_SESSION ?? [];
    error_log("Geral Debug: iniciaSessao() - depois de atribuir session");
    error_log("Geral Debug: iniciaSessao() - sessão copiada: " . print_r($this->session, true));

    error_log("Geral Debug: iniciaSessao() - finalizando sessão");
    # Finalizo a sessao - comentado temporariamente para debug
    # if (session_status() === PHP_SESSION_ACTIVE) {
    #     session_commit();
    # }

    error_log("Geral Debug: iniciaSessao() - fim");
  }

  public function atualizaSessao() {

    # Inicio a sessão apenas se não estiver ativa (PHP 8 compatibility)
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    # atualizo os dados
    $_SESSION = $this->session;

    # Finalizo a sessao
    if (session_status() === PHP_SESSION_ACTIVE) {
        session_commit();
    }

  }

 /**
  * Finaliza a sessão do cliente
  *
  */
  public function finalizaSessao() {

    global $WORK_DIR;

    unset($this->session["logado"]);
    unset($this->session["usuario_logado"]);
    unset($this->session["usuario_logado_chave"]);

    $this->atualizaSessao();

    if(!$this->isJson) {
      if (!headers_sent()) {
        header("location: ".($WORK_DIR ?? "")."/");
      }
      if (!extension_loaded('swoole')) {
          exit;
      }
    } else {
      $this->retornaJson(-1, "Sessão encerrada.");
    }

  }

  /**
  * Autoriza o usuário (Libera na session ) a acessar o sistema
  * @param $idusuario ID do usuário autorizado.
  */
  public function autorizaUsuarioLogado($usuario) {

    # Coloca informacoes na sessao do PHP
    $this->session["logado"] = true;
    $this->session["usuario_logado"] = $usuario; // ID do usuário

    $this->atualizaSessao();

    return 0;

  }

  /**
  * Verifica se o usuário está autorizado na sessão e se ele existe no banco de dados
  * Interrompe o script se não tiver autorizado e redireciona para a login.
  * @param $idusuario ID do usuário autorizado.
  */
  public function verificaUsuarioLogado($needLogin = true) {

    if(isset($this->session["logado"])) {
      # Usuário autorizado
      if( $this->session["logado"] == true ) {

        # Salva a referencia usuário na classe geral.
        $this->usuarioLogado = $this->session["usuario_logado"] ?? null;

        # Está ok, e o usuário está autorizado.
        return true;

      }
    }

    # Usuário não tem permissão para estar aqui
    if($needLogin) {
      if($this->isJson == false) {
        if (!headers_sent()) {
          header("location: /");
        }
        if (!extension_loaded('swoole')) {
            exit;
        }
      } else {
        echo json_encode(["deslogado" => 1, "redir" => "/"]); // "{Usuario não logado - json }"
        if (!extension_loaded('swoole')) {
            exit;
        }
      }
    }

  }

  public function dispatch($urls) {

      $method = strtoupper($_SERVER['REQUEST_METHOD']);
      $path = $_SERVER['REQUEST_URI'];

      error_log("Dispatch Debug: method = $method, path = $path");

      # Vou remover os parametros ? na url
      $split = explode("?", $path);
      $_SERVER['REQUEST_URI'] = $split[0];
      $path = $split[0];

      error_log("Dispatch Debug: path after cleanup = $path");

      $found = false;
      krsort($urls);


      foreach ($urls as $regex => $class):

          $regex = $this->settings["work_dir"] . $regex;
          $regex = str_replace('/', '\/', $regex);
          $regex = '^' . $regex . '\/?$';

          if (preg_match("/$regex/i", $path, $matches)):

              $found = true;
              error_log("Dispatch Debug: Found match for path $path with class $class");

              # Verifica se a view tem .html no final
              # se tiver, é só um template direto.
              $file = explode(".", $class);

              if(count($file) > 1 && in_array($file[1], array('html', 'xml')) ):

                  if (!headers_sent()) {
                    header('Content-Type: text/'.$file[1].'; charset=utf-8');
                  }

                  $template = $this->template($class);
                  print $template->render();

              else:

                  error_log("Dispatch Debug: Trying to instantiate class $class");
                  if (class_exists($class)):

                      error_log("Dispatch Debug: Class $class exists, creating instance");
                      $obj = new $class;

                      # Caso tenha os middlewares: before
                      if( property_exists($obj, "before") ):
                          foreach($obj->before as $decorator) {
                            $decorator = sprintf("\Middlewares\%s", $decorator);
                            $decorator($this);
                          }
                      endif;

                      if (method_exists($obj, $method)):
                          $obj->$method($matches, $this);
                      else:
                        $this->error("404");
                      endif;


                      # Caso tenha os middlewares: after
                      if( property_exists($obj, "after") ):
                          foreach($obj->after as $decorator) {
                            $decorator = sprintf("\Middlewares\%s", $decorator);
                            $decorator($this);
                          }
                      endif;

                  else:
                      $this->error("404");
                  endif;

              endif;
              break;

          endif;

      endforeach;

      if (!$found) $this->error("404");

  }

  function acessaAPI($metodo, $dados = [], $decode_json = true, $trigger_error = true ) {

    $set_code_error_handler = "api";

    # URL da API de e-books
    $url = "https://api.lettore.com.br/{$metodo}";
    // $url = "api_ebooks.jurid.com.br/{$metodo}"; // primeiros testes na AWS EC2
    //$url = "*********:8000/".$metodo;


    if(file_exists($_SERVER["DOCUMENT_ROOT"]."/classes/local.php")):

      # Calculo o tempo gasto na query
      list($usec, $sec) = explode(' ', microtime());
      $script_start = (float) $sec + (float) $usec;

    endif;
      
    # Usuário da API
    $username = $this->settings["tenant"]["api_login"];
    $password = $this->settings["tenant"]["api_senha"];
    $dadosauth = sprintf("%s:%s", $username, $password);

    $dados_string = "";
    $dados_string = http_build_query($dados);

        # Cria o CURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, count($dados));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(sprintf('Tenant: %s', $this->settings["tenant"]["tenant"])));
        curl_setopt($ch, CURLOPT_POSTFIELDS, $dados_string);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_ANY);
        curl_setopt($ch, CURLOPT_USERPWD, $dadosauth);
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($ch, CURLOPT_BINARYTRANSFER, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, true);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, true);
        curl_setopt($ch, CURLOPT_ENCODING, 'gzip');
        curl_setopt($ch, CURLOPT_VERBOSE, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 5);

        # Resultado
	$resultado = curl_exec($ch);

    
	if (file_exists($_SERVER["DOCUMENT_ROOT"] . "/classes/local.php")):

            # Fim do tempo gasto com a query
            list($usec, $sec) = explode(' ', microtime());
            $script_end = (float)$sec + (float)$usec;
            $elapsed_time = round($script_end - $script_start, 5);

            $file = fopen($_SERVER["DOCUMENT_ROOT"] . "/errors/" . "nowlog" . ".log", "a+");

            if ($file !== false) {
                fwrite($file, date("d/m/Y H:i") . ' (ET: ' . $elapsed_time . ') ' . $url
                    . " ####### # CURL INFO ------  ####### " . json_encode(curl_getinfo($ch))
                    . " ####### # DATA SEND ------  ####### " . json_encode($dados)
                    . " ####### # RESPONSE  ------  ####### " . $resultado . "\n");
                fclose($file);
            }

        endif;


    // Vou sempre encodar o resultado
    $resultado = $resultado;  

    curl_close($ch);

    # Estou esperando um json
    if($decode_json):

        $retorno = json_decode($resultado, true);

        $json_error = json_last_error();

        # Trigger nos erros
        switch($json_error) {
          case JSON_ERROR_DEPTH: trigger_error("Retorno API: A profundidade máxima da pilha foi excedida"); break;
          case JSON_ERROR_STATE_MISMATCH: trigger_error("Retorno API: JSON inválido ou mal formado"); break;
          case JSON_ERROR_CTRL_CHAR: trigger_error("Retorno API: Erro de caractere de controle, possivelmente codificado incorretamente"); break;
          case JSON_ERROR_SYNTAX: trigger_error("Retorno API: Erro de sintaxe : $retorno"); break;
          case JSON_ERROR_UTF8: trigger_error("Retorno API: Caracteres UTF-8 malformado , possivelmente codificado incorretamente"); break;
        }

        # Retorno inválido
        if($json_error):
          trigger_error("Ocorreu um erro no acesso da API, o resultado esperado não é um JSON.\n\n ".$resultado);
        endif;

        # Retorno com erro
        if($retorno["erro"] != 0 && $trigger_error):
          if(isset($retorno["msg"])) trigger_error($retorno["msg"]. "\n\n". print_r($dados, true) );
          else trigger_error("Método ".$metodo.": - Erro desconhecido na API.");
        endif;

    else:
        $retorno = $resultado;
    endif;

    return $retorno;
  }

}


  function encoda_url($url) {
      return urlencode($url);
  }

  function formataData($data) {
      return date("d/m/Y", strtotime($data));
  }

  function selected($value1, $value2) {
    if($value1 == $value2) return 'selected="selected"';
    return '';
  }

  function ConverteData($Data){


    $Data = explode(' ',$Data);

    if (strstr($Data[0], "/")) {
        $d = explode ("/", $Data[0]);
        if(count($d) < 3) return '';
        if(!checkdate( $d[1], $d[0], $d[2])) return '';
        $rstData = "$d[2]-$d[1]-$d[0]";
        return "$rstData";
    }
    elseif(strstr($Data[0], "-")) {

        $d = explode ("-", $Data[0]);
        if(count($d) < 3) return '';
        if(!checkdate( $d[1], $d[2], $d[0])) return '';
        $rstData = "$d[2]/$d[1]/$d[0]";
        return "$rstData";
    }
    else{
      return "";
    }
  }

  h2o::addFilter('encoda_url');
  h2o::addFilter('formataData');
  h2o::addFilter('ConverteData');
  h2o::addFilter('highlight');
  h2o::addFilter('base64_encode');
  h2o::addFilter('n2lbr');
  h2o::addFilter('trim');
  h2o::addFilter('print_r');
  h2o::addFilter('time_elapsed_string');
  h2o::addFilter('selected');


?>
