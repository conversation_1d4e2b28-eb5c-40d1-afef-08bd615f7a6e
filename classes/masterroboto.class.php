<?php

class MasterRobotoAttachment {

  # Nome do arquivo completo e também sera o CID dos anexos dos emails
  public $filename = '';

  # Arquivo encodado em base64
  public $content = '';
  public $mime_type = '';
  public $mime_extension = '';

}

class MasterRobotoEmail {

    # Identificação
    public $account = 11;
    public $project = 4;
    public $identifier = '';

    # Destinatários
    public $mail_from = '';
    public $mail_to = '';
    public $mail_cc = '';
    public $mail_bcc = '';
    public $reply_to = '';

    # Assunto
    public $subject = '';

    # Corpo
    public $body_text = '';
    public $body_html = '';

    # Anexos
    public $attachments = null;

    public function attach($attachment) {

        # Adiciona o anexo
        if($this->attachments == null) $this->attachments = array();
        $this->attachments[] = $attachment;

    }

    public function send() {

        # URL da API de e-books
        $url = "http://masterroboto.jurid.com.br/api/mailmessage";

        $data = json_encode($this);

        # Cria o CURL
        $ch = curl_init();

        # Header
        $header = array();
        $header[] = 'Accept: application/json';
        $header[] = 'Content-Type: application/json';
        $header[] = 'Authorization: Token 313a0f07290955c3c88bc3f2da04041861e2820b';

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLINFO_HEADER_OUT, 1);

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        # POST
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data) ;

        # Resultado
        $resultado = curl_exec($ch);
        curl_close($ch);

        $retorno = json_decode($resultado, true);

        # Retorno inválido
        if(!$retorno):
         trigger_error("Ocorreu um erro no acesso da API do MasterRoboto, o resultado esperado não é um JSON.\n\n ".$resultado);
        endif;


    }

  }

?>
