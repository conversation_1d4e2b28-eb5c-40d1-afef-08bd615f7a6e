<?php

/**
 * Bootstrap para inicialização da aplicação com Swoole
 * Configura o ambiente e gerenciamento de configurações para compatibilidade com PHP 8.3
 */

require_once __DIR__ . '/ConfigManager.php';

class SwooleBootstrap 
{
    private static $initialized = false;
    
    /**
     * Inicializa a aplicação para uso com Swoole
     */
    public static function initialize()
    {
        if (self::$initialized) {
            return;
        }
        
        error_log("SwooleBootstrap: Inicializando aplicação para Swoole");
        
        // Verificar se Swoole está disponível
        if (!extension_loaded('swoole')) {
            error_log("SwooleBootstrap: Warning - Swoole não está carregado, usando modo tradicional");
            return;
        }
        
        // Configurar gerenciador de configurações
        $configManager = ConfigManager::getInstance();
        $configManager->initialize();
        $configManager->configureSwoole();
        
        // Registrar hooks para eventos do Swoole
        self::registerSwooleHooks();
        
        self::$initialized = true;
        error_log("SwooleBootstrap: Inicialização concluída");
    }
    
    /**
     * Registra hooks para eventos específicos do Swoole
     */
    private static function registerSwooleHooks()
    {
        // Hook para quando um worker inicia
        if (function_exists('swoole_set_process_name')) {
            error_log("SwooleBootstrap: Registrando hooks do Swoole");
        }
        
        // Registrar handler para limpeza de cache em caso de restart
        register_shutdown_function([self::class, 'cleanup']);
    }
    
    /**
     * Processa uma requisição no contexto Swoole
     */
    public static function handleRequest($request, $response)
    {
        try {
            // Detectar mudanças no host
            $configManager = ConfigManager::getInstance();
            $configManager->detectHostChange();
            
            // Validar integridade das configurações
            if (!$configManager->validateConfig()) {
                error_log("SwooleBootstrap: Configurações inválidas detectadas");
                throw new Exception("Erro na configuração da aplicação");
            }
            
            // Configurar variáveis superglobais a partir da requisição Swoole
            self::setupSuperglobals($request);
            
            // Garantir que as variáveis globais estão disponíveis
            $configManager->setGlobalVars();
            
            return true;
            
        } catch (Exception $e) {
            error_log("SwooleBootstrap Error: " . $e->getMessage());
            
            if ($response) {
                $response->status(500);
                $response->header('Content-Type', 'application/json');
                $response->end(json_encode([
                    'erro' => -1,
                    'mensagem' => 'Erro interno do servidor'
                ]));
            }
            
            return false;
        }
    }
    
    /**
     * Configura superglobais a partir da requisição Swoole
     */
    private static function setupSuperglobals($request)
    {
        // Configurar $_SERVER
        if (isset($request->server)) {
            foreach ($request->server as $key => $value) {
                $_SERVER[strtoupper($key)] = $value;
            }
        }
        
        // Configurar $_GET
        if (isset($request->get)) {
            $_GET = $request->get;
        } else {
            $_GET = [];
        }
        
        // Configurar $_POST
        if (isset($request->post)) {
            $_POST = $request->post;
        } else {
            $_POST = [];
        }
        
        // Configurar $_COOKIE
        if (isset($request->cookie)) {
            $_COOKIE = $request->cookie;
        } else {
            $_COOKIE = [];
        }
        
        // Configurar $_FILES
        if (isset($request->files)) {
            $_FILES = $request->files;
        } else {
            $_FILES = [];
        }
        
        // Garantir que HTTP_HOST está configurado
        if (!isset($_SERVER['HTTP_HOST']) && isset($request->header['host'])) {
            $_SERVER['HTTP_HOST'] = $request->header['host'];
        }
        
        // Configurar REQUEST_METHOD
        if (!isset($_SERVER['REQUEST_METHOD']) && isset($request->server['request_method'])) {
            $_SERVER['REQUEST_METHOD'] = strtoupper($request->server['request_method']);
        }
        
        // Configurar REQUEST_URI
        if (!isset($_SERVER['REQUEST_URI']) && isset($request->server['request_uri'])) {
            $_SERVER['REQUEST_URI'] = $request->server['request_uri'];
        }
    }
    
    /**
     * Limpeza quando a aplicação termina
     */
    public static function cleanup()
    {
        error_log("SwooleBootstrap: Executando limpeza");
        
        // Limpar cache se necessário
        if (self::$initialized) {
            ConfigManager::reset();
        }
    }
    
    /**
     * Reinicia configurações para nova requisição
     */
    public static function reset()
    {
        error_log("SwooleBootstrap: Resetando para nova requisição");
        
        // Limpar configurações
        ConfigManager::reset();
        
        // Permitir reinicialização
        self::$initialized = false;
    }
    
    /**
     * Verifica se a aplicação está rodando no Swoole
     */
    public static function isSwooleEnvironment()
    {
        return extension_loaded('swoole') && 
               (defined('SWOOLE_VERSION') || function_exists('swoole_version'));
    }
    
    /**
     * Obtém informações do ambiente Swoole
     */
    public static function getSwooleInfo()
    {
        if (!self::isSwooleEnvironment()) {
            return null;
        }
        
        return [
            'version' => function_exists('swoole_version') ? swoole_version() : 'unknown',
            'initialized' => self::$initialized,
            'process_id' => getmypid(),
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ];
    }
}