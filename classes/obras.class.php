<?php

require_once "conecta.class.php";

class Obra {

    public $id = 0;
    public $titulo = "";
    public $autor = "";
    public $dados_obra = "";
    public $dados_autor = "";
    public $capa = "";
    public $capa_hi_res = "";
    public $idcategoria = 0;
    public $preco = 0;
    public $qtdpaginas = 0;
    public $edicao = "";
    public $ano = 0;
    public $tipo_ISBN = "";
    public $ISBN = "";
    public $codigo_editora = "";
    public $status = 0; // Rascunho, toda obra começa com status 10.
    public $etapa = 0;
    public $porcentagemautor = 0;
    public $json_extra = '';
    public $assinatura = 0;
    public $revisao = 0;
    public $datapublicacao = '';
    public $datalancamento = '';
    public $paginaum = 0;
    public $slug = '';
    public $sumario_inicio = 0;
    public $sumario_fim = 0;

}

class Obras extends conecta {

    function __construct($link = NULL, $tenant = NULL) {
      parent::__construct($link, $tenant);
    }

    function __destruct() {
      parent::__destruct();
    }

    public $app = null;

    public $lista = array();

    public $registro_inicial = NULL;
    public $registro_quantidade = NULL;
    public $registro_total = NULL;

    # Filtros
    public $filtroID = NULL;
    public $filtroStatus = NULL;
    public $filtroEtapa = NULL;
    public $filtroSincronia = NULL;
    public $filtroBusca = '';
    public $filtroEbook = 1;
    public $filtroIDUsuario = NULL;
    public $filtroExibirCapa = false;
    public $filtroExibirInfo = true;
    public $filtroCategoria = false;

    public $filtroStatusPublicadas = false;
    public $filtroStatusRascunhos = false;

    public $filtroEmMontagem = false;
    public $filtroEmAguardo = false;

    # Where
    public $where = '';
    public $limit = '';
    public $innerjoin = '';
    public $order = 'ORDER by etapa ASC, id DESC';

    public function preencheFiltros($no_status = false) {

      $where = '';
      $innerjoin = '';
      $fields = '';

      # Filtro por IDS
      if($this->filtroID != NULL) $where .= sprintf(" AND (o.id IN (%s)) ", $this->escape_string(trim($this->filtroID)));

      # Adiciona as duas capas na busca e retorna o conteudo.
      if($this->filtroExibirCapa) $fields .= ", o.capa, o.capa_hi_res";

      # Adiciona os textos de dados de obra e de dados de autor
      if($this->filtroExibirInfo) $fields .= ", o.dados_obra, o.dados_autor";

      if($this->filtroStatus != NULL && is_numeric($this->filtroStatus)) $where .= sprintf(" AND (o.status = %d ) ", $this->filtroStatus);
      if($this->filtroEtapa != NULL && is_numeric($this->filtroEtapa)) $where .= sprintf(" AND (o.etapa = %d ) ", $this->filtroEtapa);

      if($this->filtroEmMontagem != NULL) $where .= sprintf(" AND (o.etapa = 5) ");
      if($this->filtroEmAguardo != NULL) $where .= sprintf(" AND (o.etapa <> 5 and o.etapa <> 10) ");

      if( strlen($this->filtroBusca) )
        $where .= sprintf(" AND (o.titulo like '%%%s%%' OR o.id = '%s')  ", $this->escape_string(trim($this->filtroBusca)), $this->filtroBusca );

      # Apenas ebooks
      if($this->filtroEbook != NULL) $where .= sprintf(" AND (o.ebook = %d) ", (int) $this->filtroEbook );

      # Status de publicada (1 e 2 )
      if($this->filtroStatusPublicadas != NULL) $where .= sprintf(" AND (o.status in (1,2) ) ");

      # Novos status, de rascunho e tal.
      if($this->filtroStatusRascunhos != NULL) $where .= sprintf(" AND (o.status in (0) ) ");

      # Paginação
      if($this->registro_inicial !== NULL && $this->registro_quantidade !== NULL)
        $this->limit = sprintf("LIMIT %s, %s", $this->registro_inicial, $this->registro_quantidade);

      $this->where = $where;
      $this->fields = $fields;
      $this->innerjoin = $innerjoin;
  }

    public function total() {

      $this->preencheFiltros();

      # Monta a query completamente
      $squery = sprintf("SELECT count(o.id) as quantidade FROM obras o %s
                         WHERE 1=1 %s", $this->innerjoin, $this->where);

      $query = $this->query($squery);
      $fetch = $query->fetch();

      return $fetch[0]["quantidade"];
    }


   public function listarObras() {
      /***
	*
	* Mario - 13/01/2021
	* Adicionado (etapa >= 0) para omitir obras com etapa negativa (pausada ou marcada para exclusao)
	*
      ***/

      # Limpando a variavel
      $this->lista = array();

      # Preenchendo filtros
      $this->preencheFiltros();

      # Monta a query completamente
      $squery = sprintf("SELECT o.codigo_editora, o.revisao, o.etapa, o.mastigando, o.datapublicacao, o.datalancamento, o.sincronizar, o.assinatura, o.preco, o.id, o.titulo, o.autor, o.idcategoria, o.qtdpaginas, o.edicao, o.ano, o.ISBN, o.status, o.porcentagemautor, o.json_extra, o.paginaum, o.qtdpaginas, o.sumario_inicio, o.sumario_fim
                                %s
                        FROM obras o
                        %s
                        WHERE 1=1 AND (etapa >= 0) %s %s %s", $this->fields, $this->innerjoin, $this->where, $this->order, $this->limit);

      $query = $this->query($squery);
      $fetch = $query->fetch();

      # Percorro todos os lançamentos fixos encontrados.
      foreach($fetch as $r):
        $this->lista[] = $r;
      endforeach;

      # Buscando o total de registros
      $this->registro_total = $this->total();

      return true;

  }

  public function listarObrasObjetos() {

      # Limpando a variavel
      $this->lista = array();

      # Preenchendo filtros
      $this->preencheFiltros();

      # Monta a query completamente
      $squery = sprintf("SELECT o.codigo_editora, o.revisao, o.etapa, o.mastigando, o.datapublicacao, o.datalancamento, o.sincronizar, o.assinatura, o.preco, o.id, o.titulo, o.autor, o.idcategoria, o.qtdpaginas, o.edicao, o.ano, o.ISBN, o.status, o.porcentagemautor, o.json_extra, o.paginaum, o.qtdpaginas, o.sumario_inicio, o.sumario_fim
                                %s
                        FROM obras o
                        %s
                        WHERE 1=1 %s %s %s", $this->fields, $this->innerjoin, $this->where, $this->order, $this->limit);

      $query = $this->query($squery);
      $fetch = $query->fetchobj();

      # Percorro todos os lançamentos fixos encontrados.
      foreach($fetch as $r):
        $this->lista[] = $r;
      endforeach;

      # Buscando o total de registros
      $this->registro_total = $this->total();

      return true;

  }


 public function gravarObra(&$obra) {
      # Inicia a transação.
      if(!$this->transaction()) return -1;

      # Insert
      if($obra->id <= 0):

        $query = $this->query(sprintf("INSERT INTO obras (titulo, autor, dados_obra, dados_autor, capa, capa_hi_res, idcategoria, preco, qtdpaginas, paginaum, edicao, ano, ISBN, codigo_editora, status, etapa, porcentagemautor, ebook, assinatura, revisao, datapublicacao, datalancamento, slug, json_extra,sumario_inicio,sumario_fim)
                                       VALUES ('%s', '%s', '%s', '%s', '%s', '%s', %d, '%s', %d, %d, '%s', %d, '%s', '%s', %d, %d, '%s', 1, %d, %d, %s, %s, '%s', '%s',%d,%d)",
                                        $this->escape_string($obra->titulo),
                                        $this->escape_string($obra->autor),
                                        $this->escape_string($obra->dados_obra),
                                        $this->escape_string($obra->dados_autor),
                                        addslashes($obra->capa),
                                        addslashes($obra->capa_hi_res),
                                        ($obra->idcategoria),
                                        $this->escape_float($obra->preco),
                                        ($obra->qtdpaginas),
                                        ($obra->paginaum),
                                        $this->escape_string($obra->edicao),
					($obra->ano),
#					$this->escape_string($obra->tipo_ISBN),
                                        $this->escape_string($obra->ISBN),
                                        $this->escape_string($obra->codigo_editora),
                                        ($obra->status),
                                        ($obra->etapa),
                                        $this->escape_float($obra->porcentagemautor),
                                        ($obra->assinatura),
                                        ($obra->revisao),
                                        $this->escape_date($obra->datapublicacao),
                                        $this->escape_date($obra->datalancamento),
                                        $this->escape_string($obra->slug),
					$this->escape_string($obra->json_extra),
					($obra->sumario_inicio),
					($obra->sumario_fim)
                                       ));

      # Update
      else:

        $obra->slug = slugify( sprintf("%s-%s", $obra->titulo, $obra->id) );
	

	if ($this->tenant["tenant"] != 'biblos' and $this->tenant["tenant"] != 'gz' and $this->tenant["tenant"] != 'hexag' and $this->tenant["tenant"] != 'cpiuris') {
	// retirar a capa e a capa_hi_res 
        $query = $this->query(sprintf("UPDATE obras SET
                                        titulo = '%s',
                                        autor = '%s',
                                        dados_obra = '%s',
                                        dados_autor = '%s',
                                        capa = '%s',
                                        capa_hi_res = '%s',
                                        idcategoria = %d,
                                        edicao = '%s',
					ano = %d,				
                                        ISBN = '%s',
                                        codigo_editora = '%s',
                                        paginaum = %d,
                                        preco = '%s',
                                        porcentagemautor = '%s',
                                        json_extra = '%s',
                                        qtdpaginas = %d,
                                        status = %d,
                                        etapa = %d,
                                        sincronizar = 1,
                                        assinatura = %d,
                                        revisao = %d,
                                        datapublicacao = %s,
                                        datalancamento = %s,
					slug = '%s',
					sumario_inicio = %d,
					sumario_fim = %d
                                        WHERE id = %d",

                                        $this->escape_string($obra->titulo),
                                        $this->escape_string($obra->autor),
                                        $this->escape_string($obra->dados_obra),
                                        $this->escape_string($obra->dados_autor),
                                        addslashes($obra->capa),
                                        addslashes($obra->capa_hi_res),
                                        ($obra->idcategoria),
                                        $this->escape_string($obra->edicao),
					($obra->ano),
#					$this->escape_string($obra->tipo_ISBN),
                                        $this->escape_string($obra->ISBN),
                                        $this->escape_string($obra->codigo_editora),
                                        ($obra->paginaum),
                                        (float) $this->escape_string($obra->preco),
                                        (float) $this->escape_string($obra->porcentagemautor),
                                        $this->escape_string($obra->json_extra),
                                        ($obra->qtdpaginas),
                                        ($obra->status),
                                        ($obra->etapa),
                                        ($obra->assinatura),
                                        ($obra->revisao),
                                        $this->escape_date($obra->datapublicacao),
                                        $this->escape_date($obra->datalancamento),
					$this->escape_string($obra->slug),
					($obra->sumario_inicio),
					($obra->sumario_fim),
                                        ($obra->id)

                                       ));
	} else { 
       $query = $this->query(sprintf("UPDATE obras SET
                                        titulo = '%s',
                                        autor = '%s',
                                        dados_obra = '%s',
                                        dados_autor = '%s',
                                        idcategoria = %d,
                                        edicao = '%s',
					ano = %d,
                                        ISBN = '%s',
                                        codigo_editora = '%s',
                                        paginaum = %d,
                                        preco = '%s',
                                        porcentagemautor = '%s',
                                        json_extra = '%s',
                                        qtdpaginas = %d,
                                        status = %d,
                                        etapa = %d,
                                        sincronizar = 1,
                                        assinatura = %d,
                                        revisao = %d,
                                        datapublicacao = %s,
                                        datalancamento = %s,
					slug = '%s',
					sumario_inicio = %d,
					sumario_fim = %d
                                        WHERE id = %d",

                                        $this->escape_string($obra->titulo),
                                        $this->escape_string($obra->autor),
                                        $this->escape_string($obra->dados_obra),
                                        $this->escape_string($obra->dados_autor),
                                        ($obra->idcategoria),
                                        $this->escape_string($obra->edicao),
					($obra->ano),
#					$this->escape_string($obra->tipo_ISBN),
                                        $this->escape_string($obra->ISBN),
                                        $this->escape_string($obra->codigo_editora),
                                        ($obra->paginaum),
                                        (float) $this->escape_string($obra->preco),
                                        (float) $this->escape_string($obra->porcentagemautor),
                                        $this->escape_string($obra->json_extra),
                                        ($obra->qtdpaginas),
                                        ($obra->status),
                                        ($obra->etapa),
                                        ($obra->assinatura),
                                        ($obra->revisao),
                                        $this->escape_date($obra->datapublicacao),
                                        $this->escape_date($obra->datalancamento),
					$this->escape_string($obra->slug),
					($obra->sumario_inicio),
					($obra->sumario_fim),
                                        ($obra->id)

                                       ));



	}

      endif;

      # se NÃO inseriu, então, retorna erro!
      if (!$query){
        $this->rollback();
        return -2;
      }

       # Recebe o registro
      if($obra->id == 0):
        $query = $this->query("select last_insert_id() as id");
        $fetch = $query->fetch();
        $r = $fetch[0];
        $obra->id = $r['id'];
      endif;

      # Comita a transação.
      return $this->commit();
    }

    public function atualizaEtapa($idobra, $etapa) {

      $squery = sprintf("UPDATE obras SET etapa = %d WHERE id = %d", $etapa, $idobra);
      $query = $this->query($squery);

      return $query->result();
    }

    public function salvaCrop($idobra, $crop) {

      $squery = sprintf("UPDATE obras SET json_extra = '%s' WHERE id = %d", $this->escape_string($crop), $idobra);
      $query = $this->query($squery);

      return $query->result();
    }

    public function salvaQtdepaginas($idobra, $total) {

      $squery = sprintf("UPDATE obras SET qtdpaginas = '%d' WHERE id = %d", $total, $idobra);
      $query = $this->query($squery);

      return $query->result();
    }




    public function listarImagens($idobra, $conteudo = false) {


      $add = '';
      if($conteudo) $add = ", imagem";

      $squery = sprintf("SELECT id, idobra, nomearquivo, sincronizar, CHAR_LENGTH(imagem) as size %s FROM imagens i WHERE idobra = %d ORDER BY nomearquivo ASC", $add, $idobra );
      $query = $this->query($squery);
      $fetch = $query->fetch();
      return $fetch;
    }

    public function deletaImagens($idobra) {
      $squery = sprintf("DELETE from imagens WHERE idobra = %d", $idobra);
      $query = $this->query($squery);
      return $query;
    }

    public function deletaPaginas($idobra) {
      $squery = sprintf("DELETE from paginas WHERE idobra = %d", $idobra);
      $query = $this->query($squery);
      return $query;
    }

    public function listarPaginas($idobra) {

      $squery = sprintf("SELECT id, numeropagina, alteradaem, texto FROM paginas p WHERE idobra = %d ORDER BY numeropagina ASC", $idobra );
      $query = $this->query($squery);
      $fetch = $query->fetch();
      return $fetch;
    }
    
    public function clearIndexSumario($idobra){
    	$this->transaction();
        $squery = sprintf("DELETE FROM sumarios WHERE html='%s'",'--SUMARIO_BASICO--');
	$this->query($squery);
    }
    
    public function basicoSumario($idobra,$indice,$nivel,$pagina){
    	$this->transaction();
        $squery = sprintf("INSERT INTO sumarios (idobra,indice,nivel,pagina,html) VALUES (%d, '%s', %d, %d, '--SUMARIO_BASICO--')", $idobra,$indice,$nivel,$pagina);
	$this->query($squery);
    }

    public function atualizaPaginaObra($id, $conteudo, $debug = false) {

      $this->transaction();

      # Pesquisa a página
      $squery = sprintf("SELECT * FROM paginas p WHERE id = %d", $id);
      $query = $this->query($squery);


      # Marcações
      $lista_marcacoes = $this->parseMarcacoes($conteudo);
      $marcacoes = array();
      foreach($lista_marcacoes as $m) $marcacoes[] = $m["marcacao"];
      $json_conteudos = json_encode($marcacoes);

      // Seleciona a pagina
      $pagina = $query->fetchone();

      # Atualiza a página
      $squery = sprintf("UPDATE paginas SET texto = '%s', json_conteudos = '%s', alteradaem = now() WHERE id = %d LIMIT 1", $this->escape_string($conteudo), addslashes($json_conteudos), $id);

      $query = $this->query($squery);

      # MARCACOES --------

      # Deletando as marcacoes dessa pagina
      $dquery = sprintf("DELETE FROM marcacoes WHERE idobra = %d AND pagina = %d", $pagina["idobra"], $pagina["numeropagina"] );
      $this->query($dquery);

      foreach($lista_marcacoes as $m):
        $squery = sprintf("INSERT INTO marcacoes VALUES (null, %d, '%s', %d, '%s')", $pagina["idobra"], addslashes(json_encode($m["marcacao"])), $pagina["numeropagina"], $this->escape_string( $m["html"] ) );
        $this->query($squery);
      endforeach;
      # ---

      # SUMARIOS --------

      # Vou apagar todos os sumários dessa obra, para essa pagina
      $dquery = sprintf("DELETE FROM sumarios WHERE idobra = %d AND pagina = %d", $pagina["idobra"], $pagina["numeropagina"] );

      $this->query($dquery);

      # Vou buscar todos os sumários, dentro do texto.
      $sumarios = $this->parseNiveis($conteudo);

      foreach($sumarios as $sumario):

        $sumario["texto"] = trim(preg_replace('/\s\s+/', ' ', $this->escape_string( $sumario["texto"] )));
        $sumario["texto"] = str_replace('\n', ' ', $sumario["texto"]);

        $squery = sprintf("INSERT INTO sumarios VALUES (%d, '%s', %d, %d, null, %d, '%s')", $pagina["idobra"], substr($sumario["texto"], 0, 400), $sumario["nivel"], $pagina["numeropagina"], $sumario["pagina_ordem"], $this->escape_string( $sumario["html"] ) );
        $this->query($squery);
      endforeach;

      # ---

      return $this->commit();

    }



    public function salvarImagem($idobra, $nomearquivo, $imagem) {

      # Verifico se existe essa imagem cadastrada
      $squery = sprintf("SELECT id FROM imagens WHERE idobra = %d AND nomearquivo = '%s'", $idobra, $this->escape_string($nomearquivo) );
      $query = $this->query($squery);

      if($query->rows() > 0):
          $iquery = sprintf("UPDATE imagens SET imagem = '%s', sincronizar = 1 WHERE idobra = %d AND nomearquivo = '%s' LIMIT 1", addslashes($imagem), $idobra,  $this->escape_string($nomearquivo));
      else:
          $iquery = sprintf("INSERT INTO imagens VALUES (null, %d, '%s', '%s', 1)", $idobra, $this->escape_string($nomearquivo), addslashes($imagem) );
      endif;

      $insert = $this->query($iquery);

      if(!$insert->result()):
          $this->rollback();
          return -1;
      endif;

      return $insert;

    }

    public function paginaObra($idobra, $pagina) {

      $squery = sprintf("SELECT * FROM paginas p WHERE idobra = %d AND numeropagina = %d", $idobra, $pagina);
      $query = $this->query($squery);

      $fetch = $query->fetchone();

      return $fetch;

    }

    public function imagemObra($idobra, $pagina) {

      $squery = sprintf("SELECT * FROM imagens i WHERE idobra = %d AND (nomearquivo = 'obra_Page_%s.png' OR nomearquivo = 'obra_Page_%s.png' OR nomearquivo = 'obra_Page_%s.png' OR nomearquivo = 'obra_Page_%s.png') ", $idobra, $pagina, str_pad($pagina, 4, "0", STR_PAD_LEFT), str_pad($pagina, 3, "0", STR_PAD_LEFT), str_pad($pagina, 2, "0", STR_PAD_LEFT) );

      $query = $this->query($squery);

      $fetch = $query->fetchone();

      return $fetch;

    }


  public function atualizaSumarioObra($idobra, $debug = false) {


      if($debug)
        file_put_contents('sumario.log', FILE_APPEND);

      # Peguei todas as imagens e todas as páginas
      $paginas = $this->listarPaginas( $idobra );

      foreach($paginas as $pagina):

        if($debug)
          file_put_contents('sumario.log', sprintf(" -------- Pagina ".$pagina["numeropagina"]." \n"), FILE_APPEND);

        $this->atualizaPaginaObra( $pagina["id"], $pagina["texto"], $debug );
      endforeach;

    }

    public function listarSumario($idobra) {

      $squery = sprintf("SELECT DISTINCT * FROM sumarios s WHERE idobra = %d order by pagina, pagina_ordem", $idobra);
      $query = $this->query($squery);

      $fetch = $query->fetch();

      return $fetch;

    }

    public function listarMarcacoes($idobra) {

      $squery = sprintf("SELECT * FROM marcacoes m WHERE idobra = %d order by pagina", $idobra);
      $query = $this->query($squery);

      $fetch = $query->fetch();

      return $fetch;

    }


    public function parseNiveis($texto) {

      preg_match_all('!<div([^>]*)class="nivel nivel([0-9]+)"([^>]*)></div>!', $texto, $matches);

      $retorno = array();

      $cont = 0;
      foreach($matches[0] as $div):

        $cont++;

        # Buscando o titulo
        preg_match('!nivel nivel([0-9]+)!', $div, $nivel_matches);
        $nivel = $nivel_matches[1]; // Primeiro grupo dos niveis

        # Buscando o titulo
        preg_match('!title="([^"]*)"!', $div, $title_matches);

        $title = isset($title_matches[1]) ? $title_matches[1] : ''; // Primeiro grupo dos niveis

        $retorno[] = array("texto" => $title, "nivel" => $nivel, "pagina_ordem" => $cont, "html" => $div ) ;

      endforeach;

      return $retorno;

    }


     public function parseMarcacoes($texto) {

      preg_match_all('!<div([^>]*)class="marcacao[ a-zA-Z0-9-_]*"([^>]*)></div>!', $texto, $matches);

      $retorno = array();

      $cont = 0;
      foreach($matches[0] as $div):

        $cont++;

        # Buscando o titulo
        preg_match('!data-json="([^"]*)"!', $div, $json_matches);

        $json64 = isset($json_matches[1]) ? $json_matches[1] : ''; // Primeiro grupo dos niveis

        $retorno[] = array(
                            "marcacao" => json_decode( base64_decode($json64), true ),
                            "html" => $div
                          );


      endforeach;

      return $retorno;

    }

    public function salvarPagina($idobra, $numeropagina, $texto, $update = true) {

      # Verifico se existe essa imagem cadastrada
      $squery = sprintf("SELECT id FROM paginas WHERE idobra = %d AND numeropagina = %d", $idobra, $numeropagina);
      $query = $this->query($squery);

      if($query->rows() > 0 && $update ):

          $iquery = sprintf("UPDATE paginas SET texto = '%s', alteradaem = now(), sincronizar = 1 WHERE idobra = %d AND numeropagina = %d LIMIT 1", $this->escape_string($texto), $idobra, $numeropagina );

      elseif($query->rows() == 0):
          $iquery = sprintf("INSERT INTO paginas VALUES (null, %d, %d, '%s', now(), 1, '')", $idobra, $numeropagina, $this->escape_string($texto) );

      else:

        // Ja está cadastrada e eu nao quero sobreescrever
        return true;

      endif;

      $insert = $this->query($iquery);

      if(!$insert->result()):
          $this->rollback();
          return -1;
      endif;

      return $insert;

    }

    public function resincronizarImagens($idobra) {

      $squery = sprintf("UPDATE obras SET sincronizar = 1 WHERE id = %d", $idobra);
      $query = $this->query($squery);

      $squery = sprintf("UPDATE imagens SET sincronizar = 1 WHERE idobra = %d", $idobra);
      $query = $this->query($squery);

      return true;

    }

    public function tratarArquivos($obra, $clonesumario = false) {

        $INC_TO_PX = 98;

	$extra = json_decode($obra->json_extra, true);

        $des_left = $extra["des_left"] * $INC_TO_PX;
        $des_top = $extra["des_top"] * $INC_TO_PX;
        $des_right = $extra["des_right"] * $INC_TO_PX;
        $des_bot = $extra["des_bot"] * $INC_TO_PX;
        $altura = $extra["altura"] * $INC_TO_PX;
        $largura = $extra["largura"] * $INC_TO_PX;

        $ajuste_vertical = isset($extra["ajuste_vertical"]) ? $extra["ajuste_vertical"] : 0;

        # Antes de buscar o sumário, eu vou atualizar ele
        # para salvar todos os htmls nos campos ao lado.

        #if(!$clonesumario)
        #  $this->atualizaSumarioObra($obra->id);

        # Abro a transaction
        $this->transaction();

        # Vou pegar os sumários
        $sumarios = $this->listarSumario($obra->id);
        $marcacoes = $this->listarMarcacoes($obra->id);

        # Vou agrupar esse resultado de sumário pela pagina2
        $sumarios_agrupados  = array();
        foreach($sumarios as $sumario) {
          if(!isset($sumarios_agrupados[$sumario["pagina"]])) $sumarios_agrupados[$sumario["pagina"]] = array();
          $sumarios_agrupados[$sumario["pagina"]][] = $sumario;
        }

        # Vou agrupar esse resultado de marcacoes pela pagina
        $marcacoes_agrupados  = array();
        foreach($marcacoes as $marcacao) {
          if(!isset($marcacoes_agrupados[$marcacao["pagina"]])) $marcacoes_agrupados[$marcacao["pagina"]] = array();
          $marcacoes_agrupados[$marcacao["pagina"]][] = $marcacao;
        }

        # Deleto as paginas do banco de dados.
        $this->deletaPaginas($obra->id);

        # Vou percorrer todas as paginas MAPAS
        // $basedir = ROOT_SYS . "/uploads/" . $this->tenant["tenant"] ."/" . $obra->id;
        $basedir = $_SERVER["DOCUMENT_ROOT"] . "/uploads/" . $this->tenant["tenant"] ."/" . $obra->id;

        $dir_paginas =  $basedir . "/paginas/";
        $scanned_directory_mapas = array_values(array_diff(scandir($dir_paginas), array('..', '.')));

        $dir_imagens = $basedir .  "/imagens/";
        $scanned_directory_imagens = array_values(array_diff(scandir($dir_imagens), array('..', '.')));
        $ini = $basedir . "/tratador.ini";

        $ini_obj = save_ini($ini, array(), 1, "maps", " Percorrendo os outros arquivos... ");

        $count = 0;
        foreach($scanned_directory_mapas as $file):



            # Verifico se nao eh u directorio
            if(preg_match("/pagina_([0-9]+)\.map/", $file)):

                # Ext-file
                $ext = pathinfo($file, PATHINFO_EXTENSION);

                if ($ext != 'map') continue;

                $count++;

                $ini_obj = save_ini($ini, $ini_obj, $count, "maps", "$file");

                # Aqui, vou cadastrar cada pagina no banco de dados
                # Mas antes vou verificar se ela ja esta cadastrada.
                $texto = '';

                # Conteu
                $content = file_get_contents($dir_paginas.$file);

                # Vou dividir o arquivo por linha
                $linhas = explode("\n", $content);

                # Parseio o HEADER com o tamanho da página
                $header = json_decode($linhas[0], true);

                # Fixando ligaturas
                # $linhas = fixing_ligaturas($linhas);
                #$linhas = juntando_palavras_proximas($linhas);



                # Gerando a proporcao que a imagem diminuiu (Tudo em INCH)

                $proporcao_img_x = ($largura - $des_left - $des_right) / $header["x2"];
                $proporcao_img_y = ($altura - $des_top - $des_bot) / $header["y2"];

                # Gerando a proporcao que o texto diminuiu (Tudo em INCH, mas aqui nao aplica as margens cortadas)
                $proporcao_txt_x = $largura / $header["x2"];
                $proporcao_txt_y = $altura / $header["y2"];

                $header_pos = formata_coordenadas_pagina($header, $proporcao_img_x, $proporcao_img_y);

                # Crio a div da pagina, formato2
                $texto .= sprintf("<div id=\"formato2\" style=\"position:relative;left:%dpx;top:%dpx;width:%dpx;height:%dpx\">",
                                    $header_pos["left"],
                                    $header_pos["top"],
                                    $header_pos["width"],
                                    $header_pos["height"]
                                 );



                # Percorro cada palavra
                for($i=1; $i < count($linhas); $i++ ):

                  # Parseio a palavra com o tamanho da página
                  $palavra = json_decode($linhas[$i], true);

                  if($palavra):

                    $palavra_pos = formata_coordenadas_palavra($palavra, $header, $proporcao_txt_x, $proporcao_txt_y, $des_left, $des_top);

                    # Adiciono no conteudo a div de palavra
                    $texto .= sprintf("<div class=\"palavra\" style=\"left:%dpx;top:%dpx;width:%dpx;height:%dpx\">%s</div>",
                                    $palavra_pos["left"],
                                    $palavra_pos["top"] + $ajuste_vertical,
                                    $palavra_pos["width"],
                                    $palavra_pos["height"],
                                    $palavra["txt"]
                                  );
                  endif;

                endfor;

                # Número da pagina
                $numero = ($header["numeropagina"]-1);

                if(isset($sumarios_agrupados[$numero]))
                  foreach($sumarios_agrupados[$numero] as $sumario) {
                    $texto .= $sumario["html"];
                  }

                if(isset($marcacoes_agrupados[$numero]))
                  foreach($marcacoes_agrupados[$numero] as $marcacao) {
                    $texto .= $marcacao["html"];
                  }

                # Fexo a div formato2
                $texto .= '</div>';

                $this->salvarPagina($obra->id, $numero, $texto, false);


            endif;


        endforeach;

        $ini_obj = save_ini($ini, $ini_obj, 0, "images", "Percorrendo as imagens");

        # Deleto todas as imagens do banco de dados
        $this->deletaImagens($obra->id);

        $count = 0;
        foreach($scanned_directory_imagens as $file):

          if(!preg_match("/obra_Page_([0-9]+)\.png/", $file))
            continue;

          $ini_obj = save_ini($ini, $ini_obj, $count, "images", "$file");

          # Extensao
          $exts = explode(".", $file);
          $ext = $exts[1];

          # Renomenado todos os arquivos
          preg_match_all('([0-9]+)', $file, $matches);
          if($matches):

            $last_number = (int) $matches[0][ count($matches[0]) - 1 ];

            $newname = "obra_Page_".$last_number.".".$ext;

            rename($dir_imagens.$file, $dir_imagens.$newname);

            # Verifico se nao eh u directorio
            if(is_file($dir_imagens.$newname)):


              $count++;

              $content = file_get_contents($dir_imagens.$newname);

              $this->salvarImagem($obra->id, $newname, $content);

            endif;

          endif;

        endforeach;

      $ini_obj = save_ini($ini, $ini_obj, $obra->qtdpaginas, "complete");

      # Abro a transaction
      $this->commit();

    }


    public function mastigarObra($obra) {

      global $SETTINGS;

      $BIN = $SETTINGS["bin"];      

      # Diretório de uploads dessa obra
      // $dirname = ROOT_SYS . "uploads/" . $this->tenant["tenant"] ."/" . $obra->id . "/";
      $dirname = $_SERVER["DOCUMENT_ROOT"] . "/uploads/" . $this->tenant["tenant"] ."/" . $obra->id . "/";

      # Paths
      $pdf = $dirname . $obra->id . ".pdf";
      $target_dir_mapas = $dirname . "paginas";
      $target_dir_imagens = $dirname . "imagens";
      $target_dir_textos = $dirname . "textos";
      $ini = $dirname . "mastigador.ini";

      # Criando os paths
      if (!file_exists($target_dir_textos)) mkdir($target_dir_textos, 0777, true);
      if (!file_exists($target_dir_imagens)) mkdir($target_dir_imagens, 0777, true);
      if (!file_exists($target_dir_mapas)) mkdir($target_dir_mapas, 0777, true);

      # Limpando os diretórios
      #array_map('unlink', glob("$target_dir_textos/*"));
      #array_map('unlink', glob("$target_dir_imagens/*"));
      #array_map('unlink', glob("$target_dir_mapas/*"));

      //    if (file_exists($ini)) unlink($ini);

      # Iniciando o processo
      $ini_obj = get_ini($ini);
      if(!$ini_obj) $ini_obj = save_ini($ini, array(), 1, "extracting", " Iniciando extração... ");

      if(!isset($ini_obj["pagina"]))
        $ini_obj = save_ini($ini, array(), 1, "extracting", " Iniciando extração... ");

      # Pego a pagina inicial do obj mastigador.ini
      # que poderia vir do arquivo salvado ou do init de 1
      $pagina_inicial = $ini_obj["pagina"];

      # CROP REFERENCE
      $info_command = sprintf("$BIN/pdfinfo %s -box  2>&1",$pdf);    

      exec($info_command, $output);

      foreach($output as $o) {
        if( stristr($o, "CropBox")) $cropbox = preg_split("/\s+/", $o);
        if( stristr($o, "MediaBox")) $mediabox = preg_split("/\s+/", $o);
        if( stristr($o, "Pages")) $qtdpagina = preg_split("/\s+/", $o);
      }


      $json_extra = json_decode($obra->json_extra, true);
      if (!$json_extra) $json_extra = [];

      if(isset($cropbox) && count($cropbox) && isset($mediabox) && count($mediabox) && isset($qtdpagina) && count($qtdpagina)){
      $extra = array(
            "des_left" => ($cropbox["1"]  / 72),
            "des_top" => ($cropbox["2"] / 72),
            "des_right" => (($mediabox["3"] - $cropbox["3"]) / 72),
            "des_bot" => (($mediabox["4"] - $cropbox["4"]) / 72),
            "largura" => ($mediabox["3"] / 72),
            "altura" => ($mediabox["4"] / 72)
    );
      }else{ 
	$qtdpagina = [];
	$cropbox = [];
	$mediabox = [];
	$extra = [
		"des_left"=>null,
		"des_top"=>null,
		"des_right"=>null,
		"des_bot"=>null,
		"largura"=>null,
		"altura"=>null
	];
      }      

      $json_extra = array_merge($json_extra, $extra);

      //FIX
      //EM CASO DE DESALINHAMENTO DAS SELECOES DE PALAVRAS
/*      if($obra->id == 50){
	      $json_extra['des_top']= 0.5;
	      $json_extra['des_bot'] = 0.55;
	      $json_extra['des_left'] = 0.5;
	      $json_extra['des_right'] = 0.5;
      }
 */
      $obra->json_extra = json_encode($json_extra);

      $obra->qtdpaginas = count($qtdpagina) > 1 ? $qtdpagina[1] : 0;
      $obra->status = 0;

      # Salvo essa obra, antes de tratar os arquivos
      # Pois atualizei os cropbox dela.
      $this->salvaCrop($obra->id, $obra->json_extra);
      $this->salvaQtdepaginas($obra->id, $obra->qtdpaginas);

      # Percorrendo cada pagina do pdf
      for($i = $pagina_inicial; $i <= $obra->qtdpaginas; $i++) {    

        $ini_obj = save_ini($ini, $ini_obj, $i, "extracting", " Extraindo mapa da pagina $i ");

	# Extraindo os mapas
	$mapa_command = sprintf("cd $dirname && $BIN/pdftotext -f %d -l %d -bbox -htmlmeta %s %s/pagina_html_%d.map", $i, $i, $pdf, $target_dir_mapas, $i);

	$ini_obj = save_ini($ini, $ini_obj, $i, "extracting", $mapa_command);

        exec($mapa_command);

        $ini_obj = save_ini($ini, $ini_obj, $i, "extracting", " Extraindo os textos da pagina $i ");

        # Extraindo os textos puros
        $mapa_command = sprintf("cd $dirname && $BIN/pdftotext -f %d -l %d -layout %s %s/%d.txt", $i, $i, $pdf, $target_dir_textos, $i);
        $ini_obj = save_ini($ini, $ini_obj, $i, "extracting", $mapa_command);
        exec($mapa_command);

        $ini_obj = save_ini($ini, $ini_obj, $i, "extracting", " Extraindo imagem da pagina $i ");


        # Extraindo todas as imagens
        $imagens_command = sprintf("cd $dirname && $BIN/pdftoppm -r 150 -singlefile -f %d -l %d -png %s %s/obra_Page_%d -cropbox -aa yes ", $i, $i, $pdf, $target_dir_imagens, $i);


        $ini_obj = save_ini($ini, $ini_obj, $i, "extracting", $imagens_command);
        exec($imagens_command);

        $ini_obj = save_ini($ini, $ini_obj, $i, "extracting", " Padronizando o mapa da página $i ");

        # Ajustando o arquivo de mapas
	$html = file_get_html($target_dir_mapas . "/pagina_html_" . $i . ".map");

        $mapa = array();
        $texto = "";

        # Extaindo página
        foreach($html->find('page') as $e)
          $mapa[] = array("numeropagina" => $i, "x1" => "0", "x2" => $e->width, "y1" => "0", "y2" => $e->height);

        # Extraindo palavras
        foreach($html->find('word') as $e) {

		$text = $e->plaintext;

	        $mapa[] = array("txt" => $text, "x1" => ($e->xmin), "x2" => ($e->xmax), "y1" => ($mapa[0]["y2"]) - ($e->ymax), "y2" => ($mapa[0]["y2"]) - ($e->ymin));
	}

        # Forco a todos terem aspas, convertendo-os para string.
        foreach($mapa as &$m) {
          foreach($m as &$p) {
            $p = (string) $p;
          }
        }

	# Montando o mapa e salvnado de novo
        $novo_mapa = "";
        for($c = 0; $c < count($mapa); $c++) {
          $novo_mapa .= sprintf("%s\n", json_encode($mapa[$c], JSON_UNESCAPED_UNICODE));
        }
        $novo_mapa = trim($novo_mapa, "\n");

        # Explodo todos pelo \n
        $linhas = explode("\n", $novo_mapa);
        $header = $linhas[0];

        # Passo as funcoes de correcões.
        //$linhas = fixing_ligaturas($linhas);
        $linhas_txt = juntando_palavras_proximas( array_slice($linhas, 1) );

        $linhas = [$header];
        foreach($linhas_txt as $l) $linhas[] = $l;

        # Volto a ser texto denovo
        $novo_mapa = implode("\n", $linhas);

        $ini_obj = save_ini($ini, $ini_obj, $i, "extracting", " Salvando o mapa da página $i ");

        file_put_contents($target_dir_mapas . "/pagina_" . $i . ".map", $novo_mapa);

        //$ini_obj = save_ini($ini, $ini_obj, $i, "extracting", " Salvando o texto da da pagina $i ");
        //file_put_contents($target_dir_textos . "/pagina_" . $i . ".map", trim($texto) );

        # Extraindo todas as imagens
        $crush = sprintf("cd $dirname && $BIN/pngcrush  -reduce -force -ow %s/obra_Page_%d.png", $target_dir_imagens, $i);



        $ini_obj = save_ini($ini, $ini_obj, $i, "extracting", $crush);
        exec($crush);

      }


    }

    public function getPagina($pagina,$idobra){
	    $squery = sprintf("SELECT texto FROM paginas WHERE numeropagina = %d AND idobra = %d",$pagina,$idobra);
	    $query = $this->query($squery);
	    $fetch = $query->fetch();
	    return $fetch;
    }

    public function excluirObra($idobra) {
      if (empty($idobra) OR !is_numeric($idobra)) return false;

      // Se etapa for = 10 ou < 1, nao fazer nada, return false
      $squery = sprintf("SELECT id,etapa FROM obras WHERE (etapa >= 10 OR etapa < 1) AND id=%d", $idobra);
      $query = $this->query($squery);
      $fetch = $query->fetch();
      if (count($fetch) > 0) return false;

      // Colocar etapa com valor negativa correspondente
      $squery = sprintf("UPDATE obras SET etapa=(etapa * -1) WHERE id=%d AND etapa < 10 AND etapa > 0", $idobra);
      $query = $this->query($squery);
      file_put_contents("debug.log", "\nQuery seta etapa negativa: ".$squery, FILE_APPEND);

      // marcar obra p/ exclusao na base de dados do monitor
      // Cadastrar obra na tabela obras_excluir
      // Conectar na base exclusiva do monitor
      $dblink_monitordeobras = mysqli_connect("10.1.1.23","jurid",'!kashmir$s!',"monitordeobras");
      $dbconn_monitordeobras = new Conecta($dblink_monitordeobras);
      $squery = sprintf("INSERT INTO obras_excluir (idobra, tenant, idusuario, email, status) VALUES (
				%d,
			 	'%s',
				%d,
				'%s',
				1)",
				$idobra,
				$this->app->settings['tenant']['tenant'],
				$this->app->session['usuario_logado']['id'],
				$this->app->session['usuario_logado']['usuario']
			);
      $dbconn_monitordeobras->query($squery);

      return true;
    }
};


