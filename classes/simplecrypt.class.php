<?php

/*

Sistema melhorado de criptografia:

Baseando-se em tabela de conversão de caracteres, este sistema trabalha com dois
elementos: uma tabela de conversão direta caracter->caracter criptografado e num
vetor que indica qual linha da tabela será utilizada para converter cada carac-
ter.

*/

 define('KB',1024);
 define('MB', KB * KB);

  $THE_MATRIX = array(array(0xAF,0x66,0x09,0x35,0xE0,0x4D,0xE3,0x63,0x49,0x29,0x98,0x1A,0xFA,0x70,0xC3,0x85,0xB7,0x45,0xAA,0x32
                ,0x3B,0xF1,0x96,0x19,0xD4,0x6F,0x06,0x08,0xC9,0xC7,0x95,0xCE,0x83,0x05,0xAB,0x17,0xC5,0x5B,0x9A,0xC1
                ,0x5C,0xBB,0x4F,0x72,0xAC,0x8C,0x40,0x22,0x52,0x8D,0x8F,0x3F,0x16,0x57,0x84,0x86,0xFC,0x02,0xE7,0x4C
                ,0x2A,0xBD,0xF8,0x10,0xB8,0x7C,0xD9,0x25,0x54,0xC4,0xD5,0x5D,0x65,0x74,0x55,0xF9,0x7E,0xF7,0x9F,0x5E
                ,0x80,0x11,0xBF,0xA1,0xA7,0x97,0x59,0xB2,0x7A,0xA3,0x71,0x9B,0x64,0xC2,0xE9,0xF5,0x1B,0x13,0xD8,0xA6
                ,0xE6,0x26,0xFE,0xB6,0x48,0x7B,0xD2,0xA5,0x28,0x6D,0x6A,0x36,0x62,0x12,0x0D,0x60,0xEB,0x68,0xF4,0xF2
                ,0x4B,0x82,0xD0,0x89,0xC8,0x77,0x81,0x75,0xB0,0x61,0x2C,0xBA,0xB9,0xD7,0xCD,0x31,0x37,0x15,0x1E,0x14
                ,0xE5,0x9C,0x20,0xD3,0x43,0x9D,0x04,0x03,0x39,0x23,0x5F,0x92,0x94,0xDF,0xBC,0x88,0xC0,0xA0,0x00,0xEA
                ,0x1D,0xCF,0x1F,0x8E,0x8A,0xDE,0x58,0x38,0x6E,0xD1,0x30,0x27,0xA2,0xEC,0x3E,0xE4,0xB4,0x33,0x4A,0xED
                ,0x2B,0xDB,0xB5,0x0A,0xAD,0x87,0xBE,0x6C,0x7D,0x3C,0x42,0xCA,0xFB,0x76,0x2E,0xF0,0x56,0x2F,0xE1,0xE2
                ,0xCB,0x53,0x44,0x4E,0xA9,0x34,0x41,0x99,0x0E,0x0F,0xA4,0xDD,0x2D,0x0C,0x5A,0x47,0x18,0xEF,0xEE,0xE8
                ,0x69,0x0B,0x73,0x46,0xAE,0x9E,0x50,0x6B,0x7F,0xDA,0x93,0x91,0xFF,0xF3,0xF6,0x90,0x07,0x1C,0xCC,0xB1
                ,0x8B,0x21,0x3D,0xC6,0x3A,0x67,0x24,0xB3,0x79,0x01,0xA8,0xD6,0xFD,0xDC,0x51,0x78),
                array(0x0D,0xFB,0x87,0x99,0xA2,0x69,0x79,0x0E,0xB8,0x2A,0x5D,0xDA,0xBE,0x13,0x0F,0x15,0x08,0xD1,0x00,0x56
                ,0x70,0x51,0xAE,0x2C,0xF8,0xCC,0x17,0x8E,0xCA,0x9B,0x83,0x19,0x63,0xEA,0xA8,0x94,0x57,0x93,0x18,0xC3
                ,0x6C,0xBD,0xC0,0x46,0x7A,0x6D,0xBC,0x38,0x21,0x12,0x4B,0x34,0x91,0x26,0x06,0x6E,0xC1,0x40,0xDD,0x96
                ,0xAB,0xC9,0x7E,0x03,0x8A,0x23,0x11,0x5A,0x82,0x43,0xF1,0x0C,0xA3,0x04,0x53,0x44,0xC2,0x65,0x50,0x9C
                ,0xD3,0xD5,0xD0,0xCF,0xA6,0x8F,0x92,0x3F,0x3B,0xB1,0x58,0x7F,0x52,0x4E,0x4A,0xE4,0x4C,0x1D,0xCB,0xF0
                ,0x84,0xB2,0x2E,0xE1,0x25,0xC7,0x77,0x37,0xC8,0xD9,0x86,0x39,0x30,0x3A,0x5B,0xFC,0xED,0x78,0x64,0x74
                ,0x95,0xE2,0xD2,0x48,0xF6,0xF7,0xEF,0xD4,0x59,0xA5,0x66,0xA4,0xAF,0x85,0x3E,0x3D,0x67,0xAC,0xD6,0x97
                ,0x8D,0xE6,0x07,0x55,0xEB,0x35,0x31,0x2D,0x28,0x3C,0x33,0x1B,0x9E,0x4F,0x9D,0x29,0x73,0x9A,0x62,0xD8
                ,0xBF,0x05,0xEE,0x89,0x54,0x22,0x7B,0xCD,0xC6,0x68,0x16,0x7D,0x2F,0xB4,0x75,0x10,0x0B,0xFA,0x32,0x88
                ,0x45,0xF4,0x9F,0x8B,0x2B,0x47,0xB0,0x7C,0x27,0x80,0xB7,0x1F,0xDB,0x6A,0x02,0x14,0x09,0x6F,0x4D,0xFE
                ,0x42,0x1C,0xE3,0xDE,0x36,0xF5,0x49,0xE9,0xB3,0xCE,0x5C,0xE8,0x98,0x0A,0xB5,0x6B,0xB9,0xD7,0x20,0x76
                ,0x1E,0xBA,0x71,0x90,0x72,0xF3,0xE7,0xA9,0xDF,0xF9,0xA1,0xC5,0xF2,0xE5,0xAA,0xB6,0xBB,0x24,0xA7,0xAD
                ,0x8C,0x5E,0xDC,0xFD,0xC4,0x61,0x01,0x81,0xFF,0x5F,0xEC,0x41,0xA0,0xE0,0x1A,0x60),
                array(0x44,0xD8,0xB3,0x6B,0x5D,0xA6,0x56,0x51,0x22,0x9F,0xA4,0x25,0x5F,0xE1,0x37,0xC8,0xD4,0xBE,0x4C,0x65
                ,0x57,0xC3,0xCD,0x0C,0x4D,0xC1,0x77,0xD6,0x39,0xEB,0xFF,0x78,0xED,0x6E,0x69,0xE9,0x09,0x06,0xA2,0xCE
                ,0x82,0x1C,0x1D,0xC9,0x9C,0x9D,0x97,0x3B,0xDF,0x12,0x3D,0x80,0x08,0x27,0x2B,0x5B,0x2C,0x15,0x5C,0xF4
                ,0x89,0xD7,0x42,0x60,0x6A,0xF2,0x86,0x83,0x64,0x46,0x98,0x67,0x75,0x3A,0x0B,0xFC,0x24,0x3C,0xAB,0x0E
                ,0xDD,0x59,0x1A,0xD2,0xFD,0x9E,0x2A,0x45,0x99,0x6F,0x52,0xE2,0x1E,0xAE,0xF8,0x36,0xE5,0xC4,0xF5,0x66
                ,0xD5,0xFA,0x72,0x79,0xBB,0x7D,0xE7,0xDE,0x8D,0x53,0x34,0xB2,0xF6,0xF3,0x93,0x81,0xFE,0xA9,0x7C,0x02
                ,0xB9,0x68,0x5E,0x4E,0xC7,0x6D,0x6C,0x71,0xA3,0x3E,0xA5,0x8B,0x26,0x19,0x88,0xEA,0xFB,0x13,0x07,0x85
                ,0xA7,0x94,0x70,0x74,0xCA,0x91,0xF7,0xC2,0xE3,0xA0,0x0D,0x4A,0x29,0x2F,0xB6,0x16,0x17,0x8A,0x7F,0xD3
                ,0xE0,0xB4,0x00,0x33,0x01,0x9B,0xB7,0x31,0x1F,0xEE,0xB5,0x28,0x58,0x4F,0x76,0xD9,0xD1,0x7A,0x0F,0xF0
                ,0x96,0x8C,0x7E,0x35,0x1B,0xEF,0x32,0xA8,0x7B,0xAD,0x5A,0x55,0x04,0x8F,0xCC,0x3F,0x2E,0x41,0x95,0xA1
                ,0xDC,0x10,0x90,0xD0,0xAF,0x61,0x73,0xAC,0x0A,0x92,0xAA,0x21,0x87,0x63,0x30,0xC5,0xBC,0x23,0x62,0x54
                ,0x8E,0x43,0x84,0xBD,0xF9,0xCF,0x03,0x05,0xE8,0x50,0x2D,0xB8,0xE6,0xC0,0x20,0xBA,0xF1,0x40,0xB0,0x38
                ,0xDB,0xBF,0xB1,0x4B,0x49,0xE4,0xEC,0xCB,0x11,0x9A,0x14,0xDA,0x48,0x18,0x47,0xC6),
                array(0x03,0x24,0x14,0x1B,0xD2,0xA7,0x8C,0x4B,0x3D,0x63,0x83,0xE6,0x29,0xFE,0x78,0xC4,0xF1,0x2C,0x5A,0xD1
                ,0x02,0x50,0xC5,0x90,0x8D,0xAD,0x7F,0xAA,0xDF,0xB0,0xA2,0x79,0x9A,0xD3,0x0E,0xA8,0x37,0xF5,0x99,0x97
                ,0x38,0x1F,0x3A,0x9F,0x6E,0x86,0x91,0xC0,0x0F,0x31,0x4F,0x58,0x73,0xE0,0x64,0xF4,0xA3,0x6C,0xF6,0x41
                ,0xDB,0xBE,0x6A,0x0B,0xB6,0x20,0xAF,0x33,0x19,0x06,0x5C,0x96,0x26,0x4E,0x8E,0xB2,0x01,0xC9,0xF8,0x1E
                ,0xFF,0x5D,0x12,0x80,0x82,0x65,0x36,0x3B,0x89,0xA1,0xEA,0xB8,0xF7,0x67,0x54,0xFB,0x15,0xE1,0x48,0x0C
                ,0xC2,0xBC,0x43,0xD6,0xCA,0xEE,0x09,0x69,0xC6,0x30,0x57,0x0A,0x6B,0xDD,0x7D,0xBA,0x46,0x61,0xEF,0xE5
                ,0x59,0xA0,0x7B,0x23,0x75,0x7E,0x4D,0x7A,0x1C,0x9B,0x8B,0xF0,0x05,0xCE,0xB5,0xEB,0xD9,0xAB,0x49,0x84
                ,0xE7,0xE8,0xD5,0xF3,0xD8,0x34,0xC1,0x10,0x92,0x47,0xFC,0x81,0x56,0x42,0x08,0xD7,0x32,0x62,0x77,0x13
                ,0x60,0x87,0xA9,0xAE,0x00,0xE4,0xF9,0xBB,0x11,0xC3,0x9D,0x2B,0x85,0x66,0x71,0x1D,0x45,0x25,0xDA,0xC7
                ,0x2E,0x07,0xDC,0x1A,0xB3,0xB7,0x93,0x5B,0xB9,0x70,0x3E,0xB1,0x4C,0x9C,0x40,0x55,0xFD,0x6D,0xE2,0xFA
                ,0x2A,0x68,0xD0,0x27,0xE3,0xBD,0x2D,0xDE,0x2F,0xED,0xCC,0xE9,0xCD,0xF2,0x8A,0x98,0x35,0xB4,0xEC,0xA5
                ,0xA4,0x16,0x3C,0x28,0x76,0x3F,0x6F,0xD4,0x53,0x94,0x5E,0x88,0x44,0xA6,0x5F,0xC8,0x9E,0x8F,0x04,0x21
                ,0x0D,0x4A,0x18,0xCB,0x22,0x17,0xCF,0xAC,0x52,0x7C,0x39,0xBF,0x51,0x72,0x74,0x95),
                array(0x9F,0x7C,0x61,0x96,0x8F,0xA8,0xA7,0xEE,0x51,0xE3,0x26,0x56,0x65,0xE7,0x24,0x0C,0x46,0xC2,0x9A,0x4F
                ,0xB0,0x49,0x3B,0xD7,0x7F,0x34,0x83,0x9B,0x86,0xFE,0xF7,0x05,0xCC,0xA2,0x52,0x31,0x0E,0xDF,0x43,0x4A
                ,0x6F,0x0A,0x82,0x18,0xB8,0x53,0x5B,0x1D,0x2E,0x87,0xBE,0xF8,0x8D,0x20,0x42,0x19,0xB2,0x25,0xCA,0xE6
                ,0x7B,0x5D,0xFD,0x81,0x5A,0xEB,0x3A,0x13,0x3F,0xAB,0x74,0xEA,0xF9,0xE9,0x8C,0x30,0xD1,0xD3,0x63,0x6A
                ,0x7A,0xBB,0xA1,0xE5,0xCB,0x14,0xC4,0xF6,0x72,0x35,0x84,0x0D,0x07,0x3C,0x94,0x6B,0xD5,0xEF,0xED,0x55
                ,0xDC,0x88,0x41,0x7E,0x21,0x7D,0x22,0x78,0xC0,0x06,0x71,0x02,0x39,0xAF,0x6C,0xB4,0x2A,0x54,0x15,0xC3
                ,0xAC,0x1F,0xA6,0x47,0xD9,0x73,0x3E,0x93,0xDA,0x44,0x75,0x00,0xB9,0x95,0x5F,0xBC,0xC7,0xC1,0x16,0x03
                ,0x23,0x64,0xA0,0x69,0xF2,0xBA,0xB7,0xD0,0x2D,0x77,0xA9,0xF4,0x99,0x40,0xC8,0x1B,0x28,0x70,0x76,0x8A
                ,0xF1,0x80,0x97,0x9D,0x1E,0x1C,0x6E,0x4C,0x0F,0xF3,0x3D,0xC9,0x59,0x57,0xBF,0x12,0xA5,0xC6,0xCE,0x45
                ,0x60,0xFC,0xB3,0xB6,0xB1,0x0B,0x92,0x1A,0xA4,0xEC,0x36,0x01,0x08,0x29,0xAE,0x2C,0x6D,0x79,0x5C,0xF5
                ,0x2F,0x38,0x89,0x8E,0xE1,0xAD,0x2B,0xDD,0xD6,0x67,0x9E,0x33,0xAA,0x5E,0x37,0x66,0x17,0x04,0xBD,0xF0
                ,0x9C,0x4E,0xD4,0x91,0xDB,0x48,0xB5,0xC5,0x11,0xA3,0x09,0x90,0x68,0x8B,0xE2,0x98,0xE0,0x85,0x50,0xDE
                ,0xD8,0xD2,0x58,0xCD,0xCF,0xFB,0x4D,0xFA,0x4B,0xFF,0x62,0xE4,0x32,0xE8,0x10,0x27),
                array(0x96,0x39,0x84,0x87,0x7F,0xF3,0xF7,0xB1,0x91,0x92,0x61,0xEE,0x70,0x2B,0x13,0x50,0x86,0x94,0x01,0xD0
                ,0x8E,0x3E,0x21,0xB5,0xA2,0x71,0x4D,0x28,0xD1,0x07,0x3C,0x23,0xAF,0xC5,0xBB,0x77,0x42,0x64,0xA6,0x29
                ,0x3D,0x48,0xBF,0x43,0xB3,0xC3,0xA9,0xEF,0x0E,0x26,0xEC,0x88,0xF2,0x9E,0xA1,0xD3,0x80,0xE8,0x97,0x09
                ,0x5D,0x7A,0xD7,0xFB,0xA8,0x33,0xF1,0x99,0xA7,0x0A,0xFC,0xA4,0x47,0x5A,0x0C,0x8B,0x19,0xB2,0x2A,0xCE
                ,0x59,0xE7,0x4B,0x74,0xCC,0x5B,0xA0,0xD2,0xAB,0x9F,0x67,0xB7,0x31,0x8D,0x6F,0x58,0x24,0x75,0x1E,0xEA
                ,0x63,0xAE,0x06,0x0F,0x8F,0xCA,0xC8,0x1F,0x27,0x6D,0x57,0xEB,0x20,0x16,0xF6,0x9D,0x45,0x5C,0xBC,0x4E
                ,0x55,0xD4,0x5E,0x1D,0x04,0x35,0x4A,0xE5,0x25,0xD8,0x2E,0x00,0xFE,0x02,0x9A,0x08,0xF4,0xCF,0x32,0x7D
                ,0x49,0xDC,0x3F,0x3A,0xFA,0x56,0xCD,0x46,0xC0,0x51,0x4C,0x11,0xE2,0x60,0xDF,0xBE,0xC1,0x8C,0xAA,0x17
                ,0xFF,0x14,0x6A,0x4F,0x9C,0x41,0x8A,0x37,0x30,0xAD,0x18,0xC7,0xE0,0x34,0xC4,0x5F,0x44,0xD6,0xCB,0xB4
                ,0x81,0x40,0x72,0x1A,0xF8,0x69,0xE3,0x7B,0x83,0x76,0xE6,0x79,0x2F,0xF5,0xC6,0x38,0xE9,0xA5,0xA3,0xB9
                ,0x12,0x85,0x03,0x3B,0xF9,0xF0,0x66,0x1C,0x2D,0xB8,0xAC,0xB0,0x15,0x98,0x6C,0x0D,0xD5,0xFD,0x62,0xDA
                ,0xD9,0x65,0x36,0xDE,0xBD,0x68,0x7E,0x6B,0xBA,0x73,0x93,0xC9,0x90,0x95,0xDD,0xB6,0x0B,0x53,0x54,0x05
                ,0x6E,0x2C,0x22,0x9B,0x10,0xE1,0x78,0xDB,0x89,0x1B,0xED,0x52,0x7C,0xE4,0x82,0xC2),
                array(0x26,0xC2,0xCE,0x08,0xF2,0xCA,0x99,0x14,0x7D,0x29,0xBA,0x18,0x19,0x13,0x1A,0xDB,0x39,0x05,0x4D,0xE4
                ,0x95,0xD3,0xB2,0x64,0xF5,0xD5,0xF9,0x25,0xBC,0xC5,0xA5,0xCD,0x5E,0x9C,0xC9,0xF4,0xDD,0x98,0xEA,0x12
                ,0x38,0xF0,0xB8,0x3A,0x4B,0x47,0x11,0x85,0x71,0x52,0x1C,0x65,0x74,0x6D,0x72,0x37,0x82,0x33,0xAD,0x36
                ,0x0B,0x06,0x94,0x89,0x3C,0x5F,0x67,0x7F,0xA1,0x0A,0xA4,0x91,0xD4,0xFA,0xB4,0x01,0x9E,0x77,0x66,0x54
                ,0xE9,0x57,0xEB,0x92,0xC0,0xC8,0xCB,0x8F,0x20,0x4E,0x68,0x60,0x62,0x56,0x3F,0xEF,0xE6,0xB7,0x6E,0x4F
                ,0x84,0x6A,0x69,0xFD,0x0C,0x07,0xAA,0x43,0x2A,0xC1,0xBD,0x15,0xCC,0xDE,0x2E,0x16,0xE0,0xF6,0xAB,0x4C
                ,0x17,0x8A,0x8D,0x2B,0x6B,0x7B,0x59,0x02,0x28,0x8B,0x58,0x5A,0x9D,0x93,0xD1,0x63,0xE2,0x70,0x46,0x86
                ,0x81,0x1B,0xEC,0x34,0xB9,0x44,0xF3,0xA9,0xBE,0xDC,0xD0,0x73,0x31,0x7A,0x75,0xA8,0xF8,0x35,0x0D,0x80
                ,0xF7,0xFE,0x41,0xD7,0xA0,0xDF,0xB0,0x9A,0xA7,0x6F,0x22,0x2F,0x9B,0xE7,0xDA,0xA3,0x79,0x23,0xA6,0x5D
                ,0xC4,0x48,0x5C,0x4A,0x32,0xD2,0xEE,0x87,0x40,0xB5,0xD9,0x8C,0x00,0x27,0xB3,0x5B,0xED,0x8E,0xD6,0xAE
                ,0x88,0xFF,0xE5,0xE3,0xBF,0x3E,0x09,0xC6,0x78,0x03,0xB6,0x76,0x21,0x2C,0x24,0xC7,0xFC,0xAF,0x97,0x0F
                ,0x10,0xC3,0x50,0x1D,0x61,0x45,0x9F,0xCF,0xE1,0x90,0x7E,0x7C,0x1E,0x53,0x6C,0x3B,0x96,0x51,0x49,0x42
                ,0x0E,0x55,0x1F,0x83,0xE8,0xAC,0xFB,0x2D,0x04,0xBB,0xF1,0x3D,0x30,0xD8,0xA2,0xB1),
                array(0x92,0x03,0x89,0xC0,0x76,0x59,0x61,0x34,0xA8,0x67,0x90,0x93,0x38,0xCE,0x1D,0xAD,0x82,0x73,0x71,0x7A
                ,0xE9,0x6B,0xB1,0x28,0x94,0x77,0xBA,0x27,0x6C,0x1E,0x20,0x9A,0x72,0x1A,0x18,0xA9,0xA1,0x2F,0x96,0x84
                ,0x2D,0xB3,0x75,0x2A,0xDE,0x64,0xC7,0x45,0x7E,0xDF,0xDA,0x0C,0xA2,0xCC,0xE4,0xB5,0xB6,0x0A,0x87,0x80
                ,0x53,0x54,0xFA,0x0B,0xDB,0x8D,0x99,0x06,0xB2,0xA4,0xAF,0x4A,0xAB,0x12,0xE6,0xC6,0xB8,0x78,0xEC,0xC2
                ,0xC5,0x6E,0xA3,0x7D,0x4F,0x52,0xE1,0x32,0x37,0x8E,0x24,0x17,0x35,0x4E,0xF2,0x51,0x21,0x08,0x65,0x48
                ,0xD7,0xBB,0xB4,0xBF,0x23,0x40,0x5A,0xA6,0xE2,0xEA,0xC1,0x46,0x62,0xD3,0x33,0x6F,0x15,0x14,0xE7,0xB0
                ,0x22,0x7C,0x04,0xFB,0xCA,0x6A,0x88,0x9C,0xB9,0xF3,0x00,0xD2,0x0D,0x4D,0x3A,0x44,0x1C,0xF7,0xAA,0x4B
                ,0xD8,0x10,0x7B,0x9B,0x07,0xE8,0xFC,0x8A,0x41,0x1F,0xBD,0x2E,0xBC,0x9E,0x5F,0xD9,0x70,0x11,0xF6,0xA0
                ,0x5C,0x3B,0xF0,0x25,0x3C,0xFD,0xC3,0x5E,0x83,0x60,0xF1,0x1B,0x19,0xFF,0xDC,0xD4,0xF5,0x4C,0x58,0x47
                ,0x8F,0x57,0x2C,0x63,0xED,0x09,0xAC,0x43,0x97,0xC4,0xC9,0x74,0xD6,0xF9,0x01,0x49,0x02,0x16,0xCF,0x31
                ,0xCB,0x50,0x85,0xE0,0x6D,0x0F,0xE3,0x39,0x3F,0x29,0xDD,0x66,0xBE,0xD0,0x3E,0x79,0xCD,0xF8,0xFE,0x26
                ,0x98,0x9F,0x7F,0x42,0xAE,0xA7,0x2B,0x13,0xA5,0x5D,0x81,0x0E,0x95,0x8B,0x8C,0x86,0x5B,0xC8,0x36,0x9D
                ,0x3D,0xEF,0xEB,0xB7,0x30,0xD5,0xF4,0xEE,0xE5,0x68,0x69,0x55,0x91,0x56,0xD1,0x05),
                array(0xD7,0x8E,0x27,0x78,0x24,0x8C,0xEF,0x45,0x89,0x25,0xA2,0x7E,0x55,0x61,0xB6,0x8F,0xC0,0x49,0x59,0x92
                ,0x08,0x4D,0xA4,0x5E,0x5C,0xDF,0x6F,0x15,0xB8,0xFE,0x62,0x35,0x87,0x8D,0xC3,0xC2,0xC6,0x91,0x7C,0x93
                ,0xF7,0xBE,0xCB,0x28,0x03,0x7D,0xDA,0xB0,0x2F,0xA0,0xF2,0x39,0xB2,0x33,0x44,0xBA,0x9A,0xFD,0x4F,0xAE
                ,0xA8,0x66,0xBF,0x6A,0xD8,0x72,0x54,0x19,0xD4,0xB1,0x0D,0xED,0x63,0x81,0xF9,0x70,0x3F,0x56,0xFF,0xE3
                ,0x77,0x09,0x2B,0x18,0xF6,0xEC,0x32,0x73,0xD5,0x46,0x7B,0xE6,0xA6,0x98,0x99,0x82,0x96,0xEB,0xD3,0xDD
                ,0x3A,0x74,0xCD,0x7F,0xF4,0x84,0x17,0xC8,0x83,0x58,0x6B,0x76,0xBC,0x1C,0xE0,0x1B,0x3B,0x65,0xBD,0x9E
                ,0xAC,0x53,0x2E,0x3C,0xCC,0x2D,0x0A,0x52,0x5A,0xE7,0x95,0x5F,0x9C,0x94,0x4A,0x5D,0x34,0x4C,0x75,0x48
                ,0x50,0xF0,0xC4,0xE2,0xD0,0x11,0x23,0x6C,0x41,0x64,0x2C,0x14,0xB9,0x51,0xA1,0x30,0x38,0x37,0x00,0x21
                ,0x42,0x29,0x9D,0xDC,0xB4,0x36,0x1F,0x6E,0x10,0xFC,0x05,0xCE,0xA3,0xBB,0xD2,0xAF,0x06,0x22,0xF5,0x20
                ,0xB7,0x9F,0xF1,0x1D,0x69,0x79,0x13,0x5B,0xE4,0x16,0xAA,0xE9,0x97,0xA5,0x7A,0xDB,0xB5,0x47,0x68,0x6D
                ,0xC9,0x1A,0x90,0xB3,0x0C,0x02,0xE5,0xC5,0x12,0x0F,0x01,0xE1,0xAD,0x4E,0x3E,0x07,0x31,0xD6,0xFB,0x0B
                ,0x3D,0xC1,0xF3,0x57,0x71,0xA9,0x04,0xD9,0x88,0xCF,0x40,0x43,0xDE,0x80,0x8B,0xE8,0x26,0xCA,0x9B,0x0E
                ,0xF8,0xFA,0xC7,0x1E,0x2A,0x8A,0x85,0x86,0xAB,0xD1,0x60,0xA7,0xEE,0x4B,0x67,0xEA),
                array(0xB0,0xAE,0xDA,0x92,0x6B,0xDD,0x7E,0x39,0xA2,0x08,0xD5,0x8E,0x61,0xD2,0x56,0xAA,0x9C,0x7D,0x0B,0xB8
                ,0xC5,0x33,0x09,0xFE,0x8D,0x26,0x82,0xF4,0x85,0x55,0x44,0x50,0xC4,0xCE,0x74,0x68,0xC2,0x28,0xAF,0x35
                ,0xD4,0xD9,0x42,0x60,0x80,0x3F,0x5F,0x6D,0x57,0x27,0xC6,0xAD,0x11,0x87,0x31,0x58,0xF9,0xE8,0xB7,0x88
                ,0xBE,0x38,0xD6,0xFD,0x6E,0xE6,0x13,0x1D,0xB4,0x2D,0x19,0x99,0x40,0x73,0x3A,0x2A,0xE2,0xFC,0xF3,0xB2
                ,0x51,0x37,0x67,0xA4,0xF7,0x5C,0xC7,0xE4,0x75,0x46,0x93,0xA7,0x25,0xBA,0x9B,0x29,0x21,0xE9,0x30,0xC0
                ,0x20,0x34,0x49,0x9F,0xBD,0xD8,0xCB,0x72,0x59,0xAB,0xBB,0xA8,0x52,0x1E,0x3D,0x62,0xA6,0xBF,0x9A,0xA5
                ,0x2B,0xC8,0x47,0x4C,0xEC,0x23,0x1F,0xB6,0x6C,0x53,0xB1,0x1C,0x7B,0xE3,0xDC,0xDF,0x63,0x70,0x05,0x16
                ,0x91,0x18,0xAC,0x2E,0x65,0x14,0x5B,0x7F,0x64,0x48,0x97,0x10,0xB3,0x01,0xFA,0x9E,0x66,0x6A,0x95,0x22
                ,0xD7,0xC3,0x98,0xD0,0xEA,0x79,0x0D,0xCC,0x2F,0xEF,0x0A,0x8F,0x03,0x3E,0x71,0x4B,0x4D,0x6F,0x1B,0x06
                ,0x69,0xC9,0x4A,0x5D,0x84,0x5A,0xEB,0xF1,0xF5,0xCA,0x8C,0xCD,0xCF,0x17,0xF6,0x78,0x81,0xEE,0x12,0x15
                ,0x7A,0xB9,0xC1,0xE7,0x54,0xF8,0x0E,0x3C,0xA1,0x89,0x8A,0x0C,0xF0,0xBC,0x83,0x00,0xED,0x36,0x2C,0x96
                ,0x5E,0xFF,0x76,0xB5,0xE1,0x4F,0x32,0x02,0xA9,0x3B,0x77,0x04,0xD3,0xE0,0x4E,0x7C,0x43,0x86,0xF2,0xE5
                ,0x9D,0xD1,0xA0,0xA3,0xFB,0xDB,0xDE,0x24,0x90,0x45,0x0F,0x41,0x07,0x8B,0x1A,0x94),
                array(0x42,0x64,0x9C,0x70,0x16,0x63,0x12,0x27,0xF7,0x34,0x1E,0xF2,0x25,0x24,0x29,0x60,0xC6,0x37,0x8A,0xDA
                ,0xDC,0x43,0x5D,0xB1,0x4D,0x54,0xE2,0x4B,0x9A,0x20,0xB7,0x72,0xFB,0xE1,0xA8,0xF8,0xEE,0x45,0xBC,0x66
                ,0x13,0x00,0x7E,0xAF,0x14,0x10,0x07,0x0D,0xC3,0x41,0xE9,0x2B,0x44,0xDE,0x62,0xC9,0x0C,0x7A,0xC7,0xCC
                ,0xAA,0x22,0x5C,0x85,0x88,0x32,0xE8,0xD5,0x81,0x0E,0xB6,0x73,0x3A,0x28,0x9D,0xD6,0x6B,0x01,0xE7,0xAC
                ,0x83,0xC2,0xA7,0x52,0x0B,0x57,0x8B,0xEF,0x7B,0x92,0x2E,0x04,0x9B,0xB0,0xD1,0xA0,0x3B,0x1F,0xED,0x61
                ,0xC4,0x93,0xAE,0x76,0xF3,0xD9,0xF1,0x9F,0xCF,0x2D,0x31,0xB3,0x23,0xDB,0x19,0x03,0x87,0xA6,0x4E,0x95
                ,0xD0,0xD2,0xFD,0x2F,0xAB,0xB8,0x6A,0x79,0xBD,0xA3,0x46,0x98,0x96,0xA5,0xAD,0xC5,0xB2,0xCB,0x89,0x40
                ,0x7D,0x94,0x68,0x55,0x1D,0x8F,0x75,0xFF,0x69,0xCD,0x15,0x4C,0x56,0x99,0x78,0x11,0x86,0xBB,0x18,0x4F
                ,0x8C,0x47,0xE4,0xD7,0x67,0xFE,0x1B,0x97,0x35,0xFC,0xDD,0xBE,0x6E,0xF4,0x08,0x0F,0x9E,0xEA,0x7F,0xE0
                ,0x06,0xB5,0xE5,0x80,0xD3,0x90,0xA4,0x1A,0x3E,0x65,0xDF,0xB4,0xA1,0x3C,0x84,0xA2,0xC1,0xC8,0x48,0x74
                ,0x6D,0x39,0xEC,0x05,0x26,0x51,0x91,0x36,0x5E,0x1C,0x33,0xBF,0x59,0xF5,0x5F,0xCA,0x49,0xF9,0xBA,0xE6
                ,0x21,0x71,0x5B,0x2C,0xA9,0xEB,0x17,0xB9,0x38,0x09,0xCE,0xC0,0x02,0xF0,0x8E,0x3D,0x82,0x6C,0x2A,0xD4
                ,0xE3,0xD8,0x6F,0xF6,0x53,0xFA,0x7C,0x8D,0x30,0x77,0x5A,0x50,0x3F,0x0A,0x58,0x4A),
                array(0x07,0x81,0x90,0xC7,0x51,0x0C,0xBA,0xC5,0x7D,0x2F,0x61,0xB0,0xEF,0x2A,0xEB,0xD0,0xD6,0x99,0x0D,0x5D
                ,0x82,0xE3,0x83,0x2D,0x12,0x71,0x66,0x92,0xE1,0xBB,0x5F,0x7C,0x8A,0x7F,0x9F,0x3B,0x29,0xCA,0x6D,0x08
                ,0x8E,0x0A,0xF8,0xA3,0x16,0x41,0xBD,0x63,0x86,0x28,0xFE,0x32,0xDE,0xCF,0x0F,0xC4,0x59,0xB7,0x2E,0xD1
                ,0x9E,0x62,0x76,0xA6,0x44,0x00,0x88,0x09,0x3C,0xA7,0x77,0x0E,0xD7,0x3D,0xA5,0x21,0xD4,0x19,0xDF,0x22
                ,0x6B,0x8F,0xE0,0x64,0x34,0xB8,0xED,0x30,0x84,0x53,0x1A,0x50,0x14,0xC8,0xD3,0xD2,0xD9,0x4E,0x3F,0xE7
                ,0x60,0xC0,0x8B,0xCB,0xA9,0xC2,0x03,0x57,0xF3,0x5A,0xD5,0x67,0x8D,0x75,0xA8,0xAA,0xF5,0xFA,0x74,0x79
                ,0x1D,0x13,0xE6,0x36,0x6E,0x9A,0x8C,0x98,0x5C,0x17,0x7E,0x02,0x11,0xB5,0xA2,0x2B,0x20,0x70,0xD8,0x31
                ,0xC1,0x38,0x42,0x7A,0xCD,0x1E,0x04,0x4C,0x47,0x78,0x5B,0x35,0xB1,0x2C,0x45,0x48,0xC3,0x40,0x89,0xE9
                ,0xFC,0x72,0x0B,0x01,0x96,0xAC,0x46,0xFF,0x43,0x3E,0x95,0xBF,0xB9,0x24,0xDC,0xE5,0xF4,0x23,0x05,0xB3
                ,0xB2,0xE8,0xBC,0x91,0xCC,0x33,0x4A,0xFB,0x69,0xBE,0xF1,0x49,0x6C,0xF2,0x4B,0x73,0xAB,0x25,0x93,0x1C
                ,0x87,0x54,0x4D,0xF6,0x9D,0xE4,0xAF,0x5E,0x80,0x3A,0xEC,0xE2,0x9C,0x7B,0xA0,0x26,0xAD,0x1B,0x6F,0x85
                ,0x68,0x27,0xA1,0xDB,0x18,0x56,0x94,0x10,0x15,0x58,0x52,0xF7,0x6A,0xAE,0x1F,0xEE,0xCE,0x9B,0x4F,0xEA
                ,0x65,0xFD,0xF9,0xDD,0xB4,0x06,0x55,0x97,0x39,0xA4,0xF0,0xDA,0x37,0xC9,0xC6,0xB6),
                array(0x8D,0xB6,0x6F,0x3E,0x65,0xEC,0xD2,0x68,0xD4,0x0E,0x3A,0x91,0xE9,0x08,0xA3,0x7D,0x4F,0x96,0xD9,0x90
                ,0x6D,0x2E,0xAD,0x3F,0x66,0xDA,0x02,0x53,0x9B,0x7B,0xCB,0x8A,0x37,0x4D,0xF0,0x5B,0x76,0xD7,0x0D,0x55
                ,0x04,0x14,0x5A,0x23,0x81,0xB0,0x2F,0xA7,0x12,0x35,0x85,0xAC,0x69,0x73,0xF2,0x3B,0x22,0xEE,0x40,0x54
                ,0x13,0xB7,0x31,0x61,0x2C,0x9C,0xF1,0x9E,0x7F,0x25,0xD1,0xA1,0xBC,0xF9,0x44,0xA5,0x47,0xB3,0xA0,0x6A
                ,0x60,0xE1,0x2B,0x49,0x88,0x1A,0xFC,0x8B,0xBB,0xF4,0xA6,0xB1,0xEA,0x0F,0xE4,0xD8,0x0A,0xDB,0x1C,0x77
                ,0xC5,0x57,0xDE,0xCA,0xDF,0xCD,0x74,0xD0,0xC8,0xFA,0xBE,0xB8,0x97,0xAF,0xA9,0x38,0xC1,0x19,0x6E,0x1B
                ,0x75,0xFB,0xED,0x03,0xD5,0x1E,0x98,0x01,0x8C,0xF5,0xCE,0xE6,0x45,0x9D,0x48,0x0B,0x15,0x95,0x7A,0x20
                ,0xB9,0x8E,0xC3,0xB2,0xC4,0x87,0x52,0x32,0x9A,0x29,0xF6,0xE5,0xCF,0x5D,0xFD,0x62,0x4B,0xC7,0xF8,0xAA
                ,0xC6,0xE3,0x1F,0x07,0x10,0xC0,0x26,0xCC,0x70,0xB5,0x9F,0x0C,0xDD,0x1D,0x56,0x27,0x71,0x59,0x28,0xA2
                ,0x05,0x51,0xE8,0x09,0x93,0xD3,0xF3,0x42,0xAE,0x17,0x18,0x99,0x8F,0x41,0x5C,0x80,0x78,0xEF,0x46,0x24
                ,0x92,0x63,0x84,0xBD,0xE2,0x94,0x72,0xC2,0x11,0xEB,0x64,0xB4,0x83,0xE7,0x2A,0x39,0x4A,0xBF,0x36,0x34
                ,0x79,0xBA,0x86,0x00,0x5E,0xE0,0xA8,0x33,0x58,0x43,0x7E,0x6B,0xFF,0x89,0x30,0xA4,0xC9,0x16,0xD6,0x21
                ,0x67,0x3C,0xFE,0xF7,0xDC,0x06,0xAB,0x5F,0x6C,0x7C,0x4C,0x4E,0x2D,0x3D,0x50,0x82),
                array(0xF8,0x96,0x6B,0x98,0x9E,0x65,0x39,0xEF,0x85,0x21,0xEC,0x1E,0x86,0x04,0x41,0x57,0x47,0x0B,0x71,0x31
                ,0x7A,0x87,0xC6,0x6D,0x33,0x3D,0x30,0x2B,0x49,0xF9,0xFC,0x29,0x2D,0xAE,0x3F,0x06,0x91,0xB7,0xC1,0xD2
                ,0xFF,0xD1,0xDE,0xCC,0x5A,0x24,0x08,0x2F,0x89,0x8A,0xFA,0xF3,0x6E,0xAA,0x97,0xD6,0xDF,0x8D,0x3C,0x66
                ,0x18,0x6A,0x26,0x80,0x63,0xB0,0xB6,0x7C,0x38,0x8B,0x0F,0x8F,0xF0,0x07,0x62,0x77,0xEA,0x60,0x9F,0x2C
                ,0xF5,0x54,0x82,0xC3,0xA6,0xCD,0xB8,0xF7,0xBA,0x0E,0x70,0x1A,0xF6,0x5D,0xAF,0x1B,0xD5,0xAB,0x01,0xFD
                ,0xBB,0x93,0x45,0xB1,0x2A,0xBD,0x68,0xC9,0x58,0x5C,0x5E,0xA5,0xE8,0x73,0xE6,0x4C,0x64,0xB9,0xC2,0x92
                ,0xF2,0xBC,0xA4,0xB4,0x61,0x72,0xB5,0xB3,0x5B,0x94,0x46,0xED,0x4E,0x0A,0x7D,0x59,0x90,0x43,0x4A,0x42
                ,0x55,0x28,0xA3,0x37,0xBF,0x1D,0xD4,0xCF,0xE5,0xE1,0x52,0x7F,0xDC,0x34,0x8E,0x0D,0xDD,0xC5,0xC7,0xC0
                ,0xD9,0x2E,0x40,0x10,0x09,0xC4,0x05,0x4B,0xC8,0xFB,0x83,0xEE,0xA0,0x78,0x11,0x9A,0xE3,0x79,0x23,0xCA
                ,0x3B,0x9D,0xDB,0x67,0x02,0x19,0x6C,0x9C,0x9B,0x20,0xE0,0x36,0x03,0x15,0xE7,0xEB,0x50,0xF4,0xAD,0xD8
                ,0xAC,0x84,0x1F,0x7E,0x69,0x95,0x0C,0x4F,0xCB,0x53,0x56,0xA1,0x74,0x27,0xD3,0xD0,0x1C,0xA8,0x76,0x16
                ,0x32,0xE2,0xB2,0x3A,0x44,0x51,0xE9,0x00,0x6F,0x48,0xE4,0x99,0xFE,0x22,0xA9,0x14,0x88,0x13,0x81,0xA7
                ,0x17,0x7B,0xCE,0x3E,0x4D,0xBE,0xD7,0x35,0x5F,0x25,0x75,0x12,0x8C,0xDA,0xF1,0xA2),
                array(0x4F,0x2A,0x6A,0x3E,0x5E,0xD3,0x6C,0x11,0xC8,0xD8,0xD6,0xF2,0xE5,0x60,0x7E,0x52,0xA6,0xC3,0xEC,0xD9
                ,0xE8,0xBE,0xAB,0xFD,0x0C,0x77,0x51,0x57,0xE3,0x46,0xFE,0xE6,0x41,0xB1,0x37,0x50,0x25,0x2D,0x02,0xD0
                ,0x81,0x5C,0xCD,0xC5,0xC9,0xEA,0xF8,0xDC,0x48,0x8A,0x8D,0x19,0x2C,0x3A,0x32,0xA8,0x5A,0xE1,0x2E,0x45
                ,0xCE,0x8E,0x64,0x8F,0x2F,0x07,0x10,0xC2,0x08,0x56,0x73,0x20,0x1E,0xA3,0x71,0x68,0xFF,0xD5,0x66,0x70
                ,0xF9,0x00,0x8B,0x14,0x22,0x9E,0x67,0x95,0xB3,0x8C,0x91,0xB0,0xFA,0x26,0xE0,0x05,0x92,0xDA,0x36,0x4A
                ,0x04,0xED,0x4E,0x4B,0xCA,0x98,0xF7,0xA2,0xDE,0x17,0xF3,0x33,0xC1,0x27,0x5F,0x55,0x7A,0xFC,0x34,0x12
                ,0xD1,0x06,0xF4,0x82,0x5B,0xBB,0xE2,0xF1,0xC7,0xE7,0xEB,0x7C,0x2B,0x6B,0x30,0x89,0xA4,0xB9,0x9C,0xA7
                ,0x3C,0xDB,0x1F,0x54,0x79,0xA0,0x15,0xB2,0x23,0xAC,0x59,0x1C,0x4C,0x09,0x6F,0x38,0xC4,0x97,0x0F,0xBD
                ,0x03,0x42,0xEE,0x94,0xA9,0x74,0xC0,0x9F,0x3D,0x4D,0xA1,0x3F,0x7F,0x39,0xF6,0xFB,0xCF,0xEF,0x24,0x62
                ,0x44,0xAD,0x29,0x40,0x28,0x3B,0xB8,0x6E,0xF5,0xF0,0x1B,0x43,0x01,0x53,0x7B,0xAF,0x0E,0x99,0x7D,0xE9
                ,0x18,0xCB,0x49,0x9D,0xE4,0xD2,0xB7,0x63,0x0D,0x96,0x65,0x1A,0x5D,0x87,0x31,0x88,0x83,0x21,0xCC,0x90
                ,0xB5,0xD4,0xDF,0xAA,0x58,0x13,0xDD,0x9B,0x93,0x75,0x0A,0x76,0x16,0x9A,0xC6,0xB4,0x6D,0x85,0x0B,0xBC
                ,0x47,0x72,0x1D,0x61,0xBA,0xB6,0xD7,0x86,0xBF,0x80,0xAE,0xA5,0x84,0x78,0x69,0x35),
                array(0x35,0x85,0xE2,0xF2,0x0C,0x26,0x21,0xBF,0x4C,0x70,0x30,0x9D,0x9B,0xD2,0x79,0x1C,0x84,0x31,0x58,0xFC
                ,0x2F,0xA5,0x10,0xCF,0xE0,0x55,0x6F,0x66,0x88,0xD9,0x99,0xA6,0x76,0x7D,0x1B,0x5C,0x62,0x60,0x2C,0x91
                ,0x01,0x96,0x43,0x64,0x04,0xC4,0x07,0x13,0x80,0xF6,0x12,0xFE,0xB8,0x44,0x4F,0x19,0xD8,0xA1,0x1D,0xF0
                ,0x22,0xA3,0xB9,0xEA,0x75,0xF9,0xCB,0x97,0xB5,0x61,0xEF,0xA7,0x81,0x69,0x33,0xEB,0x17,0xFD,0x32,0x0E
                ,0x29,0xEE,0xF3,0xCE,0x14,0xB6,0xA4,0xC8,0xE5,0x86,0x00,0x59,0x0B,0x48,0xAD,0xC9,0xD7,0x4E,0xFF,0x94
                ,0xBA,0xED,0xE1,0x71,0x8A,0x7F,0x41,0x20,0x8C,0x3F,0xB4,0x51,0x08,0xCA,0xE9,0x34,0x92,0x9E,0x03,0xC1
                ,0x2B,0x6B,0x16,0x18,0x4A,0x45,0xB0,0x56,0xF7,0x74,0x1E,0x25,0xD1,0x5D,0xC5,0x7B,0x27,0x89,0x3B,0x2A
                ,0xAF,0x6E,0xBB,0x23,0xF1,0xB2,0x5F,0x05,0xA9,0x5A,0xAC,0x6D,0x68,0xBC,0xD6,0x3E,0xA0,0x52,0xB1,0xAA
                ,0xC6,0x42,0x4D,0x77,0x57,0xDC,0x37,0x82,0x38,0xD4,0x02,0x47,0x0A,0x1F,0x36,0x7E,0xDF,0x73,0xDA,0xC7
                ,0x24,0x6A,0x9F,0xDE,0x8F,0x50,0xF4,0x8B,0xC0,0x7C,0xEC,0x3D,0x7A,0x9A,0xA8,0x06,0xDB,0x65,0xAB,0x8D
                ,0x46,0x0D,0x3A,0x67,0x93,0x5B,0x78,0x5E,0x2D,0xA2,0x09,0xCC,0xE4,0x72,0x63,0x53,0xC3,0xCD,0xB3,0x8E
                ,0x87,0xE8,0xDD,0xE6,0x83,0x39,0xBD,0x11,0x2E,0x98,0xE7,0x3C,0xD0,0xFB,0x40,0x95,0xFA,0x0F,0x6C,0xAE
                ,0xBE,0xE3,0xF5,0xC2,0x1A,0x90,0xD3,0x28,0x9C,0x15,0x54,0xD5,0xB7,0xF8,0x49,0x4B),
                array(0xF2,0x99,0xBD,0xE4,0xDF,0x60,0xCF,0xF9,0x0E,0x0F,0x27,0x7D,0xFA,0x25,0x4A,0x4D,0x7F,0x44,0x36,0xD6
                ,0x79,0xCB,0x84,0x6D,0x5B,0x34,0x86,0xD1,0xA7,0xA0,0x91,0xBE,0x88,0x19,0x14,0x56,0x6E,0xAE,0x9F,0x3B
                ,0x16,0xE5,0xE7,0xD2,0x18,0x64,0x87,0x7C,0xB3,0x73,0x50,0xDE,0xCA,0x4E,0xED,0x12,0xC4,0x8D,0x1D,0xE3
                ,0xD0,0xC5,0x66,0x9C,0x52,0x8B,0x08,0x8A,0xEC,0xE0,0x24,0xB5,0x3A,0xB2,0x05,0x1B,0x85,0x1E,0x55,0xB4
                ,0x95,0xF3,0xE2,0x2B,0x62,0x9D,0x59,0xCD,0xAF,0x77,0x92,0x15,0x2C,0xFC,0xD7,0x54,0x2E,0x89,0x22,0x74
                ,0x07,0xF6,0x01,0x46,0x4C,0x94,0xD4,0xDD,0xCC,0x40,0x32,0x5E,0x21,0x7A,0x7E,0xBA,0x57,0x67,0xE1,0x1C
                ,0x58,0x68,0x63,0x3D,0x48,0xAD,0xE6,0xBF,0xEB,0x29,0x7B,0xF8,0x43,0x10,0x61,0x8E,0xC0,0xA1,0x2D,0x51
                ,0x49,0x0D,0x9E,0xAB,0x20,0x06,0x0A,0x71,0xE8,0x3C,0x1F,0xA2,0x37,0xF4,0x69,0xEF,0x04,0x13,0x53,0x9A
                ,0x90,0x65,0xC3,0x4F,0x2A,0xC2,0x47,0x96,0xD9,0x0C,0x97,0x31,0x76,0x83,0x5C,0xEE,0xFD,0xAA,0xDA,0x0B
                ,0xFB,0xDC,0xD5,0x2F,0xDB,0xF5,0x17,0x9B,0x00,0xFE,0x75,0xBC,0x6F,0x5F,0x30,0x6C,0x02,0xEA,0xA8,0x8C
                ,0xF1,0xC8,0x98,0x45,0xC7,0x72,0x28,0xB0,0xAC,0x5D,0x41,0x82,0xB6,0xE9,0xC6,0x4B,0x35,0xA9,0x81,0xB9
                ,0x33,0xFF,0xD8,0xF7,0xB1,0xB8,0x3F,0xC9,0x78,0xBB,0x6B,0xC1,0xF0,0x38,0x5A,0x8F,0xB7,0x39,0x11,0x26
                ,0x09,0x1A,0x42,0xA4,0xA6,0xA5,0x23,0xA3,0x80,0xCE,0xD3,0x93,0x3E,0x70,0x03,0x6A),
                array(0x46,0x8A,0xF8,0xB1,0x4B,0x2E,0x3D,0x6B,0x71,0xB3,0xCC,0x79,0x06,0x61,0xCF,0x84,0x72,0xF1,0x33,0x2D
                ,0x25,0xBD,0x4D,0x1A,0x1E,0xC3,0x8C,0x48,0x27,0x63,0xE1,0xB4,0x89,0x24,0x85,0xCE,0xF4,0x3F,0x1B,0xA4
                ,0xE4,0xA1,0x01,0x3B,0xB8,0x57,0xC7,0x44,0x58,0xFB,0xF0,0xE7,0xDE,0x92,0x2A,0xD3,0x96,0x67,0x0B,0xF2
                ,0x86,0xEE,0xF7,0x13,0xC2,0x0C,0xEA,0x0E,0x5A,0x60,0xB7,0x17,0x4E,0xE2,0x14,0x80,0xD4,0x54,0x2C,0xD9
                ,0x1C,0x81,0xD1,0x51,0x36,0x88,0x38,0xC1,0x00,0xAC,0xBC,0xC9,0xE9,0x76,0x8D,0xB9,0xDB,0xCD,0x4A,0x07
                ,0xFE,0x94,0x04,0xAA,0x4C,0x28,0x0D,0xD6,0x21,0x55,0xA2,0x68,0x18,0xE6,0xDC,0x9F,0xCB,0x73,0xD8,0x20
                ,0xB5,0xFC,0x5F,0x12,0x5B,0xAE,0x75,0xAD,0x6A,0x35,0xD2,0x82,0xE3,0x59,0x7B,0xA8,0x43,0x47,0xE8,0xBA
                ,0xAF,0x10,0xD7,0x1F,0x7C,0xA6,0x9B,0xBB,0x32,0xB2,0xDD,0x9A,0x6E,0x3E,0x9D,0x09,0xD5,0x50,0x64,0x0F
                ,0x34,0x97,0x03,0x23,0xFA,0xD0,0x45,0x77,0x30,0xE5,0xDA,0x9C,0xA5,0xA3,0x87,0xB6,0x7F,0xDF,0xAB,0x52
                ,0xA9,0xEB,0x1D,0xF5,0x90,0x5E,0x2F,0x15,0x7E,0x31,0xC4,0x05,0xC0,0x3C,0x02,0x0A,0xE0,0x37,0x26,0xF9
                ,0x40,0x4F,0x8E,0xFF,0x2B,0x6D,0xA7,0x69,0x91,0x5D,0x99,0x6C,0x49,0x8F,0x9E,0x56,0x7D,0xC8,0xEF,0xBF
                ,0x78,0x53,0x29,0xA0,0x83,0x93,0x74,0x19,0xC5,0x39,0xF6,0x62,0x11,0x65,0x66,0x41,0xED,0x3A,0x8B,0xFD
                ,0xEC,0xBE,0x95,0x22,0x08,0xF3,0xC6,0x16,0x5C,0x70,0x6F,0x7A,0x42,0x98,0xB0,0xCA),
                array(0x9B,0x90,0xAE,0x61,0x88,0x94,0xAA,0xDF,0x0F,0x39,0x85,0x56,0x43,0x1E,0xA4,0xEA,0xD6,0x6F,0x25,0xE4
                ,0x8D,0x1F,0x2E,0x5E,0xFD,0x4C,0xFF,0x5F,0x21,0x80,0xDB,0x93,0x20,0x47,0x33,0xA0,0x8B,0xF3,0xB6,0x60
                ,0x23,0x7D,0xC7,0x10,0x6C,0x38,0xC1,0x29,0x13,0x0C,0x58,0xB0,0x17,0xF6,0x82,0x4A,0x16,0xBD,0xF8,0xCB
                ,0xA3,0x63,0xBA,0xC4,0xF0,0xC5,0xB2,0xC6,0x08,0x42,0xAC,0xC2,0xF9,0x41,0xD8,0x2A,0x4E,0x2C,0x86,0x70
                ,0xA5,0x99,0x69,0xFC,0x92,0x46,0x4F,0x14,0xE0,0x64,0xF1,0x3F,0x1C,0x24,0x22,0x9E,0xE6,0x01,0xA2,0xD7
                ,0xFB,0x71,0x97,0x5C,0xE1,0x51,0xB8,0x72,0xB7,0xD0,0x34,0xD4,0xE8,0xAB,0x1A,0x8C,0x54,0x87,0xCA,0x84
                ,0x3A,0x0D,0x37,0xE3,0xE2,0xA7,0x1D,0xD3,0xC3,0x8E,0x78,0x26,0xDE,0x77,0xEE,0x6B,0xCF,0x62,0xA8,0xAF
                ,0x52,0x55,0x49,0xB9,0xAD,0x2F,0x35,0x19,0xE7,0xD9,0xA9,0x3D,0xBF,0x07,0xB5,0x68,0x2B,0xE9,0x74,0x3E
                ,0x89,0xFA,0xEB,0x31,0x50,0x59,0x12,0xA6,0x75,0xBB,0x5A,0xED,0xD1,0xC0,0x79,0x1B,0xCD,0xBC,0x11,0x67
                ,0x2D,0x76,0x9D,0xD5,0xF4,0x05,0x06,0xBE,0x96,0x0B,0xE5,0x73,0x83,0xCC,0x6D,0x6E,0x04,0x7A,0xDD,0xB1
                ,0x27,0x7B,0x36,0xF5,0x7C,0xCE,0x9C,0x30,0xC8,0x09,0x9A,0x65,0xB4,0x02,0x45,0xF7,0x53,0x8A,0x98,0x91
                ,0xEF,0x18,0x44,0x6A,0x66,0xEC,0x5D,0x4B,0x03,0xA1,0x7E,0x8F,0x57,0x81,0xDC,0x00,0xC9,0xF2,0x48,0x3B
                ,0x4D,0x32,0x9F,0xDA,0x40,0x15,0xB3,0x7F,0x0A,0x0E,0x5B,0x3C,0x28,0xD2,0x95,0xFE),
                array(0xBF,0x11,0x73,0x5D,0x5C,0x27,0xB9,0xED,0xC4,0xC2,0x3F,0xD2,0xC7,0xA9,0x7F,0x64,0x0B,0xC8,0xBB,0x91
                ,0x65,0xD6,0x60,0x92,0x82,0x1C,0xFF,0xDB,0x4F,0x9E,0xFE,0x66,0x3B,0xD9,0xF6,0xF1,0xE7,0x56,0x86,0x6F
                ,0x88,0x72,0x98,0x63,0xF2,0x95,0xCC,0x9C,0xAC,0x2E,0x18,0x09,0x67,0x1B,0xE1,0x19,0xBA,0x93,0xD0,0x10
                ,0x28,0xBC,0x85,0x5E,0xC5,0x45,0x1D,0x68,0x2F,0xD3,0x01,0x9F,0xB4,0x42,0x62,0x70,0xB8,0x9B,0xDE,0xA5
                ,0xE4,0xFC,0xFD,0xF8,0x58,0x0A,0xE5,0xB7,0xF0,0x96,0x71,0x33,0xEA,0x0F,0x25,0x43,0xB6,0x47,0x8D,0x61
                ,0x4D,0xD5,0x3D,0x8E,0xB2,0x17,0x23,0x55,0xD4,0x03,0xA4,0x94,0xE3,0x2A,0x8B,0x8F,0xCA,0x22,0xCB,0xB0
                ,0xA2,0x4A,0x37,0xE9,0x5B,0x78,0x30,0x2B,0x9A,0x9D,0x21,0x05,0x2C,0x3E,0xBD,0xA3,0x57,0xB1,0x3C,0x7E
                ,0xEC,0xEE,0xD7,0x6B,0xD1,0x00,0x08,0x4C,0xFA,0x07,0xB3,0xAA,0x7D,0x69,0xA7,0x51,0x77,0x40,0x39,0x7C
                ,0x4E,0xDA,0x12,0x46,0x06,0xDC,0x13,0x31,0x83,0x87,0x38,0x24,0xCF,0xBE,0x81,0x8A,0x54,0xE2,0x1A,0x44
                ,0xDF,0xF3,0x75,0x15,0xC0,0xF9,0x14,0x2D,0x48,0x0E,0x50,0x0D,0x59,0xB5,0xEF,0x97,0x80,0x6E,0x02,0x36
                ,0x29,0xFB,0x35,0xAE,0x53,0xA1,0x49,0xA6,0x1E,0xAD,0xEB,0xD8,0xE8,0xF5,0xA0,0xC6,0x20,0x16,0x04,0x7B
                ,0x90,0xAB,0x1F,0x5F,0xC1,0xF7,0xCD,0x6D,0x84,0xE0,0x34,0x7A,0x52,0x3A,0x0C,0xC3,0xC9,0x8C,0xDD,0x5A
                ,0x4B,0x26,0x6A,0x41,0x99,0xCE,0xF4,0x32,0x74,0xE6,0xA8,0x6C,0xAF,0x76,0x89,0x79),
                array(0x70,0x48,0x45,0x19,0xE8,0xB2,0x1A,0x72,0x9D,0xEB,0x07,0x24,0x5B,0x2C,0x41,0x5A,0xA9,0xD0,0xD5,0xB3
                ,0x27,0x6C,0x5E,0xBB,0x81,0x28,0x03,0xF8,0x92,0xA2,0x18,0x62,0x32,0x63,0x01,0x57,0x3E,0x87,0x90,0x3C
                ,0x11,0x9A,0xDF,0x74,0x35,0x76,0xC9,0x73,0x8A,0x65,0x39,0xCE,0x54,0x82,0xF1,0xD7,0x00,0x20,0xD4,0x29
                ,0xDB,0xD9,0x9E,0xAE,0x06,0x09,0xB4,0xB1,0x69,0x53,0xBE,0x8F,0x7E,0xF9,0x21,0x08,0xA0,0x5D,0xD1,0x22
                ,0x97,0xA8,0x55,0xD3,0x9B,0xF2,0xFB,0x14,0xCD,0x68,0x7A,0x10,0xCB,0x75,0x59,0x34,0xB7,0x91,0xC3,0xE5
                ,0x83,0x0D,0xE7,0xD8,0xDE,0x33,0x67,0x12,0xC1,0x2B,0x8E,0xC4,0x47,0xBF,0xA7,0x49,0x0B,0x61,0x05,0x04
                ,0x3A,0x7B,0x23,0xAD,0x31,0x95,0xF0,0xF4,0x2A,0x0F,0xC6,0xB9,0x8B,0x17,0xEE,0xE2,0x7F,0x44,0xA1,0x50
                ,0xC8,0xEF,0x79,0x2F,0x60,0x16,0x3D,0xEA,0xF3,0x02,0x89,0x86,0xFF,0x4F,0xCF,0x93,0x43,0x8D,0xA4,0x1E
                ,0x7D,0x6F,0x5C,0x99,0x2D,0xF7,0x52,0xB5,0xC7,0xAC,0x1C,0x77,0x66,0xF6,0x30,0xE1,0x51,0x36,0x25,0xEC
                ,0x94,0xBC,0xCC,0xE9,0x13,0x64,0xFA,0x6B,0xE4,0x1D,0x9F,0x88,0x2E,0x85,0x4A,0x1F,0xC2,0x6D,0x78,0x9C
                ,0xE0,0xAF,0x84,0x0E,0x38,0x1B,0x58,0xFD,0x40,0x6E,0x6A,0x46,0xB6,0xFC,0xA5,0x4E,0xBA,0xD6,0x42,0xDD
                ,0xA3,0x71,0xC5,0xFE,0xE3,0x4C,0x15,0x56,0xE6,0xDC,0xA6,0x4D,0xCA,0x4B,0x7C,0xB8,0x3B,0xC0,0x37,0x96
                ,0xAA,0x98,0xED,0xAB,0xDA,0x80,0xB0,0x5F,0x3F,0xF5,0xBD,0x26,0x0C,0xD2,0x8C,0x0A),
                array(0xE5,0x55,0xCA,0x96,0x1A,0xED,0x25,0xEE,0x43,0x26,0xFE,0x09,0x67,0xF1,0x7D,0x44,0x38,0xA8,0x3C,0x8C
                ,0xAA,0xBC,0xDB,0xE4,0x20,0x65,0x19,0xEA,0x5C,0xD7,0xE2,0x2E,0x14,0x5E,0x9A,0x3A,0x05,0x73,0x91,0xE1
                ,0x4D,0x6A,0xEF,0x56,0x84,0x8D,0xD9,0xB8,0xBF,0x95,0xF3,0x80,0xB2,0xF6,0x7C,0x62,0x15,0x16,0x39,0x06
                ,0x9B,0x9F,0x7B,0xB4,0x47,0xAC,0x87,0xEC,0x97,0x49,0x46,0xDD,0x29,0x1F,0x53,0x86,0xFD,0xDE,0x75,0x93
                ,0xCE,0x1E,0xFB,0xFA,0x31,0xC2,0xCD,0x4A,0x35,0xD6,0xCC,0x6C,0x52,0x79,0xC7,0x3E,0x74,0xC8,0xBD,0x92
                ,0x2D,0x02,0x6D,0xB1,0x7A,0x23,0x7F,0x22,0xBE,0x8E,0xF9,0xFF,0x0A,0x00,0xD0,0x63,0x61,0x0F,0x4E,0x76
                ,0xB6,0x4F,0x6B,0x68,0xA2,0x72,0xA5,0xEB,0x90,0x59,0x0D,0xD1,0x42,0xF0,0x98,0xC0,0xE3,0xA3,0xF7,0xF8
                ,0x57,0x40,0x33,0x1D,0xCF,0x08,0xDC,0xCB,0x69,0x27,0x66,0xF2,0x8F,0x83,0xE7,0xC4,0xAB,0x45,0xDA,0xD5
                ,0x77,0x9C,0x5B,0x21,0xA4,0xA7,0x11,0x0E,0x9D,0xE8,0x0B,0x48,0xFC,0x9E,0xC6,0xBA,0xF5,0x24,0x34,0xB0
                ,0x2F,0x3B,0xD2,0x30,0x78,0x85,0x41,0x89,0x12,0x32,0x71,0x5A,0xE6,0x4C,0x01,0x13,0xA9,0x6F,0x70,0xA1
                ,0xC1,0x94,0x99,0xA6,0x88,0xAD,0x1B,0x5F,0x8A,0xBB,0x17,0x50,0x2A,0xD4,0x54,0x10,0x18,0x3F,0x07,0x3D
                ,0x04,0xB5,0x0C,0xDF,0x1C,0xB3,0x81,0xB7,0xD3,0xF4,0x37,0xC3,0x6E,0xA0,0x8B,0xE0,0x4B,0xB9,0x2C,0x60
                ,0x82,0x2B,0x36,0x5D,0xD8,0xAF,0x58,0xE9,0xC9,0x7E,0x64,0x28,0xC5,0x51,0xAE,0x03),
                array(0x24,0xF1,0x21,0x96,0xD2,0xE8,0xAB,0x09,0xCC,0xC1,0x75,0x93,0x3D,0x9B,0x13,0x2C,0x99,0x6E,0xBE,0x5D
                ,0xE0,0xD1,0xED,0xE9,0x27,0xCE,0x58,0x97,0x40,0x9A,0x7C,0xBB,0xA8,0x36,0xA2,0x37,0x43,0x81,0x89,0x56
                ,0x2D,0x04,0x19,0x44,0x62,0x6B,0x9C,0x22,0x55,0xD7,0x35,0xC6,0x70,0x32,0x49,0xD6,0x17,0x30,0xC2,0x8A
                ,0xE1,0xE4,0x92,0xFA,0x83,0x3C,0x3F,0x38,0xD4,0x39,0x03,0x29,0x47,0x4F,0xBC,0xCB,0xCA,0x34,0xD8,0x4B
                ,0xD5,0xA9,0x4E,0xE7,0x33,0xB1,0xB7,0xAD,0x5A,0x0C,0xB5,0xF9,0xA4,0x72,0xDB,0x2A,0xAA,0x41,0xF7,0xA0
                ,0xEE,0x9E,0x50,0x12,0x5B,0x4D,0x7B,0xC9,0x15,0x64,0xA5,0x7A,0x18,0x63,0x1D,0xC5,0x3B,0x77,0x90,0x14
                ,0x91,0xFF,0xC0,0xCD,0x9D,0x57,0x73,0xC4,0x74,0x23,0x7D,0x8C,0xD0,0x3E,0xF5,0xFC,0x61,0x2E,0x20,0x53
                ,0xE5,0x82,0x5C,0x1F,0xEA,0x94,0xEF,0xDE,0xC8,0xAC,0x71,0x0B,0x78,0x4C,0x85,0xEB,0x1E,0xEC,0x6C,0x8E
                ,0x68,0x1C,0xDA,0xFB,0x06,0x84,0xDC,0x3A,0xDD,0xFD,0x65,0x1A,0xBF,0x10,0x42,0x8F,0xCF,0xF3,0xC3,0x66
                ,0x88,0xAE,0x11,0xE6,0xC7,0xF6,0x05,0x2B,0xF2,0xBA,0xE2,0x2F,0x6D,0x48,0x00,0x86,0x52,0x76,0xB3,0xDF
                ,0xA3,0x0D,0x51,0x7F,0xD9,0x67,0x1B,0xB2,0x95,0x0F,0x0E,0x5F,0xB6,0x80,0x6F,0x4A,0x16,0x60,0x0A,0x79
                ,0xA1,0xB8,0xE3,0xB4,0xF8,0xAF,0xD3,0x01,0x25,0x28,0xBD,0x54,0xA7,0x87,0xFE,0x02,0xF4,0xA6,0x5E,0xF0
                ,0x08,0x8D,0x6A,0x98,0x07,0x9F,0x46,0x59,0xB0,0x26,0x8B,0x45,0x31,0x7E,0x69,0xB9),
                array(0x55,0xE6,0xA9,0x0A,0x76,0xAC,0x26,0xA2,0xFA,0xC4,0x6D,0x73,0x9A,0x7B,0xF4,0x93,0xCF,0xE9,0xC0,0x12
                ,0xB6,0x3D,0x96,0x51,0xEB,0x5A,0xAA,0x2A,0x75,0x33,0xE5,0xA3,0xC9,0x8A,0xE2,0x6B,0x9C,0xC5,0x23,0x6F
                ,0x78,0x06,0x98,0x24,0xB2,0xA0,0xC3,0x85,0xB0,0x3C,0xCE,0xDD,0x9F,0x1B,0x32,0x1C,0x4A,0xBC,0x5D,0x80
                ,0x19,0x99,0xEC,0xD1,0x6C,0x17,0x88,0xA5,0xD6,0xD9,0x5B,0xD2,0xB3,0x0B,0x36,0x2C,0x70,0xF7,0xB7,0x94
                ,0x44,0x42,0x47,0x46,0x14,0x72,0x89,0x64,0x5F,0x15,0x41,0x0E,0x86,0x6A,0x3E,0x37,0x63,0xD4,0xF6,0xBB
                ,0xE1,0xBE,0xF1,0x25,0x11,0x67,0x28,0x8C,0x68,0xF8,0x97,0x61,0x4F,0xB1,0xAE,0x6E,0x38,0x77,0x13,0x90
                ,0xD0,0x4D,0x74,0x09,0x03,0x43,0x48,0xE7,0xB8,0xB5,0xA1,0xFC,0x2B,0x4B,0xCB,0xBF,0xEA,0xD8,0xAF,0x0C
                ,0x20,0x57,0x56,0x40,0x62,0x3B,0xBA,0xF2,0x87,0xCC,0x1F,0x8E,0xC2,0xE4,0x54,0xC1,0x9D,0x35,0xC6,0xFE
                ,0x27,0xD7,0x30,0x7F,0x65,0x5C,0xED,0xDC,0x60,0x8B,0x52,0x58,0xE3,0xF3,0xDA,0x18,0x3A,0xD5,0xE0,0x4E
                ,0x08,0xC7,0xC8,0x81,0xF0,0xAD,0x9E,0x8D,0x7E,0xFB,0x45,0x34,0xCD,0x50,0x49,0x2D,0x1A,0xDF,0x10,0x79
                ,0x31,0xA7,0x0D,0x2E,0xF9,0xE8,0xBD,0xEF,0x7D,0x53,0x3F,0x8F,0x7C,0x00,0x59,0x04,0x22,0x66,0x83,0x29
                ,0x39,0xF5,0xB4,0xFF,0x4C,0xFD,0x69,0xDB,0x16,0x7A,0x02,0x07,0xA6,0x05,0xCA,0x91,0xAB,0xEE,0x84,0x95
                ,0x1E,0x71,0xA4,0x01,0xB9,0x1D,0x0F,0x9B,0x5E,0x21,0xD3,0x2F,0xDE,0x82,0x92,0xA8),
                array(0x5B,0x15,0x12,0x0C,0xF4,0x18,0xD6,0x4B,0x2A,0xA7,0xCF,0x17,0x4F,0x30,0xC4,0x37,0xE6,0xCA,0x55,0xAF
                ,0x76,0xC7,0x6D,0x5A,0x7B,0x96,0x52,0xC1,0x2B,0x61,0xD4,0xF2,0x89,0xF1,0x8A,0xB4,0x47,0xD2,0x38,0xA4
                ,0x8C,0x9F,0x14,0x0E,0x65,0xE1,0xEF,0xE4,0x00,0x35,0x83,0x22,0xE8,0x63,0xC8,0x40,0x97,0x26,0x1D,0x07
                ,0xDC,0xCD,0xB2,0x93,0x5D,0x59,0x3E,0x85,0x1B,0xAE,0x28,0x4A,0xE5,0x7D,0xF9,0xC2,0x5C,0x6F,0x60,0x24
                ,0x64,0x49,0x9B,0xD0,0x3D,0x50,0x73,0x53,0x1A,0x29,0x43,0xB6,0xBA,0x67,0x56,0x5F,0x84,0x16,0x74,0xD3
                ,0xA0,0xA3,0x3C,0x81,0xE7,0x42,0x2D,0x7C,0x13,0xAD,0xDF,0x68,0x88,0xD1,0xD8,0x57,0xEB,0xB8,0x09,0xF8
                ,0xE2,0x72,0x6A,0x45,0x4E,0xD7,0xCC,0x1C,0xC6,0xB5,0x39,0x2E,0xAC,0x58,0x1F,0xBC,0x0B,0x8B,0x9A,0xBB
                ,0x06,0x78,0x02,0x44,0xFB,0x41,0x80,0x54,0x05,0xA5,0x0D,0xFC,0x11,0x99,0xA9,0xBF,0xFA,0xC5,0xB9,0xAB
                ,0x7A,0xF3,0xB3,0x23,0x2F,0xE9,0xB0,0xDB,0x62,0xCE,0x3F,0x4C,0x98,0x27,0x3A,0xA8,0xDD,0xAA,0xA6,0x7E
                ,0x77,0x36,0xF0,0xA2,0x0A,0x91,0x87,0xF7,0x32,0x9E,0x69,0xFD,0x70,0xC9,0xE3,0x71,0x75,0xA1,0xC0,0xEE
                ,0x94,0x5E,0xED,0x20,0xF6,0x7F,0x6B,0xBD,0x82,0x3B,0xD5,0x25,0xCB,0x19,0xEA,0x79,0xFE,0x04,0x95,0xFF
                ,0x8F,0x4D,0x86,0x10,0x92,0xC3,0x33,0xB7,0xE0,0x9C,0x48,0xB1,0x2C,0x46,0x8D,0x0F,0xBE,0x03,0xDE,0x6E
                ,0x51,0xEC,0xF5,0x1E,0xD9,0x90,0x8E,0x08,0x31,0x01,0xDA,0x66,0x21,0x9D,0x34,0x6C),
                array(0xAF,0xBD,0xAC,0xD2,0x75,0x2A,0x41,0xCE,0x45,0x62,0xFE,0x21,0x90,0x65,0x39,0xAE,0x9D,0x4A,0x3A,0xCC
                ,0xD6,0xA8,0xA3,0x07,0xC9,0xC0,0x5C,0x9B,0x71,0xCD,0x06,0x78,0xE6,0xC8,0xF5,0xD8,0x6D,0xDC,0xE2,0x27
                ,0xBA,0xDF,0x2D,0x5E,0xE8,0xFD,0x5A,0xA7,0x09,0x47,0x33,0xA6,0x70,0xAA,0xE1,0xC1,0x8D,0xC6,0x29,0xB6
                ,0x59,0x69,0xB7,0xB9,0x83,0x60,0xA4,0x7E,0x51,0x6B,0x94,0x05,0x7D,0xA1,0x0F,0x57,0x30,0xF3,0x9F,0x08
                ,0xDE,0x5F,0x46,0x40,0xCF,0x93,0x7A,0xFA,0x66,0x22,0xBB,0x26,0xF4,0xB4,0x63,0x1F,0x15,0xB2,0x9A,0x84
                ,0xD1,0x88,0xDA,0xED,0xD3,0x7B,0x20,0x49,0x74,0xD4,0x8C,0x0E,0x8A,0x0C,0x35,0xB0,0xF2,0x23,0x31,0x11
                ,0x14,0xEB,0x16,0x44,0x92,0x55,0xC4,0xE9,0xB5,0x25,0xEE,0x04,0xBC,0xA0,0xC2,0x86,0xBE,0x76,0xB1,0xCA
                ,0x56,0xE0,0x9C,0x10,0xC7,0x2F,0x0A,0xD5,0x82,0x89,0x3D,0x19,0x17,0x1E,0x68,0x97,0x61,0x28,0x43,0xC3
                ,0x67,0x3E,0x2E,0x58,0x98,0xB8,0xE3,0x77,0x6A,0x95,0xF9,0x3B,0xF0,0x3F,0xFC,0xBF,0x96,0x81,0x3C,0x0D
                ,0xC5,0x52,0x18,0x5D,0x32,0xF1,0x9E,0x8E,0xFF,0x64,0x03,0x50,0xEF,0x01,0x24,0x53,0x37,0xF6,0xA9,0x85
                ,0x42,0xFB,0x38,0x1B,0xD0,0x79,0x4D,0x36,0x02,0x87,0x7C,0x99,0x4F,0x1A,0xAD,0x72,0xE7,0x1D,0x6F,0xF7
                ,0x4C,0x34,0xA2,0xD9,0x7F,0xDD,0x8B,0xEA,0x0B,0x6E,0x91,0x6C,0x00,0xAB,0xA5,0x80,0x48,0xE4,0xEC,0x2B
                ,0x12,0xE5,0x5B,0xF8,0xDB,0x1C,0x4E,0x13,0x73,0x2C,0xB3,0x54,0x4B,0xD7,0x8F,0xCB),
                array(0x9D,0xC3,0xCC,0x3C,0x40,0x75,0x97,0x82,0x9A,0x8B,0x6C,0xB9,0xB0,0xB5,0x3B,0x5A,0xF1,0xDC,0x90,0x34
                ,0x9F,0x6B,0x73,0x4D,0x3E,0x0C,0x95,0x5E,0xFC,0x02,0xEF,0xF9,0x91,0x2E,0xC2,0xAC,0x88,0xCE,0x45,0x2D
                ,0x25,0xE8,0xFF,0x1C,0xED,0xD6,0xCB,0x6D,0xEB,0x7B,0x39,0xE0,0xB7,0x99,0x68,0x8D,0xA9,0x74,0x55,0x4C
                ,0x48,0xAD,0x66,0x21,0x5B,0x6A,0x5F,0xC4,0x77,0xBC,0x06,0x0E,0x84,0x98,0x79,0xD7,0x50,0x71,0xD5,0xE4
                ,0xF5,0x4B,0x46,0x42,0x9B,0x6F,0x94,0x5D,0xE7,0xE6,0x7D,0x8F,0x0A,0x8E,0xF3,0x11,0xBB,0x65,0x27,0x80
                ,0x36,0xA5,0x52,0x93,0x10,0x51,0xF4,0xEE,0x9C,0x37,0xE1,0xD4,0xC1,0xD3,0x85,0x16,0xCA,0x1A,0xF6,0xC0
                ,0xA8,0x0F,0x38,0xB3,0x7E,0x01,0x54,0x83,0x1D,0x4A,0xF7,0x49,0x62,0xAA,0x1F,0xCD,0x86,0x13,0x35,0xFD
                ,0xFA,0xC5,0x12,0x20,0x19,0xE9,0xFB,0xD8,0x03,0x6E,0x24,0xF8,0xEC,0x4F,0x53,0xD9,0x43,0x44,0x33,0x56
                ,0xAB,0x1B,0xD0,0x17,0xC7,0x00,0x69,0xF0,0xC9,0xDF,0xE2,0x7A,0x72,0xBE,0x22,0x70,0xA3,0x78,0x5C,0x81
                ,0xDB,0xAF,0xB8,0xE5,0xEA,0x0D,0x08,0xCF,0x07,0x4E,0xF2,0x18,0x92,0xC6,0x41,0x47,0x60,0x67,0x14,0xA1
                ,0xDD,0x2F,0x9E,0xBD,0xDE,0x15,0x61,0x26,0xC8,0xFE,0x0B,0x2B,0x76,0xA4,0x89,0xB2,0x64,0x58,0x31,0xA6
                ,0xB6,0xBA,0x59,0x32,0xA7,0x96,0xAE,0x23,0x2A,0x3A,0x57,0x09,0x05,0xE3,0x1E,0xB1,0xB4,0x3F,0x29,0xD2
                ,0xDA,0xBF,0x87,0xD1,0x3D,0x2C,0x04,0xA0,0x7C,0x30,0x63,0x7F,0xA2,0x28,0x8A,0x8C),
                array(0xC2,0xFE,0x1F,0x8C,0x58,0x6E,0x00,0xDF,0x8A,0x93,0x85,0x37,0x4F,0x10,0x17,0x78,0xFF,0x7E,0x61,0xED
                ,0x75,0x34,0x1A,0x8B,0x5A,0x2C,0x39,0x9F,0x0D,0xFB,0x7C,0x5C,0xE2,0x91,0x1E,0x33,0xF2,0x19,0xAE,0x65
                ,0x02,0xA9,0xB3,0xBC,0x3A,0xEC,0x4A,0x0C,0xEB,0xDA,0xA3,0xF8,0x8D,0x7A,0x0A,0x36,0x5D,0xAA,0xF9,0x97
                ,0x12,0x28,0x54,0x3E,0x6F,0xD0,0x67,0xF7,0xAD,0x16,0x6A,0x70,0x20,0x06,0x76,0x0E,0x21,0x35,0x55,0xCF
                ,0xE4,0x26,0x24,0x1C,0xDE,0xC6,0x60,0x42,0x7D,0xD5,0x7F,0xB5,0xC0,0xC4,0xB9,0x2D,0x03,0x50,0xEA,0xE3
                ,0x79,0x99,0x3C,0xD3,0xD8,0xA6,0x04,0x80,0x83,0x14,0x1B,0xBF,0xC1,0x30,0x38,0x46,0x43,0x63,0xBA,0xF1
                ,0xBB,0x31,0xE8,0xF4,0xDD,0x7B,0x8E,0xD4,0xEF,0x53,0x4D,0xBD,0x56,0x4B,0x6B,0xB8,0x9E,0x89,0x96,0x0F
                ,0xC8,0x1D,0x57,0x69,0x68,0xB4,0x9D,0xE5,0xD7,0xA0,0x0B,0xE0,0x51,0xA1,0xFC,0xB2,0xA8,0x9A,0x87,0x01
                ,0x77,0x3B,0x18,0x08,0xA5,0x29,0x98,0x9C,0xC7,0x90,0xC5,0xAB,0xB7,0x47,0xE9,0xCA,0x59,0xA7,0xF5,0x07
                ,0x11,0x88,0xA4,0x49,0x27,0xDB,0x22,0x41,0xAC,0xC3,0x05,0xCE,0xBE,0x45,0x6D,0xCB,0x2E,0x09,0x74,0xD9
                ,0x3F,0xEE,0x4C,0xE1,0x95,0x23,0x64,0xB0,0xE7,0x9B,0xE6,0x52,0x2F,0x71,0xF6,0x6C,0xCD,0xF0,0xC9,0xD2
                ,0xD1,0xDC,0x13,0x72,0x81,0x92,0x4E,0xB1,0x66,0x94,0x40,0xB6,0x5E,0x32,0xA2,0xF3,0x86,0xFA,0x2B,0x73
                ,0xFD,0xAF,0xCC,0x8F,0x3D,0x62,0x15,0x5B,0x82,0xD6,0x84,0x5F,0x25,0x48,0x44,0x2A),
                array(0x91,0x8C,0x18,0x67,0x0E,0x78,0x88,0xC0,0xB9,0x85,0x63,0x3C,0xB4,0x2C,0xAE,0x95,0x75,0x1E,0x58,0xCC
                ,0xE0,0xD2,0x5B,0x5D,0x7A,0xE4,0xA6,0x70,0x26,0xAA,0xC3,0x0C,0x01,0xCF,0xAD,0xAC,0x50,0x24,0xE3,0xC7
                ,0x4E,0x05,0x4B,0x41,0x2B,0x9A,0xA7,0x1A,0xDE,0x22,0x49,0xD0,0x20,0x5E,0xB8,0x1F,0xF4,0xC9,0x8B,0x92
                ,0x56,0xC2,0x2F,0xBA,0x55,0x32,0xFC,0xD1,0x00,0xBF,0x25,0x8E,0x43,0xED,0x1B,0xB2,0xFD,0x8D,0x5A,0x10
                ,0x7C,0xEC,0x94,0xA0,0x90,0x0F,0x3A,0xE8,0x66,0x98,0x17,0x7D,0x80,0xA2,0xA1,0x08,0x13,0x9C,0xD7,0x2A
                ,0xDA,0xCE,0x82,0x44,0xC6,0x97,0xE9,0x09,0xE1,0x0B,0x65,0xEB,0x42,0xEA,0x4C,0x8F,0xF9,0xA3,0x93,0xFE
                ,0xFA,0xC4,0x7E,0x61,0x81,0xB6,0x6E,0x86,0x40,0x6C,0x29,0x30,0xCB,0x6F,0x52,0x31,0xD6,0x83,0x89,0x15
                ,0x14,0x69,0xD3,0x7B,0x77,0x79,0x38,0x0A,0xF5,0x3E,0x6D,0x87,0xF3,0x96,0x46,0xF7,0x8A,0x2E,0xB1,0x4A
                ,0x27,0xE5,0xE7,0x16,0xBE,0x5F,0xCD,0x3D,0x76,0x3F,0xD8,0xBD,0x4F,0xE6,0x73,0x74,0xC1,0x39,0x02,0xF1
                ,0x53,0x4D,0x84,0x54,0x6A,0x34,0x23,0x03,0x28,0xE2,0xCA,0x71,0x07,0x59,0xA8,0x60,0x0D,0x9F,0x9D,0x35
                ,0xB3,0x45,0xDC,0xC8,0x7F,0xEF,0xBB,0x19,0x11,0x51,0x36,0x6B,0xDF,0xC5,0xA9,0x33,0xAF,0xF6,0x99,0xBC
                ,0x3B,0x2D,0x48,0xD4,0x1C,0x47,0xA5,0xA4,0x5C,0xFB,0xDB,0x9E,0x72,0xEE,0xAB,0x04,0x21,0x62,0x1D,0xF8
                ,0x57,0xFF,0x64,0xB0,0x06,0xDD,0xF2,0xD9,0x12,0xD5,0xB5,0xF0,0x68,0xB7,0x37,0x9B),
                array(0x9D,0x89,0xCD,0xEF,0x71,0xE3,0x42,0x01,0xFD,0xB1,0xC9,0x48,0x43,0x94,0x30,0xF7,0x52,0xB6,0x1A,0xDD
                ,0x1F,0x2F,0xAD,0x17,0x31,0xB5,0x3A,0x6B,0x63,0x0E,0x33,0x58,0x1C,0xB7,0x0D,0x50,0x92,0xA3,0xA9,0x10
                ,0x82,0x90,0x4B,0xD2,0x09,0xDE,0xF6,0x28,0xCC,0xDF,0x36,0x27,0xA8,0xE4,0x0C,0xE0,0x20,0x56,0xDA,0x0F
                ,0x61,0x6F,0xFC,0x35,0xCA,0x2C,0x2A,0x85,0x45,0x1D,0x24,0x67,0xF9,0xBE,0x00,0x15,0xF3,0x47,0xD5,0xBD
                ,0xE2,0xD9,0x18,0x79,0x46,0xD3,0xF4,0xEC,0xA1,0xFB,0x4C,0xFA,0xD1,0x66,0x40,0xB4,0x21,0xE6,0xB8,0x39
                ,0x70,0xE7,0x78,0xC3,0xC1,0x41,0xD0,0xC2,0x8F,0x9F,0xED,0x91,0x7B,0xFF,0x83,0x12,0x19,0x5A,0x14,0x32
                ,0x4A,0xF1,0x88,0xA4,0xE8,0x07,0xBA,0x86,0x60,0x75,0xAF,0xC8,0xF2,0x8B,0xB2,0x73,0x3F,0x8D,0x1E,0xAE
                ,0x80,0x8E,0xCB,0x57,0x5C,0xC0,0x6C,0xBF,0x65,0xA5,0x2D,0x5B,0x11,0xF5,0xEE,0x7E,0x22,0x5F,0xA0,0x03
                ,0x9B,0xC4,0x38,0xD8,0xA6,0xC7,0xD4,0x5D,0x9C,0x08,0xE5,0x55,0x62,0xA7,0x97,0x25,0x72,0x8C,0x3D,0xF0
                ,0x13,0xDC,0x6A,0x99,0x2E,0x76,0x8A,0xBC,0x59,0x3C,0x1B,0xCF,0xDB,0x51,0x7F,0xE1,0x26,0x7A,0xBB,0x3E
                ,0x04,0x93,0xEA,0x4D,0x74,0x05,0x7C,0xB0,0xD6,0xEB,0xD7,0x16,0xC6,0x69,0xAA,0x64,0x37,0x6D,0xAB,0x9A
                ,0xCE,0x0A,0xFE,0x54,0x3B,0xB3,0x95,0x7D,0xAC,0x77,0x98,0x81,0x96,0x02,0x68,0x53,0x2B,0x44,0x84,0x87
                ,0x4E,0x0B,0x9E,0xF8,0x4F,0x49,0x5E,0xE9,0x34,0xB9,0x06,0xC5,0xA2,0x6E,0x29,0x23),
                array(0xAF,0x3B,0xFE,0x09,0x4C,0x46,0xFF,0x81,0xCA,0xA7,0x54,0x42,0xC9,0x23,0x87,0x10,0xBC,0xDB,0x0E,0x2E
                ,0x6B,0xEF,0xF0,0x83,0x58,0x40,0x79,0xD3,0xB5,0xDA,0x71,0x32,0x1E,0xFB,0x6F,0xE4,0xA9,0xC3,0x7E,0xA3
                ,0xA5,0x13,0x77,0xD9,0xE7,0xB6,0x07,0x0D,0x0A,0xBD,0x41,0x95,0x82,0x7B,0xE0,0x6C,0xE6,0x17,0x35,0xFC
                ,0x94,0x8B,0x59,0x93,0xB9,0x7F,0xA4,0x43,0x70,0x05,0xC2,0x89,0x03,0x1B,0xC4,0x86,0x6D,0xEE,0x16,0x7C
                ,0x8E,0xFD,0x00,0x21,0x29,0xAA,0x12,0x0F,0xE1,0xD4,0x2B,0x01,0x5D,0x50,0x2C,0xAE,0xBF,0xB4,0xF3,0xCC
                ,0x9A,0xDE,0xC8,0x04,0x26,0x9E,0x91,0x74,0xB8,0x4D,0xF8,0x61,0x20,0x28,0x97,0x45,0xEA,0x14,0x33,0x90
                ,0x5C,0x6E,0x67,0xCB,0xF7,0x34,0xE8,0x30,0x25,0xED,0x24,0x55,0xCF,0x66,0xA1,0x15,0xBA,0x3C,0x4A,0xC5
                ,0xC0,0xD6,0x1C,0x9D,0x48,0x1F,0xA8,0x51,0x11,0xCD,0x4B,0xE5,0xA2,0xFA,0x39,0xBB,0x2F,0x38,0x5B,0xC1
                ,0x9B,0xE2,0x56,0x06,0xF4,0xD2,0x47,0x8D,0x3D,0x3F,0x9F,0xB3,0xAB,0xAC,0x75,0x19,0x1A,0xDF,0x02,0x98
                ,0x8A,0xF9,0x44,0xC7,0xB2,0x99,0x6A,0x92,0xBE,0xF5,0x8F,0xE9,0xB0,0x78,0x37,0x73,0x65,0x3A,0x9C,0x18
                ,0xB1,0x08,0x8C,0x72,0xF6,0x80,0x69,0xD8,0x7A,0x4E,0xD7,0xC6,0x1D,0xD1,0x63,0x53,0x68,0x5F,0xEC,0xEB
                ,0xAD,0x85,0x2D,0x64,0x0C,0xB7,0x4F,0x49,0x0B,0xF1,0x7D,0x31,0x96,0x57,0x36,0xA6,0x84,0x62,0x88,0x27
                ,0xD0,0xDC,0xA0,0xDD,0x3E,0xD5,0x76,0x22,0xE3,0xF2,0xCE,0x5E,0x5A,0x52,0x60,0x2A),
                array(0x53,0x4A,0x76,0xC7,0x86,0x4E,0xDE,0x10,0x8B,0xF9,0x20,0x44,0x46,0x9E,0x84,0x75,0xF0,0x9D,0xFE,0xC0
                ,0x7B,0xD8,0x54,0xB2,0x0B,0xA0,0x0C,0x7D,0x94,0x5C,0x50,0xC4,0x23,0xA9,0xB4,0x60,0x6B,0x7F,0x65,0xAC
                ,0x4F,0xE3,0x85,0xBA,0x68,0xEE,0x8A,0xF1,0x69,0xB8,0x36,0x16,0x33,0xD1,0xC9,0x29,0x64,0x74,0x89,0x98
                ,0x37,0x87,0xE9,0xBD,0x97,0x08,0x3C,0x2C,0xCA,0x77,0xD4,0x3F,0xEF,0x56,0xF7,0x6A,0xCD,0x70,0x3E,0x7A
                ,0x5E,0x81,0x2E,0x73,0xFD,0x17,0x35,0x47,0x12,0xA2,0xD5,0xCB,0x7C,0x01,0x49,0xEB,0x9F,0x8F,0xB1,0x5D
                ,0x8D,0x0F,0x9B,0x26,0x6F,0xFA,0xA3,0x06,0xDF,0x42,0x19,0x25,0xB0,0xD0,0xBB,0x5F,0x93,0x78,0x22,0xB6
                ,0x61,0xC8,0x80,0x3A,0x41,0x6D,0x45,0x07,0x2D,0x3D,0xE5,0x21,0xCF,0xF8,0xE8,0x51,0x2B,0x59,0xB7,0x2A
                ,0x58,0xD2,0xAB,0xED,0x92,0x63,0x9A,0x8C,0x5B,0x2F,0x4B,0xB5,0x83,0xC5,0xFF,0xE6,0x79,0x31,0xDC,0x7E
                ,0x04,0x14,0xEA,0x40,0x6C,0x38,0x57,0x6E,0x03,0xE7,0x5A,0xC6,0xFC,0x43,0x88,0xBF,0x11,0x27,0x39,0x0E
                ,0xC3,0x00,0x8E,0xB9,0xD3,0x82,0xA4,0x0D,0x72,0xFB,0xAF,0x30,0x1E,0x34,0xF6,0x1A,0x62,0xF4,0xDD,0x66
                ,0xAD,0xE2,0x0A,0x24,0x13,0xDA,0x91,0xA7,0x95,0xE0,0x09,0xB3,0x1D,0xBE,0xD7,0xF2,0x32,0x02,0xAA,0xA1
                ,0xAE,0x1C,0xD6,0xEC,0x9C,0xE4,0xDB,0x96,0x28,0x1F,0xC2,0xD9,0x99,0xA5,0x3B,0x90,0xBC,0x15,0x1B,0xA8
                ,0x05,0xC1,0x4C,0xCC,0xCE,0x52,0x55,0xF5,0x67,0x4D,0xE1,0x71,0xA6,0x48,0xF3,0x18),
                array(0x18,0x68,0x31,0xC2,0x05,0x2C,0xCD,0x40,0x00,0x64,0x3A,0x4D,0x70,0xCE,0x6B,0x8B,0x57,0xF8,0x4F,0xF1
                ,0xBC,0xD0,0xD5,0x82,0x30,0xD2,0x2F,0x0F,0x8A,0x90,0xBD,0x19,0xD7,0xC1,0x5E,0x6A,0x1E,0xF6,0x0E,0xA0
                ,0xA1,0x26,0x52,0xF3,0x9A,0xA7,0xA8,0xF0,0xDF,0xCF,0xBB,0x79,0x58,0xB9,0x8C,0x69,0x22,0xC0,0x54,0x9B
                ,0xE7,0x4E,0xB7,0x99,0xEC,0x53,0x0A,0x13,0x25,0x37,0x0D,0xDD,0x76,0x17,0xF4,0xAF,0xC6,0x0B,0x6F,0xD6
                ,0xB3,0x11,0x8F,0x0C,0x98,0x72,0xAD,0x04,0xC8,0xFA,0xB8,0x33,0xC4,0xAC,0xB5,0xA6,0x88,0xB6,0xFE,0x56
                ,0x84,0xA2,0x63,0x03,0x3C,0x61,0xCA,0x3E,0xBF,0x36,0xEE,0xF5,0xDC,0xF9,0x39,0x62,0x1C,0xB0,0x34,0xA4
                ,0x9D,0xE3,0x8E,0x08,0x49,0x1D,0xA3,0xE2,0x48,0x21,0x01,0xD1,0x7A,0x75,0x27,0xFF,0xE8,0x67,0x86,0x81
                ,0x78,0x51,0x29,0x5D,0x44,0xA5,0x15,0x1F,0x96,0xFC,0xC3,0x92,0x2E,0xC5,0x2D,0x4B,0xC9,0x5C,0x65,0xF7
                ,0x4A,0xAB,0x45,0xB1,0x8D,0xBE,0x97,0xFD,0xEA,0x2A,0x47,0xA9,0xD4,0x41,0x9F,0xD9,0x09,0xB4,0xEB,0x5A
                ,0x60,0x1B,0x95,0x3B,0x5B,0x71,0x2B,0x9C,0xE9,0xAA,0x7C,0xCB,0xED,0x7F,0xC7,0x89,0x7D,0xAE,0xCC,0xE6
                ,0x42,0x20,0x6D,0x59,0x3F,0xD8,0x23,0x93,0xFB,0x3D,0x94,0xE1,0x77,0x6E,0xEF,0xF2,0x7B,0x80,0x06,0xDE
                ,0x16,0x87,0x38,0x43,0x7E,0xE0,0xD3,0x55,0x10,0x12,0x35,0xE5,0x24,0x66,0x91,0x4C,0x28,0xDA,0x50,0xB2
                ,0x07,0x9E,0x74,0x5F,0x6C,0x1A,0xBA,0x02,0x14,0x85,0x32,0x83,0x46,0x73,0xDB,0xE4),
                array(0xCD,0xF2,0x3C,0xFA,0xDF,0x2D,0x72,0x3D,0xA6,0xE4,0x80,0x46,0x74,0x4F,0x00,0xA0,0x58,0xC1,0x65,0xF9
                ,0xCE,0xD8,0xAE,0x5D,0x9A,0xFB,0x7D,0x1A,0xF4,0xF5,0x67,0x51,0xC3,0xC6,0x05,0x4A,0xEC,0x50,0x7F,0x93
                ,0xAA,0xB6,0x23,0x6E,0xEA,0x31,0x35,0x71,0xC0,0x4C,0x40,0x17,0x22,0x55,0xCC,0x7A,0x28,0x12,0x54,0x69
                ,0x34,0x5C,0x9D,0x76,0x33,0x3B,0x9F,0xE2,0x06,0xE8,0xFC,0x73,0x5A,0x18,0x8F,0xE3,0xE7,0x29,0x92,0xA7
                ,0x90,0x57,0xBA,0xE6,0xC9,0xCF,0x84,0x26,0xB2,0xB0,0x86,0x5B,0xF3,0x61,0x8C,0x66,0x52,0xC7,0x6A,0xAF
                ,0x78,0xDE,0x53,0x3F,0x47,0xDB,0xFF,0x5F,0x56,0x1D,0x63,0x95,0xC8,0xB4,0xDA,0xAB,0x09,0xCA,0xA9,0x41
                ,0x36,0x59,0x25,0xD3,0x24,0x8B,0x4E,0x1B,0x7B,0x16,0x2F,0x60,0xA5,0xED,0xEF,0x10,0x97,0xB5,0xAD,0x37
                ,0x9B,0xD5,0xA8,0x9E,0x0F,0x6C,0x08,0xC4,0x0B,0xCB,0x2A,0xD6,0x4D,0x14,0xD4,0xC5,0x32,0x8A,0x75,0x13
                ,0xB3,0x6D,0x27,0xB7,0x96,0x3E,0x1E,0x38,0x98,0x43,0x1F,0x77,0xE9,0x42,0xFD,0x44,0xF8,0x30,0x4B,0x3A
                ,0xDC,0xD1,0x81,0x6B,0x39,0xA1,0x88,0x21,0xE0,0xF6,0xE5,0x9C,0x94,0xD0,0x19,0xD2,0xF1,0xBD,0x11,0x68
                ,0x5E,0x0A,0x8E,0x85,0x02,0xB9,0x99,0x7C,0x49,0xBB,0x0C,0x79,0x87,0xAC,0x1C,0x07,0xA3,0x0E,0x91,0xBE
                ,0xF0,0x83,0x48,0x2C,0xEB,0xF7,0xB8,0x7E,0x20,0xA2,0x04,0x64,0x62,0x82,0xD7,0xD9,0xBF,0x70,0x01,0xFE
                ,0x45,0x03,0x0D,0x2E,0x2B,0xEE,0xE1,0x8D,0x6F,0xA4,0xB1,0x15,0xDD,0xBC,0xC2,0x89),
                array(0xF2,0x91,0x62,0xB6,0x41,0x7D,0x78,0x8A,0x73,0x04,0x7C,0x77,0x95,0x0D,0xBC,0xFF,0x46,0xFD,0x8C,0x2C
                ,0xB4,0xDC,0xF7,0x52,0x5B,0xE9,0x35,0x3A,0x4E,0x0C,0x58,0x1E,0xA9,0x6F,0x68,0x6C,0xF1,0x9A,0x13,0xB7
                ,0x89,0xAC,0x39,0x7B,0xC0,0xC1,0xDF,0x92,0xA8,0x19,0x7F,0x01,0x42,0x99,0x5A,0x6A,0x51,0xE8,0x27,0xE5
                ,0xC4,0xAA,0x07,0xFC,0x5C,0x90,0xAB,0x24,0x17,0x9C,0x9E,0x4A,0xB1,0xDB,0xCE,0x1D,0x7E,0x8B,0xBD,0xA7
                ,0xE4,0xAD,0xC6,0xB8,0xE0,0x65,0x80,0x0B,0x29,0xC7,0x16,0xFA,0x10,0xE1,0x61,0xCC,0x9B,0x75,0x93,0x70
                ,0x83,0x8F,0x05,0x5E,0x2F,0x23,0x06,0xA2,0x63,0xEA,0xD3,0x82,0x56,0xD8,0xF5,0x86,0x96,0x69,0x2A,0x97
                ,0xE3,0xF0,0xF9,0x1C,0x64,0x71,0xA3,0xB0,0xAF,0x6E,0x09,0x5D,0x14,0xF6,0xD5,0x3B,0xC3,0x49,0x74,0xA5
                ,0xA4,0x1F,0xB3,0x28,0xD1,0x50,0xCF,0x43,0xD7,0xF3,0x3D,0x3C,0xCA,0x87,0xC8,0x34,0x33,0x38,0x48,0x9F
                ,0xA0,0xF4,0x76,0x36,0x1A,0x08,0xE7,0xE2,0x4B,0x9D,0xC2,0x67,0x8E,0x5F,0x21,0xEF,0x53,0x72,0xD0,0x2B
                ,0x79,0xEC,0x2E,0xF8,0x25,0xBB,0xDA,0x03,0x4D,0xCD,0x6D,0x0F,0x98,0xFB,0xDE,0x1B,0x45,0xE6,0xFE,0xD4
                ,0x20,0xCB,0x00,0xC9,0x94,0x11,0xEB,0xD2,0x2D,0x18,0xBA,0x3F,0x37,0xBF,0xA1,0xEE,0x84,0x30,0x59,0x54
                ,0x22,0xAE,0xA6,0x15,0xDD,0x40,0x88,0x44,0x55,0x4C,0x0E,0x12,0x85,0xB2,0x6B,0x81,0xED,0x4F,0x47,0x3E
                ,0x0A,0x66,0xB9,0x60,0xD9,0x8D,0xC5,0xD6,0x57,0x26,0x32,0x31,0x7A,0xBE,0xB5,0x02),
                array(0x4A,0x7D,0xD8,0x22,0x11,0x56,0x0E,0xB6,0x06,0x04,0x90,0x18,0x1E,0x9F,0xAD,0x8A,0x95,0xC1,0x6E,0x4E
                ,0x6B,0x1D,0xC8,0x93,0x8C,0xF5,0xB9,0x9B,0xEC,0x9D,0xE5,0x5B,0xD2,0x74,0x17,0xA6,0x8F,0xB8,0xC9,0x98
                ,0x5D,0xE2,0x76,0x80,0x41,0x4B,0xBD,0xAB,0x14,0xB1,0x4D,0xD0,0x1C,0xFF,0xA4,0x59,0x21,0xCF,0x42,0x8E
                ,0xE6,0x84,0x03,0xE8,0x3F,0xDC,0x99,0xF3,0x5F,0xFE,0xAE,0x2B,0xDF,0x53,0xFA,0xAC,0x73,0x26,0xDE,0xCA
                ,0x8D,0x5C,0xFC,0x71,0x32,0xC4,0xA2,0xF6,0xEB,0x78,0xCD,0xE4,0x0C,0xB5,0x00,0x10,0x2D,0xE9,0xEF,0x5E
                ,0x70,0x51,0x65,0xF1,0x57,0x0B,0x3C,0x4C,0x64,0x3B,0xA7,0xAA,0xE7,0x79,0xBC,0x2A,0x15,0x44,0xB2,0x27
                ,0x3E,0x9A,0x3A,0x7F,0xA8,0x36,0xBE,0x01,0xC0,0xED,0xD9,0xE1,0x67,0x46,0x58,0x96,0xDA,0x81,0xDD,0xC5
                ,0x40,0xA5,0x86,0xE0,0xD3,0xC6,0xA0,0xFD,0x7B,0x47,0xA1,0x52,0x97,0xC2,0x12,0xD5,0x13,0x16,0x35,0xBF
                ,0x63,0x7E,0xB7,0x1F,0x23,0x30,0xF2,0x87,0x5A,0x89,0xCE,0xF7,0x0A,0x31,0xAF,0x62,0xF9,0xD4,0x9C,0xEA
                ,0x66,0x60,0x72,0xBB,0x02,0xB4,0x25,0x7C,0x55,0x69,0x88,0xC3,0x6A,0x82,0xD6,0xCC,0x50,0x2C,0xA9,0x1A
                ,0x38,0x20,0xF0,0x08,0xD1,0x85,0xB0,0x2F,0x29,0x24,0x8B,0x91,0xA3,0x43,0x61,0x09,0xDB,0x45,0x34,0xF8
                ,0xD7,0x6C,0x39,0x1B,0xFB,0x33,0xBA,0x68,0x37,0xE3,0x6D,0x28,0x48,0x07,0x54,0xC7,0x92,0x3D,0x9E,0x49
                ,0x2E,0xB3,0x0D,0x83,0x94,0xEE,0xF4,0xCB,0x77,0x75,0x0F,0x7A,0x05,0x6F,0x4F,0x19),
                array(0x84,0xB9,0x6B,0x80,0x7E,0x2B,0x17,0x85,0xD1,0xF9,0xC0,0x00,0x0D,0xC1,0x53,0xFF,0x57,0x1A,0x72,0x37
                ,0x49,0x3F,0xEE,0x10,0x21,0xBA,0x79,0xA7,0x45,0x58,0x94,0xDC,0x7F,0xD7,0x95,0x08,0x2C,0xCB,0x87,0xE2
                ,0x9B,0xE3,0x67,0x61,0xBE,0x90,0xAA,0xB5,0xDF,0x8D,0x97,0xF3,0x48,0x1E,0xAE,0xF8,0x73,0x93,0xB0,0x02
                ,0x0F,0x59,0x74,0xD8,0x1F,0x69,0xA3,0x05,0xBB,0xB2,0x23,0x25,0x13,0x38,0x1B,0xD9,0x7C,0xAC,0x40,0x26
                ,0x99,0x96,0x63,0x06,0x24,0x8C,0x55,0x35,0xEC,0x5B,0xDD,0x07,0x9A,0xBD,0x9D,0x4E,0xD6,0xD5,0xB4,0xAB
                ,0xEA,0x78,0x28,0xC6,0xB3,0x86,0x47,0xA9,0x6C,0xFC,0x8E,0xD0,0xCC,0xA8,0xC5,0xF2,0xB7,0xCE,0x8F,0xC9
                ,0xA2,0x01,0xCA,0xE5,0x5E,0xC8,0xA6,0x4A,0x98,0xEF,0x12,0xED,0x8A,0xCF,0x70,0x92,0x2D,0x56,0x16,0x89
                ,0x4B,0x68,0xF5,0x52,0xEB,0xE1,0x3E,0x62,0x43,0x11,0x20,0x14,0xC2,0x22,0x5F,0x3A,0x19,0x3C,0x33,0xC7
                ,0xB6,0x4C,0x66,0x32,0xDA,0x7A,0x77,0x83,0x4F,0xD3,0x3D,0x76,0xE6,0x75,0xA0,0x2E,0xFA,0xE7,0xC4,0x27
                ,0x82,0x0E,0x0B,0xDB,0xA4,0x04,0x5A,0xFE,0xC3,0x6D,0x7D,0xD4,0x9E,0xDE,0x29,0x0A,0x7B,0xAD,0xCD,0x65
                ,0x71,0x9C,0x4D,0x03,0x5D,0x42,0x36,0xFD,0x18,0xF0,0x30,0xE9,0x15,0x0C,0x54,0xE8,0xB8,0x09,0xB1,0xF4
                ,0xF6,0x9F,0x81,0x34,0x31,0x51,0x2A,0xBF,0xA5,0xFB,0xBC,0x8B,0x60,0x50,0x5C,0xF7,0x3B,0x41,0xE0,0xAF
                ,0x91,0x39,0x6A,0xE4,0x2F,0xD2,0x46,0x1C,0x44,0x6E,0x88,0x1D,0x64,0x6F,0xF1,0xA1),
                array(0x78,0x9F,0x9E,0x6E,0x94,0x77,0xA6,0x8F,0x87,0xBA,0xF4,0x17,0x04,0x2D,0x3A,0xF5,0x81,0xED,0x1D,0x6B
                ,0x1A,0x5D,0xA1,0x35,0x30,0x07,0x57,0xF3,0x2E,0xD5,0x06,0x0B,0xD3,0xD9,0x31,0x34,0xC9,0x75,0x91,0x39
                ,0x9C,0xDE,0x01,0xFC,0xB8,0xA9,0xC8,0x85,0xF1,0x8A,0x0F,0x18,0x58,0x5A,0xD7,0x42,0x40,0x8E,0x6D,0x2C
                ,0x38,0x29,0x9A,0x24,0x49,0xA8,0xA7,0xDA,0x37,0x5B,0x3F,0x6A,0x79,0x53,0xCC,0x47,0x98,0x41,0x69,0xA3
                ,0xDB,0x46,0x6C,0x93,0xD2,0x28,0xC1,0xF2,0xEC,0xBC,0x45,0x3D,0xDF,0x8D,0xF9,0xD4,0x12,0x1B,0x3B,0x99
                ,0x00,0xE0,0xB5,0xEA,0x59,0x56,0x7E,0x51,0x08,0xB0,0x5E,0x3C,0xE9,0x89,0x83,0xB9,0xAC,0xAF,0x19,0x1C
                ,0xCF,0xAA,0x09,0xD6,0xA5,0x9B,0x76,0x4B,0xC2,0x70,0x43,0x97,0xAD,0xD1,0xB3,0xBE,0x60,0x62,0xFA,0x13
                ,0x7A,0x23,0xC7,0x7D,0xBF,0xA4,0x0D,0x4F,0x4D,0x64,0x73,0xCE,0x05,0x22,0xD8,0x86,0x61,0x25,0xE5,0xE6
                ,0x67,0x4A,0x63,0xF6,0x14,0x82,0x32,0x88,0x90,0xBD,0x02,0x0A,0xFD,0x72,0x2F,0xEE,0x52,0x9D,0x7B,0xB2
                ,0x96,0xCA,0x26,0x03,0x68,0xB6,0xCB,0xFF,0x15,0xE1,0xF7,0x2A,0xDC,0x5C,0x2B,0xE7,0x54,0xF8,0x27,0xAE
                ,0x44,0x48,0x21,0xC0,0xA0,0x50,0x80,0x7C,0xDD,0xB1,0xC4,0xFE,0xEF,0x65,0x92,0xB4,0x7F,0x1F,0x55,0x66
                ,0xE4,0x16,0xEB,0x6F,0x1E,0xAB,0xF0,0xE3,0x0C,0xC6,0x10,0xE8,0xFB,0x4C,0x95,0xA2,0x74,0x36,0xD0,0x3E
                ,0x84,0x8B,0x71,0xE2,0x11,0x8C,0xBB,0xCD,0x20,0x0E,0xB7,0x5F,0x33,0xC3,0x4E,0xC5),
                array(0x7A,0x86,0x31,0xF5,0xE8,0xAB,0x4A,0xB5,0x78,0xC3,0x8A,0xE6,0xD9,0x48,0xC8,0x9E,0x87,0x37,0x03,0xF4
                ,0xB0,0x60,0x3F,0x0E,0x70,0x2D,0x12,0x20,0xBD,0x94,0x49,0x06,0xD4,0x44,0xC7,0x99,0x4D,0x6B,0x58,0xB1
                ,0x09,0xF6,0x32,0x6A,0x47,0x07,0x6F,0x76,0x9F,0x91,0x9C,0x7D,0x66,0x23,0x79,0xCA,0xB4,0xA7,0xF2,0x7C
                ,0xF7,0x0F,0xFA,0x93,0xD2,0xCC,0x13,0xE1,0xCE,0xCF,0x8D,0x3D,0x85,0xFB,0xF3,0x00,0x01,0xA5,0x22,0x59
                ,0xDE,0x81,0x55,0x88,0x3A,0xB7,0x08,0x68,0x46,0x69,0x26,0x35,0x8E,0x4C,0xD8,0xDF,0x90,0x57,0x75,0x2A
                ,0x21,0x24,0x04,0x6E,0xCD,0x7F,0x8C,0x92,0x82,0xBF,0x61,0xA3,0xAC,0xED,0x9D,0xF1,0xB6,0x42,0x54,0x77
                ,0xE3,0x9A,0xC1,0xDC,0xAD,0x97,0xBB,0x0D,0xCB,0x38,0x43,0x8B,0xEE,0xE2,0x41,0xEB,0xD5,0xFD,0x9B,0x36
                ,0x8F,0x5D,0xAE,0xDD,0x89,0xB8,0xD6,0x16,0xB2,0xAA,0x34,0xFC,0xA2,0x50,0x51,0x65,0x53,0x4F,0xC0,0x5C
                ,0x95,0x40,0xBC,0x62,0x30,0xEC,0x52,0x29,0x2F,0x73,0x64,0x5E,0x14,0x10,0x80,0xA6,0x84,0xF9,0xC9,0xF0
                ,0x1C,0xE4,0xFE,0xDA,0x45,0x5B,0x2C,0xD0,0xC2,0x1D,0x67,0xEF,0xA0,0xEA,0x33,0x3C,0x19,0xB3,0x25,0x7B
                ,0x11,0xA8,0x74,0x3B,0xDB,0x0A,0xD3,0x96,0xC4,0x28,0x4E,0x6C,0x72,0x7E,0x18,0xE5,0xBA,0xE7,0x5F,0xD1
                ,0xD7,0x1B,0x6D,0x98,0x0C,0x0B,0xE0,0xA1,0x2E,0xFF,0x39,0x02,0xC6,0xB9,0x5A,0x3E,0x15,0xC5,0xAF,0xE9
                ,0x1F,0x56,0x17,0x83,0x1E,0xF8,0x71,0x4B,0x27,0x05,0x63,0xA9,0xBE,0x1A,0x2B,0xA4),
                array(0x94,0x1A,0x1B,0xA0,0x51,0xA4,0xF3,0x52,0x32,0xB8,0xE8,0xC8,0x35,0xF5,0x7F,0xF4,0x4C,0xCC,0x88,0xB2
                ,0x98,0xCD,0x9D,0x39,0x2E,0x62,0x03,0x46,0x02,0x87,0xB7,0xDB,0x90,0xC7,0xD7,0xC0,0x05,0x29,0xA2,0x0F
                ,0x3C,0xAC,0x64,0x33,0x8D,0x37,0xE2,0x12,0x10,0x53,0x8E,0xB0,0x89,0xA6,0x0B,0xA1,0x67,0x99,0xA8,0x78
                ,0x9B,0xFE,0x7C,0xA9,0xFF,0xE4,0x45,0xDE,0x4F,0xB6,0xC2,0xBA,0x57,0xA3,0x4E,0x00,0x04,0xB9,0x1D,0x3E
                ,0x2D,0x8B,0xB1,0x65,0xC6,0xF8,0x36,0xC9,0xE6,0xD1,0x25,0xFD,0x73,0xF6,0xED,0xF1,0xAE,0xAB,0xD9,0x42
                ,0xA7,0x54,0xBD,0x5F,0xCA,0x5D,0x06,0xC5,0x8C,0x91,0x3F,0xE0,0x16,0xCF,0x38,0xAA,0x01,0x8A,0x5C,0xCB
                ,0x5A,0x7A,0xD4,0x63,0x43,0x0D,0xC3,0x7B,0x3D,0xB5,0x40,0x19,0x71,0xBE,0x18,0x0E,0xEA,0x0A,0x22,0x96
                ,0xC1,0x48,0x9A,0x7E,0x20,0xC4,0x23,0xD5,0xDD,0xBC,0x9F,0xDF,0x07,0x83,0x26,0x84,0x2C,0x4B,0x09,0x47
                ,0xF0,0x85,0x66,0x49,0x58,0x69,0x74,0x80,0x4D,0xBF,0x0C,0xF7,0x28,0x31,0x14,0x34,0x5E,0xB3,0xE3,0x60
                ,0xF2,0x4A,0x56,0xF9,0x6B,0x6E,0x7D,0xAD,0xEF,0x95,0x2A,0x8F,0xE9,0x2F,0x6C,0xE1,0xFB,0x70,0x86,0xEC
                ,0x08,0x76,0xE5,0xD6,0x1E,0x9E,0x9C,0x77,0x72,0x97,0x5B,0x1F,0xA5,0x24,0xD2,0x93,0x92,0x6A,0x50,0x27
                ,0x82,0xBB,0xAF,0xE7,0x44,0x6D,0x41,0x2B,0xDA,0xFA,0x61,0xD3,0x1C,0x59,0xD8,0xFC,0x21,0xDC,0x68,0x81
                ,0xEB,0x13,0x75,0x3B,0x30,0x55,0x11,0x79,0x3A,0xEE,0x15,0x6F,0xB4,0x17,0xD0,0xCE),
                array(0xC4,0x2D,0x7B,0xF5,0xC2,0x58,0xC3,0x0D,0x81,0x41,0xA1,0x2F,0x90,0x33,0xBC,0xAC,0x13,0xFF,0x4A,0x91
                ,0x5F,0x40,0x66,0x5C,0x8B,0x30,0x34,0x19,0xAA,0xE8,0x38,0x43,0x26,0x8C,0x2C,0xE9,0x8A,0xA0,0x3A,0x6F
                ,0x69,0xC5,0xFC,0x32,0x9F,0x85,0x53,0x9D,0x76,0x5B,0xA9,0x80,0x06,0x67,0xEC,0x99,0xDC,0xD1,0xCB,0x0B
                ,0xA7,0x78,0xBD,0xD7,0xC0,0x5A,0xA2,0xD2,0x12,0x3D,0x48,0x8E,0xAB,0xAE,0xC9,0x0E,0x3F,0x16,0xEA,0xD3
                ,0xA6,0x4C,0xE2,0xF7,0x27,0x63,0x21,0x14,0xA4,0x6D,0x54,0xE3,0x65,0xF8,0x84,0xB2,0x9B,0xD5,0x1F,0x22
                ,0xDB,0xA5,0xBF,0xB9,0xB6,0xF9,0x23,0xF6,0x56,0x98,0x62,0xFE,0x20,0x01,0x02,0x95,0x35,0x51,0xC8,0x4B
                ,0xD4,0x1E,0xC1,0x5E,0x47,0x29,0xE6,0x1B,0x17,0xAD,0x36,0x03,0x92,0x9A,0xEB,0xBA,0x15,0xE7,0x8F,0xC7
                ,0x1A,0x4F,0xF3,0x00,0xDE,0xB3,0x0C,0x97,0xEF,0xDF,0x55,0x31,0xB1,0xF0,0xEE,0x5D,0x0A,0x64,0x72,0x88
                ,0xE1,0x50,0x7F,0xF2,0x6B,0x24,0x39,0x2B,0x2A,0x52,0xE5,0x3B,0x18,0xD6,0xD8,0x77,0xB5,0xDA,0x2E,0x86
                ,0xBB,0xD9,0x61,0x37,0x49,0xED,0xCF,0x70,0x10,0x9C,0xE4,0xFA,0xF1,0xF4,0x42,0xC6,0x4E,0x93,0xB7,0xD0
                ,0xA8,0xB8,0x57,0x25,0xDD,0x79,0x87,0x07,0x71,0x83,0x4D,0x9E,0x6C,0xB0,0x7A,0xFD,0x1D,0xCD,0x46,0xCC
                ,0x7C,0x8D,0xB4,0x74,0x89,0x04,0x09,0xA3,0x45,0x7D,0x3C,0x08,0xCE,0xFB,0x6E,0x11,0x05,0x75,0x73,0xBE
                ,0x1C,0x94,0x44,0xCA,0x68,0x60,0xAF,0x0F,0x59,0x6A,0xE0,0x7E,0x28,0x3E,0x82,0x96),
                array(0xDC,0x9F,0x02,0x73,0x68,0xC9,0xAC,0xD8,0xCF,0x7F,0xCD,0x23,0xDB,0x5E,0xC1,0x74,0x6F,0x4F,0x55,0x6E
                ,0x2C,0xB5,0x3F,0xD5,0x81,0xF8,0x14,0xD0,0xE2,0x30,0x22,0x2E,0xB1,0x06,0xC0,0x3B,0x80,0x3C,0x6C,0xF9
                ,0x2D,0x9E,0x7B,0x51,0xE3,0x16,0x0F,0x98,0xA4,0x3D,0xE4,0x5A,0x0E,0x96,0x31,0x5B,0xB4,0xFE,0x63,0xEB
                ,0xA1,0xDF,0x52,0x99,0x7C,0xA5,0x1E,0x32,0x34,0xBD,0x4B,0xFB,0x12,0xC6,0xFC,0x5C,0xAF,0x5F,0xEA,0x65
                ,0x47,0x42,0xA9,0xBC,0x9C,0xFA,0x05,0x45,0x62,0xDD,0xBB,0x6D,0x40,0x8B,0x43,0xF4,0x66,0xE1,0xD1,0xFD
                ,0x82,0xE5,0x17,0x19,0x3A,0xDA,0x37,0x1C,0x2B,0x00,0xBE,0x03,0x70,0x4C,0x11,0x72,0xA8,0x6B,0xD6,0xF0
                ,0x27,0x71,0xD3,0x4A,0x1B,0x92,0x58,0x21,0x97,0x09,0xA7,0x91,0xAA,0x9D,0xA6,0x0A,0x25,0x01,0x10,0x49
                ,0x0C,0xAB,0x24,0xDE,0xAD,0x8E,0x39,0x38,0x56,0x41,0x07,0xCC,0xCB,0x87,0xB6,0x95,0x2F,0x94,0x3E,0x85
                ,0xF3,0xE0,0xEE,0x59,0x50,0xAE,0xA2,0x8F,0x9B,0xF5,0xCE,0x7D,0xC5,0xD2,0x2A,0xF6,0x78,0x8D,0x61,0x67
                ,0xA0,0x8C,0xED,0xB9,0x7A,0xB0,0xE8,0x4E,0x79,0x90,0x7E,0x93,0xB8,0x54,0x29,0x0D,0xBF,0x36,0xE7,0x44
                ,0xD9,0x18,0x69,0x8A,0xBA,0xD4,0xB2,0x20,0x77,0xF2,0x1A,0x26,0x84,0xC3,0x60,0xEF,0xE9,0x04,0x86,0xFF
                ,0x0B,0x33,0xF7,0x28,0x13,0x1F,0x1D,0x64,0x83,0x53,0x46,0xC8,0xEC,0xD7,0x5D,0x76,0x48,0x6A,0x88,0xE6
                ,0xA3,0x35,0x08,0xC7,0x75,0x9A,0xB7,0x89,0xC2,0xCA,0x57,0x4D,0x15,0xC4,0xF1,0xB3),
                array(0x0B,0x85,0xE0,0x21,0x3C,0xA3,0x24,0x4F,0x8D,0x4C,0x20,0x39,0xC2,0xF1,0x9E,0x5A,0x45,0x37,0x91,0xEB
                ,0x46,0x1B,0x4D,0xF5,0x5C,0xF8,0xD8,0x60,0x1F,0x87,0xBA,0x50,0xD9,0xA6,0x49,0x43,0xD0,0x10,0xAB,0xE9
                ,0xBD,0x6B,0xDB,0xDE,0xAF,0x4E,0x6D,0x40,0x1E,0x22,0x70,0x2A,0xD1,0x94,0x9D,0xCC,0xC0,0xC3,0xDC,0x15
                ,0xCF,0x6C,0x56,0x9C,0x14,0x71,0xF4,0x27,0x23,0x84,0x13,0x4B,0x1A,0x80,0x31,0xE5,0x17,0xF2,0x88,0x98
                ,0xB8,0x5D,0x9A,0xF6,0x36,0x97,0xAE,0xD4,0x01,0xE6,0x3B,0xB2,0x32,0xE7,0x89,0x73,0xED,0x7C,0x0E,0x9B
                ,0x05,0xF3,0x3A,0x25,0x7F,0x29,0x08,0xFA,0x07,0xE2,0x92,0xBE,0x79,0x0A,0x72,0x8C,0xD3,0x66,0x09,0xB9
                ,0xC6,0x41,0xA7,0xEC,0x28,0x77,0x67,0x1D,0x33,0x64,0xC8,0x5E,0x02,0x26,0xDD,0x8B,0xEA,0xAD,0x68,0xF7
                ,0x52,0x95,0xEF,0x83,0x6F,0x90,0x11,0x69,0x04,0xD5,0x30,0x96,0x63,0xC1,0xFB,0xCA,0x0F,0xDF,0x82,0x38
                ,0xF0,0xB7,0xB1,0x75,0xC5,0xA9,0x58,0x0D,0xCE,0x61,0xC7,0x18,0xB3,0x55,0xB6,0x8E,0x00,0xFC,0xCD,0x2F
                ,0x74,0x06,0x51,0x76,0x48,0x93,0x5B,0xBC,0xA2,0x9F,0x8F,0xCB,0x35,0xA4,0x59,0xD6,0xD7,0x12,0x53,0x8A
                ,0x65,0xBF,0x0C,0x2B,0x44,0xB5,0x2D,0xA5,0x2E,0x4A,0xB0,0xDA,0x3F,0xE4,0x42,0x81,0x5F,0x54,0xFD,0x7B
                ,0x03,0xC9,0x16,0xA8,0xC4,0x19,0xFF,0x6A,0x3D,0xEE,0xF9,0x62,0xAA,0x57,0xA0,0x1C,0x2C,0x47,0xE8,0x7A
                ,0x86,0x7D,0x34,0x6E,0xA1,0xD2,0x78,0xE3,0xFE,0x99,0x3E,0xBB,0xAC,0x7E,0xB4,0xE1),
                array(0x6E,0x4A,0x17,0x7F,0x1B,0xA0,0x0E,0x54,0xBE,0xA5,0xA2,0x56,0x0F,0xE6,0x45,0xF1,0xDD,0x85,0x01,0x38
                ,0xF3,0xCC,0xE0,0x88,0xE3,0xF2,0x82,0xCA,0x86,0x9C,0x47,0x90,0xA8,0x76,0x96,0xAF,0x74,0x94,0x26,0x32
                ,0x7D,0x2E,0x52,0x70,0xEE,0xDF,0x98,0x4D,0x8E,0xCB,0x58,0x84,0x1A,0x68,0x3D,0xBD,0xEA,0x3F,0xE7,0xF8
                ,0xC3,0x81,0xF4,0x42,0x0B,0xD0,0x20,0xF6,0xF0,0xFA,0x93,0x7A,0x77,0xD4,0x3B,0x49,0x0C,0x24,0xC0,0x79
                ,0x25,0xAC,0x83,0xA1,0x4E,0x30,0x18,0x40,0xB0,0xBF,0x4C,0x2B,0x5D,0x51,0xB8,0x66,0xD5,0xE5,0x71,0x78
                ,0x7C,0xDC,0xE2,0xE9,0x9F,0x75,0x67,0xC1,0x6D,0x46,0xC4,0x03,0x06,0xAA,0x1D,0x13,0xD9,0x11,0xA7,0x5A
                ,0x5E,0x27,0x23,0x8D,0xC9,0xEB,0xAE,0x80,0xCE,0xD6,0xA9,0x63,0x3A,0x0D,0x04,0xD7,0x34,0xAB,0x8B,0xF9
                ,0x08,0x9A,0x5F,0xBC,0x59,0x92,0xFD,0x3E,0xE1,0x2F,0x57,0xFC,0xC8,0x44,0x5B,0x07,0x19,0x10,0x55,0x48
                ,0xDB,0x37,0x69,0x53,0x21,0x97,0x33,0x09,0xC2,0x99,0xFB,0xB7,0x2A,0x05,0x0A,0xD8,0x5C,0x8C,0xD3,0x64
                ,0xA4,0x15,0x7E,0xA3,0xB6,0x65,0x4F,0x2D,0x36,0x1F,0xF7,0x9B,0x9E,0xD1,0xED,0x8A,0xB5,0xEF,0x72,0x35
                ,0xC6,0x89,0xB9,0x87,0x50,0x8F,0xB1,0x6B,0xA6,0x2C,0xD2,0xAD,0x22,0x28,0x00,0x7B,0x6A,0x62,0xCF,0xB2
                ,0x91,0xF5,0xCD,0x6F,0x4B,0x14,0xDE,0xBA,0x16,0x3C,0x61,0x41,0x12,0x95,0x02,0xB4,0x60,0xE4,0x73,0xDA
                ,0xE8,0xC5,0x6C,0xFE,0xC7,0x1C,0x29,0x39,0x1E,0x43,0x31,0x9D,0xB3,0xFF,0xBB,0xEC),
                array(0xDC,0x2D,0xE8,0xEA,0xB2,0x0D,0x02,0x70,0x7C,0x1C,0x89,0xD2,0x06,0xB6,0x9D,0x46,0xD4,0xB4,0x36,0x19
                ,0x01,0x8F,0x52,0xF1,0x47,0x25,0x1E,0x00,0xFF,0xA4,0xA7,0x94,0x99,0x93,0x17,0xE7,0x90,0x6E,0x11,0x0F
                ,0xAE,0x3A,0xDF,0xAA,0x15,0x3E,0x4D,0xD6,0xD3,0x59,0x30,0x77,0xFC,0xC8,0x32,0x5B,0x09,0x35,0xC1,0xEF
                ,0x4C,0x29,0x67,0x72,0x6A,0x4B,0x3D,0xF3,0xA0,0x8B,0x0A,0x41,0x18,0xEE,0xC3,0x03,0x1B,0x8E,0xFE,0xC5
                ,0xE6,0x34,0xA3,0x80,0x7A,0xD0,0x53,0x16,0x5D,0xFD,0x14,0x4E,0x0B,0x3C,0x6B,0xE9,0x05,0x4A,0x45,0xD7
                ,0xE0,0xF9,0x58,0x68,0x1D,0xC6,0x71,0x56,0xEC,0xF0,0x37,0x73,0x1A,0xBA,0xB0,0xA8,0x9A,0x62,0x26,0xBB
                ,0xD9,0x85,0xCD,0xD1,0x66,0xBD,0xB7,0x49,0xBE,0x9C,0x10,0x44,0x7B,0x2B,0x5F,0x9F,0xA2,0x98,0xC7,0x96
                ,0xD8,0x0C,0x5A,0xF5,0xCF,0x1F,0xAD,0x51,0x7D,0xEB,0x57,0xCB,0x23,0xA6,0xC4,0x3F,0x61,0x92,0x3B,0x63
                ,0x78,0x81,0x8C,0x2F,0x31,0x69,0xC0,0x27,0xB5,0x9B,0x04,0x28,0xE5,0x50,0x95,0x84,0xDB,0xA1,0xCE,0x83
                ,0xBF,0xF6,0x7F,0xAB,0x6C,0x2C,0x79,0x82,0xB1,0x33,0x88,0x13,0x55,0x2E,0x7E,0x2A,0x0E,0x9E,0x20,0xCA
                ,0x6F,0xF8,0xDA,0x43,0x86,0x5C,0xA9,0xF2,0xB9,0xB3,0xC9,0x8A,0x5E,0x24,0x07,0x42,0x6D,0xF7,0xC2,0x54
                ,0xDD,0x21,0xED,0x91,0xE1,0xAC,0x64,0x75,0x40,0x97,0xE2,0x4F,0xBC,0xD5,0xF4,0xE4,0x74,0x65,0x22,0xE3
                ,0x12,0xAF,0x60,0x87,0x39,0x48,0xDE,0x08,0xFB,0xCC,0xA5,0x8D,0x76,0xFA,0x38,0xB8),
                array(0x59,0x76,0xD5,0x38,0xC2,0xBA,0x44,0x39,0x24,0x6A,0x48,0xCB,0x99,0x46,0x55,0xB7,0xF4,0xAC,0xE6,0x2A
                ,0x89,0xF6,0x68,0x61,0x2B,0x4D,0x63,0x1E,0x94,0xEF,0xCD,0x71,0x03,0x47,0x86,0xFE,0x10,0x18,0x49,0x6D
                ,0x53,0xED,0xD3,0x08,0x5A,0xE4,0x9B,0x29,0x37,0xBC,0xE2,0xB0,0xF8,0x00,0xCA,0x01,0xEA,0x4B,0x95,0xBF
                ,0x2D,0x8B,0x7A,0x14,0x16,0x9E,0x43,0xF3,0x4C,0x84,0xA3,0x17,0xBB,0x3D,0xF5,0x1D,0x1C,0x8A,0x70,0x6F
                ,0xBE,0xC8,0x4A,0x0D,0xC0,0x64,0x90,0x45,0x0F,0xC5,0xFA,0x25,0x85,0x5B,0xC3,0x5F,0x34,0x0B,0x3F,0x06
                ,0x31,0xA5,0x40,0xDD,0x8C,0x50,0x7D,0xC1,0x02,0x98,0x7B,0x27,0xE8,0xA2,0x1F,0xAB,0xDF,0x33,0x15,0xB6
                ,0xF7,0x93,0xEE,0x07,0xA8,0xDA,0x4F,0x62,0x6B,0x9C,0x2C,0x0C,0xD2,0x78,0xE7,0xD9,0x60,0xB3,0x52,0x22
                ,0x80,0x41,0xD1,0x23,0xDE,0x7F,0x72,0xD7,0x87,0xFC,0x09,0x35,0x96,0xAE,0x8E,0x9A,0x75,0xDC,0x82,0xC9
                ,0xA4,0x1A,0xF0,0xCF,0xD6,0xA6,0x19,0x77,0x0A,0xEB,0xB4,0x20,0x3C,0xD4,0x54,0xA0,0x88,0x81,0x8D,0x58
                ,0x66,0x1B,0xCE,0x79,0xB5,0x69,0x74,0x05,0x21,0x42,0x9D,0x6E,0xD8,0x13,0x3B,0x5E,0xAF,0x73,0x28,0xE3
                ,0xC7,0xAA,0xF1,0x2F,0xD0,0x3A,0xE1,0xFF,0xF9,0x4E,0x3E,0x30,0x8F,0x7E,0x65,0xB2,0xE0,0xFD,0x2E,0xEC
                ,0xB9,0x92,0xB1,0x9F,0x51,0xAD,0x36,0x7C,0x32,0x11,0x5C,0xCC,0x6C,0xA9,0x12,0x97,0xE9,0x5D,0x83,0x56
                ,0x67,0xC6,0xB8,0x0E,0xA7,0xC4,0x57,0x04,0xA1,0xE5,0xF2,0xBD,0xFB,0xDB,0x91,0x26),
                array(0xBC,0x60,0xC3,0x1C,0xA6,0x28,0xE9,0xDC,0x53,0x7F,0x04,0x99,0x41,0x3E,0xF7,0xAD,0xCF,0xE1,0xFD,0x24
                ,0x35,0xEE,0x95,0x22,0xFB,0x72,0xA0,0x25,0xED,0x47,0xBB,0xA3,0xAE,0x55,0x8B,0x59,0xF0,0x58,0x05,0x52
                ,0xF2,0x5E,0x3C,0x6C,0x12,0x56,0x0E,0x5B,0x06,0x1B,0x57,0x23,0xBF,0xBD,0x2D,0xEB,0x48,0x38,0xF3,0xC7
                ,0x78,0xAA,0x44,0x7A,0xA1,0x15,0x11,0x14,0x9B,0xCE,0x4A,0x43,0x0F,0x79,0xC5,0xB9,0xD6,0xD0,0xF9,0xD4
                ,0xB5,0x62,0x9C,0xB2,0x6A,0xB7,0x7D,0x36,0xC6,0xCB,0x0A,0xCD,0x9D,0x51,0x33,0xF6,0x64,0x2C,0x45,0x61
                ,0xE5,0x8F,0x92,0x5A,0xE0,0x84,0x1E,0x75,0x91,0x5C,0xCC,0x6E,0x16,0x4B,0xD5,0xDD,0xCA,0x71,0x39,0xEF
                ,0x66,0xAC,0x49,0x8C,0xB0,0x20,0x1D,0xF4,0x01,0x8E,0x09,0xFA,0x27,0xB6,0xC0,0xAB,0x26,0x18,0xA9,0xDA
                ,0x68,0xFE,0xD7,0x63,0x89,0x4D,0x3D,0x65,0xE7,0xA4,0xD2,0xD9,0x0D,0x7C,0x4C,0x31,0x7E,0x88,0x8A,0x94
                ,0x07,0xC2,0x19,0x4E,0x73,0x93,0xF5,0x30,0x2F,0x03,0x8D,0xC8,0x1A,0x2E,0xC4,0xA5,0x02,0x32,0xF8,0x1F
                ,0x00,0x0C,0x3F,0x5D,0x82,0x08,0x50,0x96,0xEA,0x6D,0x3A,0x97,0xBE,0x42,0x87,0x80,0x17,0x2A,0x5F,0xE6
                ,0xD3,0x70,0x9E,0x69,0x83,0x29,0xA2,0x90,0x21,0x6B,0xA8,0x4F,0xA7,0xDE,0x86,0xFC,0x40,0x9F,0x2B,0x3B
                ,0x13,0xBA,0xB8,0x81,0xB3,0xE2,0x34,0xD1,0xC9,0xD8,0x85,0xE4,0x98,0x10,0xF1,0xAF,0x37,0x76,0xB1,0xDF
                ,0xDB,0xFF,0x6F,0xE8,0xEC,0x9A,0x74,0x0B,0x54,0x7B,0x67,0x46,0xB4,0x77,0xC1,0xE3),
                array(0xC0,0x25,0xC8,0x51,0x4E,0x76,0xD9,0xC6,0xAE,0x57,0x04,0xB9,0x0D,0xB8,0xFB,0xA9,0x67,0x91,0xD2,0xDA
                ,0x26,0x54,0x13,0x07,0x24,0x96,0x66,0xDF,0xCA,0x61,0xED,0xF1,0x6A,0xD5,0x56,0xBF,0x8C,0x46,0xF9,0x1C
                ,0xEF,0x75,0xF8,0xE6,0x7D,0xB3,0x4A,0x63,0x97,0xE9,0x2E,0x36,0x4B,0x88,0xB6,0x8B,0x98,0x84,0x20,0x17
                ,0xD3,0x16,0xBA,0x08,0xDE,0xA1,0x11,0xE8,0x40,0xF6,0x78,0x8A,0x2F,0x86,0xE1,0x3B,0x35,0xCE,0xFD,0x70
                ,0xF4,0xD8,0x06,0xD1,0x8E,0x62,0xC4,0x47,0xC3,0xF7,0x69,0x3A,0x4F,0x00,0x93,0xE7,0x60,0x1F,0xA5,0x33
                ,0x44,0x32,0xC7,0xEA,0xAC,0x72,0x5E,0x15,0xCC,0x0B,0x6C,0xEB,0xC9,0xB1,0x37,0xE5,0x42,0xA4,0x45,0x4D
                ,0xD6,0x3F,0xD0,0xF2,0x9C,0x9A,0x18,0xB4,0x6F,0xBD,0xA0,0xE0,0x49,0xEE,0xB5,0x43,0x82,0xBC,0x52,0xDB
                ,0x80,0x7E,0x8F,0x74,0xD7,0x8D,0xB2,0x9F,0x83,0x85,0x2C,0xF3,0xFA,0x3E,0x21,0x4C,0x0F,0x3D,0x0E,0x0C
                ,0x38,0x05,0x94,0xE3,0xC2,0xC1,0x99,0x1E,0x64,0xE4,0x1A,0x5F,0x92,0x02,0x7F,0xAF,0x22,0xA8,0x1B,0x27
                ,0xB7,0x0A,0xDD,0x3C,0x7A,0x5A,0x10,0x9B,0x03,0x55,0x58,0x9E,0xBE,0x41,0xA3,0xAB,0x19,0x6E,0x09,0xA2
                ,0xD4,0x6B,0x14,0xFF,0xF5,0x28,0xCF,0xE2,0x65,0xFE,0xFC,0x39,0xB0,0x01,0x23,0xC5,0x50,0x71,0x31,0x1D
                ,0xEC,0x48,0x68,0x29,0x73,0x2D,0x5D,0x12,0x7B,0xF0,0x2B,0xA7,0x2A,0x34,0x53,0xAD,0x30,0xBB,0x5B,0x89
                ,0xAA,0x59,0x90,0xCB,0x7C,0x79,0xA6,0xCD,0x81,0x5C,0x77,0x6D,0x87,0xDC,0x95,0x9D),
                array(0x2C,0xF9,0x77,0x8E,0x80,0xCD,0xD4,0x12,0x86,0xA4,0x43,0x15,0xE9,0x6B,0xA3,0xB3,0x82,0x88,0x10,0x54
                ,0xE1,0x27,0xE8,0xC9,0x79,0x32,0x3B,0xED,0x9E,0x4C,0x21,0x5E,0x2F,0x03,0xE2,0x6F,0x87,0x92,0x29,0xE7
                ,0x66,0x6A,0xAC,0x5C,0x09,0xCB,0xD6,0xD9,0x1D,0x0F,0x0B,0x61,0x9B,0xDE,0x5D,0x05,0x49,0x76,0x18,0xE0
                ,0x75,0xBD,0x55,0xD0,0x81,0xD5,0xC1,0x24,0xFE,0x90,0x69,0x70,0xCA,0x34,0xB6,0xCF,0x01,0xFF,0x16,0xEF
                ,0x4B,0xD8,0xBA,0xE6,0xC4,0xB5,0xFD,0xA9,0xBE,0x48,0xF4,0xCC,0x04,0x84,0x4D,0x83,0xE4,0xC0,0x93,0x14
                ,0x2A,0x07,0xAE,0xF5,0x37,0x33,0x42,0x97,0xB4,0xF6,0x96,0x8B,0xA2,0x1F,0x2B,0xC7,0x35,0x5B,0x46,0xC6
                ,0x65,0xDB,0x6C,0x63,0xA7,0xB9,0xFB,0x89,0xAB,0x38,0xBC,0xAA,0xC5,0xDF,0x85,0x28,0x8C,0x67,0xA1,0x56
                ,0x40,0x4A,0xE3,0x95,0x26,0xB1,0xF1,0xBB,0x1E,0x62,0x68,0x7F,0x36,0x3A,0x60,0xAD,0x71,0x11,0x47,0x3F
                ,0x1A,0xDC,0x8F,0x74,0x7E,0xF8,0xF0,0x51,0x20,0x02,0xDD,0x94,0xF7,0x0A,0x52,0x0C,0x9A,0x78,0x91,0x5F
                ,0xB2,0x58,0xD7,0x1B,0x39,0x8D,0xD3,0xEB,0x98,0xA8,0xFA,0xB7,0xEA,0xC2,0x31,0xD2,0xA6,0x3C,0x64,0x7C
                ,0x44,0x13,0x8A,0x23,0xE5,0xEE,0x08,0x4F,0x7D,0xC3,0x57,0x9D,0x3D,0xFC,0xC8,0x30,0xEC,0x6E,0x1C,0x2E
                ,0x50,0xF3,0x22,0x4E,0x53,0x72,0xDA,0x7A,0x59,0x0D,0x25,0x45,0x3E,0xBF,0xD1,0x6D,0xAF,0x9F,0x73,0x7B
                ,0x5A,0x06,0x00,0x0E,0x41,0xA0,0x9C,0xA5,0xB8,0xB0,0xCE,0x17,0x2D,0x99,0x19,0xF2),
                array(0x26,0x99,0xCD,0x2D,0x72,0xBD,0x81,0xC0,0xE1,0xF1,0x62,0x59,0x5B,0x0E,0xC6,0x19,0xAC,0x4E,0x8E,0x22
                ,0x11,0xB3,0x53,0xAA,0xB5,0x5A,0xFE,0xA3,0xC2,0x25,0x32,0x46,0x16,0x87,0x09,0x6B,0xE2,0x14,0x0B,0x28
                ,0xB9,0xFA,0x92,0x7E,0xD8,0xAD,0x9F,0xA0,0x7C,0x15,0x6C,0xDB,0xA6,0xB0,0x2E,0x83,0x08,0xBE,0x89,0x44
                ,0x47,0xDC,0xCF,0x35,0x4F,0x1E,0xE4,0xE8,0x78,0x3E,0x48,0x75,0xE0,0xAB,0x96,0x31,0x24,0x6F,0x06,0x94
                ,0xA5,0x07,0x9E,0x50,0x93,0x1F,0xD9,0x70,0xC5,0x5E,0xA1,0x5C,0x63,0x91,0x39,0x7B,0x7D,0x3F,0xFD,0x57
                ,0x69,0xB2,0x1C,0x20,0x02,0x05,0x0D,0xD1,0x45,0x6D,0x73,0xAF,0x8D,0xB4,0xC9,0xF8,0x4A,0xEC,0x7F,0x6E
                ,0xA4,0xD2,0x88,0x34,0x49,0x60,0x9C,0xD3,0x97,0x54,0xF7,0x8C,0xCA,0x30,0xEE,0x01,0x13,0x00,0xD6,0x0C
                ,0x86,0xAE,0xEF,0xF5,0xBA,0xEA,0x74,0xFC,0x67,0x66,0x2F,0xF0,0xFF,0x4B,0x98,0x8F,0x64,0x37,0xE7,0xCC
                ,0x12,0xC8,0x9B,0x0F,0x18,0xFB,0x21,0x5D,0x90,0x52,0xF3,0x95,0x41,0x9A,0x10,0x6A,0x03,0xDD,0x27,0x2B
                ,0x71,0xD0,0x1B,0xBF,0x65,0x9D,0x58,0x17,0x85,0x3B,0x3A,0xC4,0xF6,0x7A,0xA8,0x23,0xC3,0x3C,0xED,0x33
                ,0x2A,0xD4,0x4C,0xBB,0x1A,0x5F,0xA9,0xC1,0x38,0xD7,0xE9,0x4D,0x84,0x0A,0xE3,0x56,0x1D,0xC7,0xB7,0x29
                ,0x79,0xCB,0x8A,0x36,0x42,0xF4,0xDA,0x76,0xDF,0xB6,0xA7,0xB8,0xD5,0x3D,0xBC,0xDE,0xF9,0x61,0x43,0x40
                ,0x51,0x8B,0xB1,0xF2,0xA2,0x77,0x55,0xE6,0x82,0xCE,0x68,0x04,0x80,0x2C,0xE5,0xEB),
                array(0x6A,0xE2,0x63,0xC1,0xF9,0x96,0x22,0x23,0x34,0x56,0x2D,0x32,0x3F,0x54,0x9E,0xBE,0xDE,0xB1,0x2F,0x8A
                ,0x4C,0xCB,0xF0,0x48,0xA8,0x75,0x19,0xF1,0x25,0xA5,0x07,0xEF,0xAC,0xFD,0x5C,0x33,0x44,0x39,0xBB,0x85
                ,0x68,0x3B,0x55,0x38,0xCC,0x14,0x53,0x09,0x31,0x9C,0xE9,0x4A,0x28,0x05,0x29,0x1F,0x0E,0x60,0xCF,0x73
                ,0xEC,0x2B,0x6C,0x9B,0xA9,0x11,0xFE,0xB6,0x66,0x52,0xFC,0xD5,0x41,0x0F,0x94,0xDA,0x1A,0xE7,0x47,0x0D
                ,0xAE,0xE4,0x98,0xC5,0xB5,0xE1,0x0C,0xB4,0x15,0x2C,0x3C,0x18,0x65,0x30,0x8C,0xBD,0x72,0xF5,0x08,0xB8
                ,0x76,0x83,0xF4,0x1E,0x92,0xA0,0x9F,0x1B,0x9A,0x46,0xAD,0x3E,0x59,0xF6,0xF2,0x78,0xA4,0x00,0xC9,0x8D
                ,0xED,0x16,0x03,0x97,0xD8,0xA6,0xF7,0x7B,0xDF,0xF3,0x7D,0x90,0x3D,0xC7,0x24,0xC4,0x13,0x93,0x7F,0xE5
                ,0x10,0x40,0x64,0x3A,0xEE,0x7A,0x04,0x6D,0xDB,0x9D,0x2A,0x87,0xFB,0xC2,0x81,0x21,0x45,0x91,0xD0,0x80
                ,0x6F,0x7C,0x26,0x4D,0x37,0xBC,0x27,0xD2,0x77,0x12,0x20,0xDC,0xEB,0xA2,0xA7,0xD6,0x01,0xC6,0xAB,0x71
                ,0xC3,0x5D,0xB9,0xDD,0x57,0x61,0xE8,0x89,0x86,0x36,0x5F,0x58,0x69,0xEA,0x2E,0x84,0x4F,0xE6,0xFA,0xCA
                ,0xA3,0x5A,0x4E,0x67,0x06,0xFF,0x4B,0x8F,0x62,0x5E,0x6B,0x74,0xAA,0xC0,0xD9,0x1C,0xE3,0xBA,0xB7,0xD4
                ,0x5B,0x0A,0xBF,0xD3,0x17,0xC8,0x8E,0x95,0xB0,0x70,0xAF,0x79,0xE0,0x43,0xD1,0x88,0xCE,0x7E,0xCD,0x82
                ,0xA1,0x51,0xB2,0x02,0xB3,0x8B,0x99,0x0B,0x50,0x1D,0x42,0x35,0xF8,0xD7,0x6E,0x49),
                array(0xFB,0x37,0x5A,0x70,0xC0,0x8C,0x46,0xAA,0x91,0x36,0xA4,0x61,0x2A,0x38,0xF4,0x29,0x0C,0xD3,0xAB,0xA3
                ,0x07,0x95,0x43,0x23,0x0B,0xC7,0xA6,0x7D,0xAC,0xB2,0xA7,0xE1,0x98,0xDC,0x6D,0x8B,0x16,0x18,0x74,0x69
                ,0x62,0x30,0xEC,0xDE,0xED,0x1E,0x22,0xEF,0x8E,0x9F,0x93,0x01,0x7B,0x8F,0x0F,0x1D,0x87,0xD0,0xD8,0x58
                ,0x94,0xA5,0xD2,0xC2,0x83,0xC9,0x03,0x00,0x9A,0x05,0x6F,0x35,0x5E,0x15,0x3D,0x26,0x40,0x04,0x4C,0x1C
                ,0xF6,0x73,0x31,0xF9,0xA1,0x4B,0xFD,0x56,0x9D,0x5D,0x4E,0xC3,0x57,0x34,0xA8,0xFC,0x27,0x88,0x75,0xB7
                ,0xB6,0xC4,0xCE,0xA2,0x4F,0xD7,0x54,0x44,0xEE,0xBC,0x53,0x7E,0x7A,0x55,0xD9,0x84,0x76,0x9E,0x14,0x72
                ,0xF7,0xF5,0x21,0x1F,0x79,0x77,0x0E,0x71,0xCD,0x3F,0x02,0x9C,0xA9,0xE2,0xF1,0x82,0x92,0x20,0xBA,0x10
                ,0x32,0xB4,0x3E,0xE5,0xF8,0x28,0x6E,0x4A,0x2B,0x86,0x6B,0xC6,0x64,0x78,0x7F,0x3C,0xE8,0x25,0x06,0x49
                ,0xD6,0x68,0x09,0x52,0x3A,0x90,0x5F,0xF2,0xCA,0x99,0xFA,0x48,0xA0,0xB3,0xBD,0x19,0x60,0xC5,0x66,0x50
                ,0xC8,0x4D,0x41,0xDF,0xF0,0x5B,0x39,0xB9,0x24,0x6A,0xEA,0xD1,0x12,0xFF,0xCC,0xBE,0xCB,0x3B,0x67,0x7C
                ,0x6C,0x45,0x80,0x96,0xE7,0x8D,0x2D,0x51,0xE3,0x9B,0xAE,0x97,0xB0,0x0D,0x17,0xBF,0x65,0x2F,0xE9,0xDB
                ,0xB8,0xAD,0x5C,0xCF,0xDA,0x59,0x85,0x11,0x13,0xE0,0xF3,0xFE,0x63,0xE6,0xE4,0xBB,0xD4,0xD5,0x1A,0x42
                ,0x47,0x8A,0x33,0xEB,0xAF,0x2E,0x0A,0x2C,0x81,0xDD,0x08,0x89,0xB1,0xB5,0x1B,0xC1),
                array(0xB4,0x72,0x76,0x66,0xCA,0xE4,0x52,0xE3,0xCE,0x2D,0x84,0x5F,0x89,0x39,0x22,0xD7,0xF3,0x5B,0xCB,0x16
                ,0x67,0x12,0x81,0xFD,0xDB,0x93,0x32,0x33,0x34,0x92,0x56,0x07,0xB0,0xA1,0x1D,0x82,0xAF,0x98,0xCD,0x05
                ,0x19,0xEC,0x63,0x6F,0xD9,0x5E,0xB9,0x0E,0x45,0xEA,0xE0,0x71,0x28,0x8C,0x8B,0xFB,0x75,0x4F,0xE9,0xAD
                ,0xBE,0x78,0xF0,0xF5,0x2A,0x2E,0x48,0xF2,0xAA,0x85,0xF9,0x9B,0x36,0x6A,0xA9,0xC5,0x0C,0x3B,0xB7,0x17
                ,0x01,0x3F,0x91,0x87,0xD1,0x41,0x30,0x03,0xFC,0x1A,0xD3,0x4B,0xBB,0x46,0x43,0xBD,0xBC,0x7D,0x65,0x27
                ,0xE8,0x20,0xEF,0x3E,0x70,0x02,0xE5,0x54,0xCF,0x1F,0x7F,0x23,0x0B,0x7B,0x37,0xB1,0x96,0xC3,0x0F,0xC8
                ,0xE1,0xD5,0x7E,0xA8,0x8E,0x4C,0x29,0xBA,0xF1,0xFA,0xF4,0xA6,0x0D,0x8A,0xC1,0xD6,0x60,0xA2,0x21,0xDD
                ,0x4E,0x6C,0xED,0x1B,0x40,0x31,0x0A,0xE7,0x06,0xAC,0x58,0x4D,0x3D,0x97,0x09,0x2C,0x55,0x11,0x13,0xDF
                ,0xCC,0x14,0x9D,0x64,0x74,0x15,0xA0,0x61,0xE2,0xEB,0x4A,0xA3,0x00,0x35,0x9C,0x77,0x1C,0xB8,0xF7,0x08
                ,0xB3,0x50,0xAB,0x1E,0x9E,0x44,0x83,0xC6,0xF8,0x7C,0x26,0x8D,0xD2,0xD4,0x99,0x73,0x8F,0x3A,0x51,0x47
                ,0x9A,0x18,0x5D,0x6E,0xC2,0xDC,0x25,0xC4,0x79,0xD0,0xA7,0xEE,0x10,0x59,0xBF,0x94,0x88,0xDE,0x49,0xB2
                ,0x38,0x5A,0xB5,0xAE,0xB6,0xDA,0x04,0x69,0xD8,0x24,0x86,0xF6,0x42,0x95,0x9F,0xE6,0x7A,0x3C,0x68,0x5C
                ,0xFE,0xA5,0x6B,0x53,0xC9,0x2B,0x57,0x80,0xFF,0xA4,0xC0,0xC7,0x62,0x2F,0x6D,0x90),
                array(0xF2,0x6F,0x18,0xD6,0xBB,0xF0,0x9C,0x7E,0xA1,0xDC,0x1A,0xAA,0x70,0xF1,0x5D,0xB3,0x81,0x92,0x6E,0x67
                ,0x5A,0x2E,0xD4,0xAC,0x55,0x41,0xBD,0xAB,0x5F,0xF3,0xFD,0xDE,0x30,0xCC,0x64,0x74,0x73,0x13,0xE1,0x75
                ,0x83,0x37,0x6B,0x1F,0xB2,0xD7,0x40,0x42,0xEB,0x86,0x7F,0xC5,0xD2,0xE0,0xBA,0xE6,0x25,0x7C,0xA2,0x72
                ,0xB9,0x36,0x08,0x4C,0x8B,0x7B,0xAF,0x52,0x8E,0x32,0x3D,0x88,0x04,0x5E,0xE9,0xF4,0x12,0x65,0xC6,0xC9
                ,0x09,0x4F,0x6C,0xEF,0x45,0x63,0xA7,0x03,0x71,0xDB,0xFA,0x51,0x2A,0x0B,0xD0,0xD8,0x90,0x69,0x01,0x1C
                ,0x9A,0x00,0x91,0x43,0x47,0x2B,0x34,0xD9,0xFE,0x17,0xD1,0x57,0xBF,0xFB,0x50,0x48,0xA0,0x3B,0xBC,0x58
                ,0x94,0x8F,0xAD,0x07,0x39,0x0D,0x89,0x19,0x99,0x14,0xB8,0x7D,0x82,0xC1,0x02,0x8C,0x62,0x23,0xCB,0x28
                ,0x2D,0x9F,0x7A,0xBE,0x85,0x0A,0x80,0x77,0xC8,0xB4,0xA6,0xC3,0x38,0x11,0x1B,0x0F,0x5B,0x0C,0x59,0xD3
                ,0xEA,0xB7,0x76,0x6D,0x79,0x3C,0x1D,0x9E,0xC0,0xCD,0xC7,0x31,0x78,0xE3,0x4E,0x4B,0x98,0x87,0x6A,0xF5
                ,0x8D,0x9D,0x96,0x44,0x84,0xDF,0x06,0xED,0xCA,0xE8,0xB0,0x46,0x68,0xB1,0xCE,0x05,0x97,0xE5,0xF6,0xDA
                ,0xB6,0x60,0x27,0xFC,0x53,0x66,0x24,0x3F,0x54,0x3A,0x22,0xC2,0x2F,0xF8,0xA8,0xA3,0xDD,0xA4,0x3E,0xFF
                ,0x4D,0x49,0xEC,0xB5,0xD5,0x93,0xE7,0x8A,0x9B,0x1E,0xAE,0xF7,0xA9,0x16,0xCF,0x33,0x20,0xE2,0x15,0x10
                ,0x5C,0x4A,0x29,0x95,0xE4,0xC4,0x0E,0x35,0xF9,0xA5,0x56,0x26,0x61,0xEE,0x21,0x2C),
                array(0x05,0x92,0x3E,0xD1,0x7C,0x53,0x6A,0x15,0xCD,0xBF,0x00,0x55,0xD4,0x71,0xE1,0x51,0x91,0x2C,0x3C,0x49
                ,0x39,0x09,0x50,0xDB,0x94,0x2D,0x99,0x12,0x9D,0xDD,0xC9,0xB5,0x47,0xCA,0x1A,0x2A,0xF6,0x69,0xF0,0xA5
                ,0x08,0xA2,0x06,0x27,0x0C,0xC4,0xA6,0x7D,0xBA,0xFD,0xFF,0x29,0x66,0x25,0x34,0x81,0x5B,0x21,0x87,0xF7
                ,0xFC,0xF8,0x18,0x9F,0xF3,0x24,0xB9,0x8A,0xDE,0x64,0xAE,0xB7,0x43,0xD9,0x0D,0x17,0x36,0x83,0xA7,0x1E
                ,0xFB,0x9B,0xA9,0x60,0x8D,0xB0,0x40,0x41,0x10,0x5C,0xA8,0xEB,0x2B,0xD5,0x07,0x1D,0x54,0xD0,0xC6,0x89
                ,0xC5,0xF5,0xC7,0xA0,0x65,0x01,0x4D,0xAA,0x79,0xDF,0xAC,0xB2,0x86,0xCF,0xE3,0x7F,0xA3,0x6F,0x38,0xB6
                ,0xA4,0xC1,0x74,0xDC,0xB3,0xA1,0x4E,0x26,0x95,0x85,0xAF,0x1B,0x9E,0x57,0x63,0xE6,0xBC,0x8B,0xB4,0x20
                ,0x35,0x37,0x6E,0xAD,0x2E,0x4B,0xE7,0x9C,0xF2,0x16,0x33,0x6D,0x8F,0x8C,0x3B,0xF9,0x62,0xCC,0xE5,0x45
                ,0x6C,0xCE,0x72,0x14,0x1C,0xD8,0xDA,0x68,0x44,0x5A,0xE0,0x97,0xE9,0x5D,0x28,0xE8,0xED,0x0F,0x6B,0x98
                ,0x9A,0xD7,0x58,0xC0,0x04,0xD2,0xBB,0x8E,0x3D,0x13,0xEA,0x76,0x2F,0x03,0x23,0xCB,0x0B,0x48,0x82,0xF4
                ,0x7A,0x11,0x46,0xC8,0x22,0xBD,0x70,0x80,0x77,0x73,0xC3,0x7B,0x88,0x5E,0xE4,0xC2,0xD3,0x93,0x56,0x61
                ,0x84,0x75,0x1F,0xF1,0x3A,0x67,0x02,0x7E,0xEC,0x5F,0x90,0x4F,0xB1,0xFE,0x4C,0xBE,0xEE,0x96,0xEF,0x30
                ,0x32,0x0A,0x4A,0x3F,0xE2,0x31,0x42,0x59,0x52,0xAB,0xD6,0x78,0xB8,0xFA,0x0E,0x19),
                array(0x13,0x87,0x77,0x31,0x6E,0x73,0x4D,0x21,0xC0,0x54,0x81,0x02,0xE1,0x5B,0xB6,0x6A,0x27,0xCE,0x30,0xBC
                ,0x25,0x64,0x56,0x52,0x80,0x37,0xDF,0x59,0x5D,0x43,0xB2,0x65,0x0E,0x3A,0x5F,0x50,0xB7,0x2A,0x9E,0x88
                ,0x0C,0x8B,0xF1,0xAB,0xFB,0xA5,0xE0,0x3D,0x48,0x01,0xBA,0x7B,0x0F,0xB8,0x72,0x98,0xFA,0x20,0x19,0xAF
                ,0xD8,0x5A,0xF5,0xAA,0xD1,0x7A,0xC3,0x09,0x4F,0x8A,0xE3,0xF4,0x47,0x3F,0xF0,0x66,0xCC,0x9D,0x8F,0x42
                ,0x5C,0xF8,0x39,0x63,0x29,0xD3,0x6C,0x45,0xEF,0xB9,0x85,0xA3,0xAE,0xFC,0x1B,0x7D,0x8C,0x14,0x00,0xBF
                ,0xE5,0x83,0x7C,0x12,0x4E,0x84,0xFE,0xA6,0xC1,0x1D,0x3E,0x05,0x1A,0xFF,0xC8,0x23,0xD0,0xCB,0xE2,0xE7
                ,0xC7,0x75,0xF9,0x95,0x5E,0xC6,0x92,0x67,0x06,0x34,0xEB,0x99,0x2F,0x32,0x60,0xA2,0xFD,0x35,0x36,0x40
                ,0x4C,0x6D,0x1F,0xC2,0xEA,0x9C,0xA0,0xBB,0x04,0xDC,0xD6,0xF3,0x0B,0x8D,0xE8,0xE4,0x97,0xC5,0xA4,0x86
                ,0x62,0x53,0x90,0x0D,0x61,0xC4,0x1E,0x94,0xCA,0x2E,0x79,0x46,0x6B,0xD4,0xD5,0xD2,0x78,0x10,0xDB,0xB3
                ,0xEC,0x07,0x96,0xB0,0xA1,0x2D,0x1C,0x2B,0x76,0x41,0x9A,0x49,0x9F,0x4A,0x74,0xF6,0x57,0x89,0x18,0xC9
                ,0xD7,0xED,0x44,0x3B,0xB4,0x51,0x0A,0xA9,0xA7,0xE9,0xCD,0x26,0xB1,0xB5,0x70,0x08,0xBE,0xDD,0x15,0xD9
                ,0xAD,0x38,0x24,0x03,0x3C,0x16,0x9B,0x2C,0x55,0x6F,0x17,0x91,0xDE,0x28,0x22,0x82,0x69,0xE6,0x7F,0xF7
                ,0xDA,0x58,0xCF,0x71,0x33,0xBD,0x68,0x4B,0x93,0x11,0x8E,0x7E,0xEE,0xAC,0xF2,0xA8),
                array(0xF2,0x13,0x88,0x0D,0xC9,0x77,0x38,0x75,0xBF,0x31,0xCC,0x74,0x6E,0x59,0x5B,0xAA,0xB4,0xAB,0x70,0x57
                ,0x91,0x1F,0xD3,0x3A,0xD4,0x21,0x28,0xEC,0xF5,0x41,0xAE,0x5E,0xC0,0xA4,0xFA,0xD7,0x3C,0xE2,0xBC,0xED
                ,0xA9,0x60,0xE6,0x3E,0xA7,0xC6,0xFC,0x94,0x35,0x58,0xCA,0xD0,0x3B,0xBB,0x16,0x10,0x8D,0xF8,0x9F,0x65
                ,0xB1,0x8F,0x2C,0xBD,0xA6,0x69,0xC5,0x1E,0xF1,0x2E,0xA1,0xC2,0x62,0xE1,0x4A,0x48,0xA0,0x45,0x18,0xDA
                ,0x86,0xDC,0x40,0xDB,0x01,0xE4,0xEA,0xAF,0x0F,0x6F,0x7C,0x4F,0xEE,0xD9,0x07,0x4C,0xE3,0xEB,0x30,0x0E
                ,0x06,0x03,0x23,0x66,0x81,0xF4,0xCB,0x84,0xB5,0x12,0x79,0xB8,0x42,0x55,0x4B,0x6A,0x02,0xC1,0x93,0xC7
                ,0xFB,0x6C,0x95,0xEF,0x78,0x9A,0xFF,0x67,0x0B,0x99,0x24,0xA2,0x20,0x37,0x50,0xAC,0x22,0x00,0x83,0x8E
                ,0x7B,0x26,0x3D,0x46,0x9E,0xAD,0x5F,0x56,0x72,0x33,0xCD,0xD6,0x52,0x1A,0x43,0xB2,0x96,0xDF,0x5C,0xCE
                ,0x76,0xD2,0xB6,0x36,0x8A,0xA8,0xC4,0x27,0x6D,0xBE,0xCF,0x25,0x19,0x2D,0xD5,0xB7,0x9C,0xFE,0xA3,0xC8
                ,0x14,0x7E,0xD1,0xE0,0x1D,0x53,0x89,0x09,0xB9,0x5A,0x5D,0x0A,0x98,0x9D,0xB0,0x11,0xDE,0x7A,0x80,0x9B
                ,0x47,0x3F,0xE5,0xFD,0x54,0xB3,0x71,0x97,0xDD,0x49,0x2B,0x64,0x63,0x92,0x8B,0x1C,0x44,0x87,0xF9,0xF7
                ,0xF0,0x05,0x2F,0x90,0xF3,0xE9,0x4E,0xBA,0x51,0x7D,0x32,0xA5,0x34,0xE7,0x39,0xF6,0xD8,0x61,0x73,0x17
                ,0x85,0x08,0x2A,0x29,0x68,0x82,0x15,0x04,0xE8,0x1B,0x6B,0x0C,0xC3,0x7F,0x4D,0x8C),
                array(0x67,0x52,0x6F,0x7B,0x85,0xC8,0xA2,0x45,0xF1,0x6C,0x0B,0x5B,0x23,0xDA,0xB0,0x26,0x94,0xEC,0x7D,0x7A
                ,0xBA,0x19,0xD8,0xFC,0x97,0x1E,0x83,0x10,0x09,0x93,0xB2,0xC3,0xD1,0x17,0x9C,0xAB,0xC4,0x3C,0x53,0x0A
                ,0x47,0x3B,0x06,0x2E,0x37,0x5F,0xD3,0x8E,0x49,0x22,0x69,0x4C,0x41,0x86,0xB7,0x73,0x33,0xF8,0xCB,0xE9
                ,0xA1,0x68,0x5C,0xC1,0xF5,0x8C,0x35,0x95,0x96,0x42,0x11,0x9F,0xA6,0xF3,0x14,0x91,0x6A,0x29,0x30,0x9E
                ,0x9B,0xF6,0x5A,0xAF,0xE0,0x24,0x0E,0x7C,0x03,0xE3,0x2F,0xD0,0x8F,0x57,0x66,0x58,0xB6,0x1C,0xA8,0x5D
                ,0x1B,0x77,0xBC,0x15,0x7E,0xDF,0xBF,0x04,0x84,0x2A,0x3F,0xB1,0x3A,0x12,0xFE,0xD9,0x2B,0xEF,0x44,0xCF
                ,0xC7,0x79,0xAA,0x81,0xA9,0x3D,0x25,0xB4,0xDD,0x74,0x4B,0x54,0xD4,0x27,0x2D,0x80,0x4D,0xAD,0xA3,0x63
                ,0x20,0xFA,0x38,0x7F,0xBD,0xDC,0x87,0x51,0xB8,0x48,0xD5,0x56,0x9D,0xDE,0x1F,0x4E,0x60,0xF4,0x75,0x1D
                ,0x6E,0x59,0xC6,0xEE,0xD2,0x72,0x82,0x78,0x21,0x0F,0x02,0x39,0xC9,0x31,0x8B,0x4F,0xA5,0x40,0xAE,0xE2
                ,0x50,0xF7,0xE8,0x89,0x6D,0x00,0x36,0xCD,0x61,0xF0,0xF9,0xB9,0xC2,0x9A,0x28,0xC5,0x34,0x46,0xD7,0xE6
                ,0x4A,0xD6,0xBB,0xFB,0x92,0xCC,0x0C,0x76,0x8A,0x07,0xBE,0xE1,0x98,0x05,0xB5,0x18,0x16,0x32,0xA0,0xFF
                ,0xEA,0xAC,0xCE,0x88,0x65,0x71,0x13,0x64,0x90,0x43,0x6B,0xEB,0xF2,0x1A,0x8D,0xFD,0xDB,0xED,0xE4,0x08
                ,0x2C,0xE7,0xCA,0x62,0x0D,0xA4,0xC0,0xA7,0x99,0x55,0xE5,0xB3,0x3E,0x5E,0x70,0x01),
                array(0xB1,0xA3,0xE7,0x2E,0xE2,0xC6,0x63,0x5A,0x8D,0x29,0xB4,0x15,0xC9,0xDA,0x0B,0xF7,0xAE,0x72,0x3C,0xC1
                ,0xF8,0x8A,0x1D,0x05,0xBE,0x92,0x14,0xEF,0xBA,0x67,0x09,0xCB,0x82,0x91,0xDE,0x83,0x20,0x40,0x28,0x62
                ,0xE5,0x52,0xBB,0xD7,0x5B,0xCF,0xCD,0x27,0xD1,0x76,0x73,0x2D,0xA5,0x08,0x9D,0x13,0x22,0xFD,0x42,0x58
                ,0x89,0xE3,0xAB,0xE8,0xA0,0xEE,0x65,0x35,0x81,0x11,0x6B,0x8F,0xD9,0xC4,0x1B,0xD2,0x95,0xF4,0x8C,0x25
                ,0xB3,0x75,0x2F,0x69,0x18,0xA4,0xC2,0xDB,0xED,0x01,0x68,0x0E,0xB0,0x02,0x4F,0xB6,0x6C,0xDF,0x9A,0xB8
                ,0x10,0x26,0x3B,0x66,0xA1,0xD6,0x51,0xC8,0x9E,0x3A,0xAD,0x32,0x5E,0xEA,0x56,0x93,0xF1,0x94,0x5D,0xD5
                ,0x04,0x0C,0x7D,0x9B,0x4C,0x59,0x49,0x06,0x5C,0xF6,0xFB,0xD8,0xB2,0xE9,0x1E,0xFF,0xBF,0x0D,0xF0,0x23
                ,0x1A,0x9C,0xA7,0xF9,0x24,0xA8,0x0F,0x07,0xD3,0x55,0xAC,0x31,0xCE,0xB7,0x98,0x57,0x7C,0x3E,0xEC,0xF5
                ,0xC3,0x4E,0x80,0x30,0x21,0xC7,0x85,0x8E,0x53,0x6D,0x7F,0xE0,0x6F,0xF3,0x6A,0x5F,0x8B,0xE1,0xCC,0x4A
                ,0x1C,0x33,0xFA,0x17,0xC5,0x03,0x77,0xFE,0x34,0x1F,0x46,0xAF,0x79,0x74,0x50,0x97,0x90,0x44,0x19,0xC0
                ,0xCA,0xA6,0x70,0x9F,0x71,0xFC,0x2A,0x2B,0xA9,0x39,0x00,0xA2,0xBD,0x54,0x96,0x4B,0x64,0x0A,0x3D,0x7B
                ,0xD4,0x7E,0x47,0xE4,0x7A,0xDD,0x88,0xB5,0x78,0x41,0xF2,0x99,0x87,0x38,0x84,0x61,0xB9,0xEB,0x2C,0x6E
                ,0x37,0x60,0x86,0x48,0x36,0xD0,0xBC,0x3F,0x12,0x16,0x4D,0x43,0xAA,0x45,0xDC,0xE6),
                array(0xD8,0x29,0xB8,0x38,0xC2,0x17,0x0B,0x55,0xEC,0x48,0x44,0x37,0x49,0x81,0xB1,0xB2,0x23,0x4F,0x24,0xAC
                ,0x50,0x85,0xDE,0x64,0xCC,0x33,0x80,0xFA,0xC9,0x5F,0xCD,0x66,0xC3,0x36,0x3F,0xCF,0x93,0xA2,0xF0,0x5C
                ,0xCA,0xDD,0x27,0x35,0x78,0x45,0x46,0x69,0x2F,0x3C,0x07,0x41,0x59,0x51,0x9A,0x71,0xF6,0x04,0x40,0x39
                ,0x19,0xFF,0x98,0xA6,0x74,0x6F,0xEA,0x8E,0x72,0x8A,0x01,0x7D,0x2A,0xB0,0xA3,0x09,0xB5,0x6D,0xAF,0x7F
                ,0xD7,0x58,0xE5,0x4B,0xE2,0xEB,0x13,0xBE,0x8D,0x4A,0xA8,0x7E,0x90,0x26,0xD4,0xFE,0xA9,0x65,0xD5,0x3B
                ,0x96,0xBC,0xC7,0x28,0x0D,0xBD,0x1B,0xAA,0x2C,0x9F,0xC1,0xDC,0x54,0x08,0x89,0xF2,0x88,0xFC,0x84,0xF7
                ,0xD9,0x6B,0xAE,0xE9,0x9C,0xCE,0xE7,0x5D,0x99,0x86,0x1A,0x0A,0x95,0x8B,0x67,0xA7,0x1C,0xAD,0x87,0x3D
                ,0x42,0x7A,0x60,0x6A,0x53,0x73,0xD2,0x05,0xCB,0x56,0xA0,0xE3,0x9D,0x7B,0x18,0xC5,0x1F,0x97,0xA4,0x06
                ,0xAB,0x34,0xC0,0xD0,0xF1,0xC8,0x4C,0xBA,0xE1,0x2D,0x43,0x5A,0x4E,0xE8,0xEF,0x77,0x30,0xF8,0x68,0xE0
                ,0x21,0xFB,0xF9,0x9E,0xE4,0x2B,0x63,0x75,0x7C,0x11,0x8C,0x61,0xC6,0x16,0x62,0x5E,0xD1,0xD3,0xD6,0x03
                ,0x0C,0x3A,0xF3,0x94,0x52,0xFD,0x1D,0x10,0xB7,0x1E,0xA1,0x31,0x5B,0xDA,0x4D,0xB4,0xBB,0x12,0x14,0x76
                ,0x3E,0x02,0x32,0x6C,0x47,0xC4,0x57,0xE6,0xDF,0x83,0x15,0xB3,0x9B,0x22,0xBF,0x8F,0x79,0x25,0x2E,0xB9
                ,0x00,0xA5,0xF5,0x82,0x70,0x92,0x20,0x91,0xDB,0xEE,0xB6,0xED,0xF4,0x6E,0x0E,0x0F),
                array(0x64,0x3D,0xE3,0xEF,0xAE,0xA6,0xD4,0x46,0x43,0x82,0x91,0x8B,0x53,0x19,0x65,0x0D,0x54,0x6D,0x71,0x74
                ,0x3F,0x87,0x8D,0x17,0x94,0x9E,0x23,0xE7,0x0E,0x31,0x72,0x5D,0xFA,0x39,0xEC,0xFB,0x11,0xE8,0x70,0x56
                ,0x90,0x40,0xE5,0xF3,0xF7,0xB2,0x6C,0x5E,0x4B,0xB4,0xD5,0xD3,0xC2,0xC5,0xA5,0xDC,0x7F,0xD7,0xAB,0xA7
                ,0x73,0x69,0xC8,0x4D,0x61,0xCD,0x58,0xE4,0x5A,0x78,0x0A,0xC6,0x97,0x9A,0x02,0x29,0xAD,0xBA,0xA2,0x77
                ,0xBC,0x4F,0x3B,0x57,0xDE,0xBD,0xC0,0x88,0xEB,0xB5,0x47,0xB8,0x14,0x0C,0xCB,0xDA,0x18,0x12,0x95,0x44
                ,0xFD,0x45,0xF1,0x2F,0x93,0xC4,0xDF,0xD8,0xF9,0x38,0x2D,0x76,0xE9,0x1F,0xA9,0x8F,0x16,0x37,0xCC,0xBF
                ,0x1E,0x0F,0x21,0xF0,0x28,0x3E,0xA3,0x9B,0xD9,0x7E,0x67,0x5C,0x9C,0xAA,0x1D,0x5B,0x20,0xE6,0xFF,0x9D
                ,0x03,0x01,0x27,0x1B,0x86,0xB0,0xC1,0x99,0x62,0x15,0x92,0xF5,0x60,0x68,0x52,0x83,0x48,0x6A,0x2C,0x0B
                ,0xB6,0x3C,0x63,0x06,0x22,0xB3,0x51,0xD2,0x85,0xB1,0xBE,0x36,0x42,0xE2,0x33,0xDB,0xB7,0x9F,0x4A,0xE0
                ,0xBB,0xD6,0x3A,0x30,0xED,0x08,0x8C,0xCA,0x7C,0xC3,0xF8,0x09,0x2E,0x1C,0x1A,0xFC,0x34,0xA0,0x98,0x35
                ,0x41,0x10,0x84,0xDD,0xF4,0x81,0x8E,0x4E,0xA8,0x59,0xEE,0xF6,0x2B,0x2A,0xA4,0x24,0x6E,0x7D,0x25,0x75
                ,0xAF,0xA1,0x80,0xCE,0x79,0x4C,0xB9,0x55,0xEA,0x66,0x6B,0xC7,0x13,0xE1,0x32,0x04,0x26,0x96,0xFE,0x5F
                ,0x49,0xF2,0x50,0x00,0x05,0x7A,0x7B,0x8A,0x89,0xC9,0x07,0xCF,0xD0,0xD1,0xAC,0x6F),
                array(0x79,0xEC,0x6E,0x5A,0xF5,0x27,0xB6,0xBF,0xC7,0xA5,0x7A,0x56,0xF1,0x09,0x3C,0x10,0x3B,0x49,0xEE,0x4F
                ,0x57,0x1B,0xC3,0x28,0xE1,0x22,0x58,0xE9,0xF8,0x03,0x37,0xB2,0xCA,0x1C,0xE3,0x2B,0x2A,0x62,0x05,0xDF
                ,0x7B,0x2E,0x0D,0x29,0x16,0x5C,0x85,0xC0,0x65,0x4A,0x70,0xBA,0x6C,0x92,0x6B,0xC4,0x2C,0x86,0xF4,0x7F
                ,0x33,0xFC,0xB9,0x97,0x84,0x0B,0xE8,0x08,0xCF,0xA3,0xD3,0xDB,0x0C,0x5B,0x9E,0x91,0xC9,0x63,0xA2,0xF2
                ,0x20,0x0F,0xBC,0x80,0xE6,0x96,0x02,0xEF,0xD9,0x3D,0xBD,0xB1,0x90,0x42,0x18,0x69,0x4C,0x3F,0xB8,0xC1
                ,0xC8,0xDC,0x88,0x2D,0xCD,0x01,0x4D,0xCC,0x8D,0xA0,0x39,0xD6,0xE5,0x76,0xE2,0x47,0x87,0x68,0x8C,0xAD
                ,0x3A,0x12,0xD7,0xFB,0x9C,0x30,0x00,0xFF,0x9D,0x9F,0xD4,0xB4,0x45,0x43,0xFE,0xE7,0x73,0x44,0xDA,0x8A
                ,0x32,0x17,0xD5,0xF3,0x78,0xA4,0x48,0x40,0xCB,0x94,0x8E,0x8F,0x07,0xD8,0x93,0xD2,0x38,0x77,0x75,0x9B
                ,0x21,0x5E,0xAA,0xAF,0xAB,0x25,0xA7,0x60,0x5D,0x23,0xED,0x50,0xF6,0xBB,0x5F,0x46,0xF7,0xFD,0x04,0x64
                ,0xAE,0x0E,0x34,0xEB,0xDE,0x4B,0xE4,0xB0,0x83,0x51,0xA6,0x4E,0x41,0x67,0x81,0x6D,0x13,0x6F,0x0A,0x35
                ,0x26,0x19,0x82,0xCE,0x1A,0xB5,0x66,0x1E,0xB7,0xAC,0x24,0xE0,0xF0,0x1D,0xC6,0x52,0xA1,0x11,0xB3,0x89
                ,0x14,0xEA,0xC2,0xDD,0xFA,0x71,0x61,0x72,0x7D,0x59,0x15,0x9A,0xA9,0x3E,0x2F,0x7E,0x55,0xD0,0x1F,0x99
                ,0x06,0xF9,0x7C,0x53,0x36,0x54,0x95,0x98,0xC5,0xA8,0x6A,0x31,0x8B,0xD1,0x74,0xBE),
                array(0x1F,0xFD,0xDE,0x57,0xF4,0x56,0x7C,0xB0,0x14,0xEB,0xCC,0x76,0xB4,0x38,0x93,0x29,0x39,0xF9,0x74,0x36
                ,0x33,0x37,0x67,0x35,0x0F,0x6D,0xE1,0x40,0x4D,0x42,0x64,0xE8,0xC8,0x83,0x5A,0xEC,0x86,0x17,0xCD,0x77
                ,0xC0,0x44,0x8A,0x69,0x5E,0x8C,0xE6,0xFF,0xDD,0x7F,0xB3,0xED,0x92,0x9B,0xE9,0x21,0xFA,0x2D,0xA0,0xEF
                ,0xBE,0xD1,0x26,0xA3,0x46,0x30,0x61,0x94,0xFB,0x5B,0xB2,0x73,0xAA,0xAB,0x16,0xBC,0xAD,0xF7,0x6E,0xC9
                ,0xB6,0x1C,0x6B,0x90,0x9A,0xB7,0xD3,0x71,0xA7,0xAE,0x8E,0xE3,0x0A,0x7E,0x7A,0x6F,0x87,0x6C,0x72,0x1B
                ,0xA5,0x20,0x2A,0x65,0xD4,0x24,0x59,0x68,0x8D,0x80,0x95,0x7B,0xFC,0xDB,0xE4,0x3E,0x07,0x43,0x18,0x27
                ,0xC1,0x41,0xCF,0xF8,0x3B,0x9E,0x23,0x32,0xF3,0x0D,0xC2,0xE7,0x47,0x7D,0x3A,0xC6,0x8F,0xF2,0x60,0x0B
                ,0x01,0xD7,0x4A,0xC7,0x34,0x31,0x78,0x5D,0xAC,0x75,0x82,0x9D,0xBD,0xD5,0x06,0xD2,0x58,0xDA,0x2B,0xCA
                ,0xD9,0x97,0x4B,0xBB,0x49,0x10,0x85,0xDF,0x51,0x08,0x3D,0x81,0x84,0x48,0xAF,0x09,0x1D,0xF5,0xB5,0x5C
                ,0x05,0xF6,0x50,0x00,0x70,0xA4,0x4F,0xCB,0x4E,0xCE,0x2E,0x91,0xC5,0x15,0xE0,0xC4,0x62,0x11,0x52,0x53
                ,0xBF,0x4C,0x0E,0xE5,0x12,0xA9,0xEA,0x22,0xFE,0x63,0x25,0xB9,0x6A,0xDC,0xEE,0x13,0x28,0x66,0x54,0xF0
                ,0x1A,0x9F,0xE2,0x55,0x02,0xBA,0x5F,0xB1,0xA6,0x98,0x8B,0x79,0x2F,0x1E,0x2C,0x99,0xC3,0x03,0xB8,0xA8
                ,0x04,0xA1,0x9C,0x3F,0x19,0xD0,0xF1,0x3C,0x45,0xA2,0x0C,0x96,0xD8,0x88,0x89,0xD6),
                array(0x32,0x80,0x59,0xC5,0x51,0xAB,0xC8,0x35,0xE4,0x9B,0x73,0x8B,0x90,0x3F,0x5E,0x81,0xE7,0x0A,0x6A,0xB9
                ,0xE5,0x37,0xE9,0x92,0xA3,0x3E,0x09,0xF9,0xC3,0x28,0xAE,0x9A,0x17,0x77,0x21,0x27,0x44,0xB0,0x39,0xBD
                ,0x75,0x8A,0x5D,0xD9,0xDF,0x0E,0x8C,0xFC,0x5A,0xBE,0x46,0x99,0xE0,0x70,0xB8,0x58,0x13,0x16,0x4F,0xF2
                ,0x68,0x05,0xDB,0xBC,0x84,0x8E,0xD7,0x78,0x2B,0x7C,0x48,0xC2,0xF7,0x55,0x79,0xC9,0x6B,0xCA,0xDA,0xF8
                ,0xA4,0x4B,0x0C,0xFD,0xE8,0x71,0x1E,0xD3,0xA6,0x86,0x1A,0xC1,0x5B,0x19,0x54,0x96,0x18,0xE2,0xCB,0x93
                ,0x72,0xB2,0x26,0x65,0xBF,0x30,0x08,0x33,0x7D,0xDD,0x47,0xB3,0xEE,0xD4,0xCF,0x5C,0xEC,0x6F,0xEB,0x3B
                ,0xC6,0xA1,0x24,0xE1,0xED,0x01,0x4E,0xF3,0x97,0x2E,0x56,0x63,0xCE,0x57,0xA9,0x89,0xFE,0xE3,0xAD,0xE6
                ,0x7F,0x10,0xD8,0x82,0x50,0x03,0x38,0x64,0x41,0x14,0xBA,0xCD,0xB6,0xEA,0xD1,0xF1,0x4D,0x83,0x7E,0x6D
                ,0x2D,0x25,0x53,0xF4,0x66,0xA8,0x76,0x1C,0x8D,0x94,0xFA,0xEF,0x69,0x9E,0xAF,0x95,0xC4,0xB7,0xBB,0x42
                ,0xF6,0x6C,0x23,0x0F,0x04,0x5F,0xD2,0x15,0xB1,0x91,0xD5,0xFB,0x34,0xDC,0x2C,0xD0,0x6E,0xB5,0xA2,0x31
                ,0x2A,0x0D,0x1F,0x61,0x3D,0x85,0x60,0xA0,0x88,0x9F,0xF0,0x29,0xC0,0x8F,0x7B,0x87,0x40,0xC7,0x9C,0x62
                ,0x00,0xA7,0x20,0xDE,0x1D,0x9D,0x07,0xAC,0x98,0x02,0x7A,0x67,0xAA,0x74,0x4C,0x45,0x36,0x06,0xFF,0xB4
                ,0x22,0x3A,0x52,0x4A,0x3C,0xF5,0x11,0x49,0x0B,0xCC,0x2F,0x43,0x12,0x1B,0xA5,0xD6),
                array(0xDE,0xD9,0xEA,0x69,0xCA,0xE3,0x39,0x17,0x1E,0xD2,0xBD,0x32,0x84,0x5A,0x4F,0x0C,0x0A,0xDB,0xC8,0x06
                ,0xED,0x01,0x36,0x12,0xA7,0xF1,0x7C,0xE7,0xA3,0xA6,0xAA,0xEE,0x79,0xA5,0xCE,0x99,0x44,0x37,0x74,0xAD
                ,0xC2,0x98,0x0B,0x3B,0x86,0x8E,0xAE,0xE0,0xF7,0x3E,0x21,0xD8,0xCC,0xBF,0x0E,0x5F,0x26,0x46,0x13,0xA2
                ,0x10,0xAF,0x90,0x83,0xB4,0x78,0x11,0xE2,0xDF,0xD3,0x54,0xCD,0x58,0x9F,0x8C,0x67,0xBE,0x40,0x09,0x66
                ,0xD1,0x5E,0xE5,0x73,0x93,0x1B,0x16,0x57,0x76,0x38,0xF4,0xAB,0x9E,0x89,0x51,0x72,0x47,0xB1,0x77,0x97
                ,0x5B,0x9A,0xC7,0x85,0xDC,0x04,0x25,0x75,0x1F,0xFD,0x55,0x4D,0xFA,0x5C,0x3D,0x59,0x7B,0x50,0x0F,0xB9
                ,0x68,0x22,0x8B,0xC4,0xB2,0x43,0x95,0x18,0xC9,0x2E,0x87,0x4B,0x3A,0x1A,0xF8,0x7E,0xFC,0xA1,0xDA,0x2D
                ,0x9D,0x6E,0xB0,0xAC,0x3C,0xBA,0xC1,0xEC,0x41,0x20,0xFF,0x8A,0xB6,0x2B,0xA8,0x1D,0x64,0x8D,0xD6,0x29
                ,0xE4,0x49,0x28,0x23,0x08,0x7A,0x4E,0xE6,0xB7,0x61,0x6C,0x2F,0x00,0x5D,0xDD,0x31,0x02,0x88,0xE8,0x45
                ,0x70,0x91,0x42,0x35,0x24,0xF2,0x92,0x15,0xEF,0x94,0xE1,0x34,0x63,0x14,0x07,0x62,0x56,0x8F,0x19,0x7F
                ,0x65,0x4C,0x33,0xFB,0xCF,0xE9,0x81,0x4A,0x0D,0x3F,0x80,0x53,0x82,0xB3,0x96,0x60,0xEB,0xD0,0xA4,0x9C
                ,0xC0,0xB8,0xBB,0x7D,0xC5,0xC6,0xA9,0xF9,0x1C,0xC3,0xBC,0xFE,0xF3,0x6F,0x52,0x27,0x6D,0x71,0x48,0xB5
                ,0x05,0x6A,0xA0,0xF0,0xD5,0x2C,0xF6,0x6B,0xD4,0x30,0xF5,0x2A,0x03,0xCB,0x9B,0xD7),
                array(0xAD,0xA9,0x19,0xE8,0x8B,0x36,0xC0,0x05,0x09,0xC1,0xBA,0x17,0x6F,0x2D,0x7F,0xD0,0x37,0xCD,0xE1,0x0D
                ,0x9E,0x9B,0x25,0x6D,0x1E,0xAB,0xA8,0xBF,0x55,0x14,0x56,0x69,0x29,0x79,0x62,0x23,0x06,0xBC,0x2A,0xF8
                ,0x96,0x78,0xCF,0x2F,0x08,0xC4,0xF2,0x8D,0xC8,0xA1,0x41,0x94,0x92,0xC5,0x00,0x88,0x28,0xF6,0x8F,0x5C
                ,0x27,0x5D,0xB1,0xE9,0xA2,0xF3,0xE7,0x01,0x1C,0xDF,0xBB,0x75,0xDD,0x22,0x7E,0xFB,0xB8,0x8E,0x32,0xB3
                ,0x66,0x4B,0xD9,0xEE,0x65,0x2C,0x4D,0x9F,0x86,0xF5,0xA0,0xED,0xD8,0x0C,0x73,0xF1,0x33,0x1F,0xD3,0xD2
                ,0x91,0x3F,0x21,0x67,0x99,0x7A,0x51,0x9C,0x44,0x4E,0xDE,0x9A,0xE2,0x57,0x42,0x50,0xA4,0xCE,0x53,0x07
                ,0x3E,0x4F,0x48,0x46,0xB2,0x4A,0xC7,0xB6,0x2E,0x89,0x43,0xC9,0x93,0x59,0xEA,0x52,0x95,0xFA,0x7B,0x10
                ,0xA3,0xF7,0xBD,0xB0,0x74,0x12,0x0E,0xE0,0x8C,0x18,0x1B,0x82,0x5F,0xD4,0x49,0xE3,0xAF,0xEB,0x38,0xB7
                ,0xF4,0xCB,0x1D,0x30,0x6E,0x0B,0x3C,0x35,0xAE,0x64,0xFE,0x8A,0x20,0x71,0xAC,0xEC,0xAA,0x34,0xB9,0x0F
                ,0x87,0x6C,0x80,0x9D,0x45,0xFC,0xFD,0x61,0x15,0x5E,0x3A,0x68,0x03,0x77,0x7C,0x26,0x6A,0x70,0xCC,0x81
                ,0xF0,0x60,0x4C,0x5B,0x11,0xC2,0xF9,0xA5,0xD7,0x90,0x97,0xDA,0x16,0x1A,0x13,0xD1,0xE6,0x2B,0xEF,0x04
                ,0xD5,0xA7,0xB4,0x47,0x0A,0xD6,0xCA,0xB5,0xDB,0x54,0x6B,0x02,0xE5,0xC6,0x7D,0x3B,0x83,0xC3,0xA6,0x40
                ,0x58,0x5A,0x39,0x63,0xE4,0x31,0xBE,0x24,0x76,0x85,0x84,0x72,0xFF,0x98,0xDC,0x3D),
                array(0x78,0x8A,0x23,0xF5,0x86,0x55,0xDA,0x28,0x8D,0x58,0x42,0xB3,0xB4,0xFE,0x9A,0x24,0xE6,0x15,0xB2,0x1E
                ,0x50,0xF6,0x16,0xC8,0x8E,0x67,0x90,0x40,0xF8,0x70,0xF9,0x32,0x14,0x81,0xFD,0xB7,0x9D,0x83,0x49,0x5E
                ,0xC4,0x4C,0x47,0x31,0x6D,0xCD,0xC1,0x5C,0xB5,0xB1,0x06,0xA3,0xFC,0x97,0x7D,0xD7,0x57,0x11,0x64,0xA0
                ,0x2D,0xCE,0x6F,0xD9,0x17,0x79,0x6B,0x1D,0x0F,0xA7,0x96,0x66,0x01,0xDC,0xBF,0xB0,0xCA,0xDD,0x2E,0x39
                ,0x18,0xA5,0xA1,0x62,0x7C,0x6E,0x30,0xD8,0xE8,0x82,0x1C,0xF2,0xB9,0x8C,0xFF,0x63,0xEE,0x1B,0x2B,0x0A
                ,0x29,0xC3,0xD6,0xC9,0x7F,0x0D,0x76,0x6A,0x89,0xBA,0xBD,0x65,0x05,0x04,0xC5,0x45,0xE9,0x59,0x5F,0x10
                ,0xF7,0x68,0xBE,0x75,0x2F,0x21,0x4D,0xDB,0x87,0x4F,0x34,0x43,0x51,0x0B,0xD5,0xD0,0xD3,0x36,0x3C,0x3B
                ,0xC7,0xE4,0xAA,0x4B,0xD1,0xFA,0x77,0xF1,0xDE,0x12,0x94,0x92,0x7A,0xFB,0xE2,0xCB,0x9F,0xE3,0xC2,0xEA
                ,0x7E,0x8F,0x98,0x00,0x26,0xA8,0xDF,0x99,0x20,0x72,0x4E,0x22,0x08,0xBC,0x5D,0xF0,0x8B,0x46,0xC0,0x35
                ,0xB8,0x95,0x02,0x4A,0x09,0xD4,0xF3,0xA4,0x74,0x6C,0xE7,0x3E,0x56,0x07,0x73,0x52,0xA6,0x27,0x93,0xED
                ,0xA9,0xD2,0x88,0xB6,0x48,0x25,0x54,0xEB,0x5B,0xA2,0xAF,0xE5,0x2A,0x41,0x2C,0xAC,0xF4,0xCF,0x69,0x3D
                ,0x9E,0x1F,0x37,0x9C,0x53,0x03,0x7B,0x91,0x5A,0xCC,0x1A,0x3A,0xAE,0x0E,0xAD,0x60,0xC6,0x80,0xBB,0x33
                ,0x9B,0x3F,0x71,0x0C,0x13,0x84,0x85,0x61,0x44,0xE0,0xAB,0xE1,0x38,0xEC,0xEF,0x19),
                array(0x35,0x40,0x90,0xA3,0x5B,0x63,0x80,0x98,0x0F,0xEF,0xE1,0x7C,0x5A,0x97,0xBB,0x94,0xFA,0xC2,0x56,0xF7
                ,0xC1,0xFC,0x32,0xE5,0x01,0xCF,0xA4,0xDB,0x75,0x89,0x29,0x14,0x77,0x36,0x0E,0xD0,0xD2,0x3F,0xAE,0x5F
                ,0x85,0x16,0x51,0xB9,0x3C,0x39,0xF6,0x61,0x8A,0x10,0x42,0x82,0xEB,0xF3,0x7A,0x68,0xC6,0xE9,0x64,0x84
                ,0xB8,0x49,0x5E,0x76,0x9D,0x34,0x1B,0x1C,0x72,0xDD,0x6C,0x3A,0xBD,0x5D,0xF4,0xA5,0x21,0x22,0xE2,0x7D
                ,0xE4,0xA9,0xBF,0x4C,0x03,0x73,0xBE,0xB6,0x52,0x4D,0xCE,0xC7,0xA8,0x99,0xF8,0x55,0x0C,0x3D,0x45,0xC4
                ,0xB1,0x78,0x6A,0x1D,0xD3,0x3B,0x9C,0x12,0x4F,0xAC,0x58,0x66,0xFF,0xD4,0x46,0xF2,0xD1,0x8F,0x6F,0x9F
                ,0xF9,0x30,0xCB,0x91,0x1F,0xB0,0x4E,0x71,0xFB,0x31,0xB7,0xDF,0xEE,0xDE,0xE8,0xC8,0x70,0x0B,0x79,0x7E
                ,0x00,0x11,0x04,0x17,0xA0,0xE0,0x25,0x47,0x02,0xBA,0x26,0x67,0x18,0xE6,0x23,0xE3,0x2A,0x24,0x07,0x9B
                ,0x8D,0x28,0x2C,0x69,0x2B,0xF5,0x59,0x57,0x83,0xCC,0xAA,0xD8,0x81,0x27,0x87,0x08,0x0A,0x15,0xA6,0xB3
                ,0x9A,0x50,0x7B,0x6E,0x0D,0xA1,0xA7,0xB5,0xAB,0x1A,0xD5,0x54,0xCD,0x95,0x44,0x20,0xC0,0x38,0x06,0xD6
                ,0x9E,0xAD,0x62,0xFE,0xA2,0x2D,0x53,0x88,0x33,0x4B,0x8E,0xB2,0xD7,0xC5,0xF1,0x6D,0x1E,0xC3,0x43,0x93
                ,0x6B,0xD9,0xC9,0x8C,0x8B,0x74,0x19,0x4A,0x5C,0xCA,0x3E,0x60,0xDC,0x7F,0x41,0x2F,0xEC,0xED,0xAF,0x48
                ,0x37,0x13,0xEA,0xFD,0x65,0xBC,0x92,0x96,0x86,0x05,0x09,0xDA,0xF0,0xE7,0x2E,0xB4),
                array(0x79,0xB1,0xD2,0x7A,0xE8,0x74,0x4C,0x97,0xAE,0xEA,0x64,0x28,0x23,0xCA,0x4A,0xA2,0x9E,0x61,0x2C,0xA7
                ,0x72,0x0E,0x27,0xA8,0x25,0x58,0x80,0xD1,0x44,0x49,0xD0,0xAD,0x71,0x50,0x9B,0x05,0xA5,0xFC,0xCE,0xB3
                ,0xEB,0xED,0x94,0x5D,0xEF,0x26,0x45,0x6E,0x21,0x1F,0xDC,0x33,0xC9,0xA0,0x3E,0xFA,0x4E,0x3D,0xBD,0x10
                ,0x22,0x3B,0x43,0x8B,0xA3,0xF8,0x73,0x35,0xDE,0xC8,0x48,0x60,0xE2,0xB5,0x9D,0x1B,0x38,0x2E,0xAB,0xD8
                ,0xF1,0xF5,0x78,0xE6,0x31,0xA1,0xBE,0xE1,0xBF,0x4B,0xD9,0xE7,0x3F,0xE9,0x2D,0x8F,0x09,0x0F,0xAF,0x56
                ,0x85,0x40,0x12,0xAC,0x3C,0x5F,0xB7,0xF4,0xC2,0x19,0x08,0x75,0xB2,0xC0,0x04,0x7F,0xC4,0xFD,0xBB,0xCB
                ,0x4F,0x67,0xD3,0xBC,0x52,0x88,0x1D,0x5E,0xF9,0xA4,0x76,0x7C,0xC1,0xCF,0xF7,0x8A,0xE3,0x96,0x63,0xF2
                ,0xF6,0x14,0x13,0x69,0x7D,0x6D,0xF3,0xDB,0x24,0x83,0x37,0x55,0xDD,0xCC,0x6A,0x47,0x7E,0xFE,0x6B,0x68
                ,0x6C,0xC6,0x2B,0x54,0x87,0x81,0xAA,0xDF,0x17,0x6F,0x42,0x70,0xA6,0x41,0x92,0x0A,0x01,0x5B,0x2A,0x8C
                ,0x51,0xE4,0x11,0x93,0x9F,0x9C,0xF0,0x84,0x2F,0x66,0xFF,0x02,0x34,0x3A,0xD7,0x1C,0xB9,0x82,0x59,0xFB
                ,0x36,0x0C,0xA9,0xEC,0x1E,0xD5,0x1A,0xBA,0x0D,0x29,0x62,0x4D,0xE0,0x65,0x7B,0xEE,0x89,0xD6,0x00,0x91
                ,0x77,0x5A,0xC7,0xCD,0x8E,0x39,0x15,0x57,0xB6,0x53,0xE5,0x03,0x30,0x9A,0xDA,0x99,0x90,0x86,0xB4,0x32
                ,0x18,0x98,0xC3,0xB8,0x46,0xC5,0x8D,0x20,0x06,0x95,0x5C,0x07,0x0B,0xB0,0x16,0xD4),
                array(0x75,0xFE,0xE9,0xE4,0x98,0x9D,0x7D,0xCC,0x19,0xF1,0x33,0x61,0xDE,0x49,0x4D,0xB5,0x22,0x39,0x0D,0xE0
                ,0x41,0xDC,0x3D,0x3E,0x73,0xEE,0x1E,0xA9,0x2C,0xDA,0xB8,0x28,0xED,0x97,0x83,0x72,0x87,0xA6,0x0E,0x40
                ,0x3A,0x66,0xEC,0x4A,0x90,0xEF,0x00,0xB0,0xE8,0x34,0x6F,0xF8,0xE7,0xFB,0x07,0xC8,0x92,0x7F,0xB6,0x23
                ,0xD2,0xDB,0x56,0x53,0xB9,0xD8,0x63,0x51,0xD5,0xF2,0x46,0x8F,0xF6,0x2E,0xD0,0xFC,0x6C,0xAE,0xE5,0xC3
                ,0x58,0x48,0x20,0x0B,0x96,0xA5,0x54,0x06,0x86,0xC0,0x2B,0xA0,0x84,0xD7,0xAD,0xE3,0xEB,0xBE,0x9F,0xF3
                ,0x30,0x35,0x78,0xCB,0xCD,0x4C,0x1C,0x17,0xD9,0x8C,0x7A,0x55,0xC7,0x32,0x16,0xBA,0x36,0xD6,0x45,0x9A
                ,0x24,0xF4,0xC9,0x77,0xBD,0xC5,0xAC,0x81,0xE1,0x29,0x25,0x0A,0x5F,0x7C,0x9E,0x43,0x13,0x47,0xE2,0xD1
                ,0x44,0x76,0x0C,0xB1,0x09,0x7E,0xA4,0x5B,0x57,0xB2,0x4F,0x5C,0x01,0xD3,0xE6,0x8E,0xC1,0xAF,0xBF,0x10
                ,0xF7,0x6A,0x3F,0x62,0xB4,0x12,0xB3,0x99,0x80,0xFA,0x27,0xF5,0xA2,0xBB,0xD4,0x91,0xEA,0x59,0x6D,0x38
                ,0x9B,0x68,0xA3,0x08,0x2F,0x8A,0x2D,0xCE,0xC4,0xAA,0x69,0x52,0xC2,0x95,0xF0,0xFD,0x1B,0x93,0x03,0x88
                ,0xFF,0x60,0xBC,0x04,0x02,0xCF,0xB7,0x1A,0x82,0x1D,0xCA,0x1F,0x85,0x5D,0x70,0x89,0x15,0xA1,0x37,0x21
                ,0x9C,0x4E,0x79,0x3C,0x05,0x94,0x50,0x5E,0x5A,0x74,0x4B,0x65,0xC6,0x64,0x7B,0x2A,0x31,0x11,0x14,0xAB
                ,0x8D,0x6B,0x6E,0x42,0xF9,0x67,0x0F,0x8B,0x26,0xDD,0xA8,0x71,0xDF,0x18,0x3B,0xA7),
                array(0xA3,0x0C,0xDF,0xC8,0x0E,0x96,0xAD,0x04,0xFD,0x51,0x25,0x46,0x0F,0xFF,0x15,0x52,0x17,0x35,0x39,0x41
                ,0xE0,0x33,0x3E,0x9E,0x03,0x36,0x38,0x13,0x8E,0xAA,0x78,0x81,0x58,0x00,0x12,0x27,0x45,0xF8,0xA7,0x90
                ,0xE4,0xCD,0x1D,0xF2,0xED,0x14,0xF5,0x3F,0xD6,0xBB,0x0D,0xD1,0xD5,0xE2,0x54,0x4E,0x8D,0xEE,0x43,0x34
                ,0x87,0x2F,0x08,0x53,0x47,0x85,0xC9,0x73,0x09,0xAB,0xD4,0xCB,0x10,0x2C,0x9D,0xCA,0x55,0x60,0xDC,0x97
                ,0x93,0x19,0xDB,0x94,0xAF,0xA1,0x67,0x5B,0xAE,0x4C,0x0B,0xCC,0x24,0xE5,0x7C,0x9C,0xA4,0x48,0x01,0x6E
                ,0x80,0x62,0x2E,0xBC,0x16,0x61,0xD0,0x7D,0xA9,0x3B,0x75,0xBD,0x30,0xA8,0x99,0x37,0xB2,0x56,0x9B,0xAC
                ,0x07,0xFE,0xB7,0x4B,0x6B,0x3D,0xB8,0x5E,0x49,0xDA,0x7F,0x69,0xF6,0x66,0xE8,0xF4,0x84,0x18,0x1F,0x83
                ,0xB6,0x71,0x50,0xD8,0x8F,0x2B,0x26,0x21,0x95,0x2A,0x7E,0x4D,0x57,0x68,0xE9,0xCE,0xD2,0x8C,0x7B,0xD7
                ,0x29,0x77,0x9F,0xB1,0xB3,0x05,0x6D,0x20,0x6A,0x7A,0x0A,0x88,0xF0,0x28,0xFB,0x5A,0x6F,0xFC,0x1E,0x86
                ,0x3C,0x89,0x9A,0x64,0xDD,0xE7,0xBA,0xD3,0xA0,0x02,0xBF,0x5F,0x79,0x42,0xF3,0x32,0xEB,0x4A,0xC0,0xCF
                ,0x23,0x74,0x44,0x40,0xA6,0x72,0xEF,0xEA,0x1C,0xB0,0xF1,0x82,0x11,0xE3,0xC7,0xDE,0xF9,0xA5,0xC5,0x31
                ,0x63,0xB4,0x8A,0x1B,0xA2,0x98,0x70,0x1A,0x8B,0xC2,0x06,0xC3,0x92,0x5C,0x65,0x22,0xE1,0xB9,0xFA,0x6C
                ,0xB5,0xC1,0xF7,0xC6,0x2D,0x3A,0x76,0xC4,0x5D,0xD9,0x4F,0xEC,0x59,0xBE,0xE6,0x91),
                array(0xCC,0x39,0xD3,0xC0,0x8D,0x75,0x68,0x48,0x52,0x35,0xE4,0x13,0xB0,0xE6,0x00,0xCA,0xA7,0x0F,0x43,0xF3
                ,0x6C,0xF8,0xCD,0xBC,0x0C,0x74,0xBA,0x67,0xC8,0xFA,0x99,0x5A,0x72,0xF6,0x2F,0xA4,0x08,0x54,0xA2,0xEA
                ,0xB3,0x90,0xF7,0x0E,0x38,0xB2,0x01,0xD2,0x1D,0x84,0x1E,0xD8,0xA5,0x27,0x20,0x7A,0xFB,0x26,0xFE,0x4C
                ,0x2E,0xE5,0x31,0xAC,0x92,0x1C,0x3C,0xC6,0xBE,0x7B,0xFC,0x8B,0xAA,0xB9,0x5E,0x70,0x04,0x5F,0x4D,0x65
                ,0x3A,0x47,0xAF,0xA1,0x3B,0xA0,0x97,0xE7,0x57,0x1B,0xAD,0x64,0xDA,0xD0,0x2D,0x6F,0xF1,0x21,0xAE,0x28
                ,0xDC,0x71,0x1F,0xEF,0x7C,0x9D,0xDE,0x15,0xDB,0x18,0x16,0xA8,0xCF,0x9B,0x81,0x36,0x3D,0xFF,0x60,0x6E
                ,0x11,0xBB,0x0A,0x45,0x4B,0xF0,0xBF,0x6A,0xD7,0xFD,0xF9,0x87,0x7F,0x5C,0xB5,0x8A,0x34,0xE0,0x41,0x06
                ,0x23,0x2C,0xDD,0x22,0xB4,0x89,0x37,0x9E,0x95,0x78,0x40,0x79,0xAB,0x2B,0xD9,0x7D,0x42,0x5D,0x86,0x09
                ,0xE9,0xF5,0xD5,0xB7,0x3F,0x55,0x4F,0x14,0x9C,0x9A,0xA3,0xCE,0x10,0x3E,0xE1,0x82,0x6B,0x33,0xE3,0x80
                ,0xD1,0x32,0x49,0x8F,0x8C,0x58,0x98,0x05,0x4E,0x83,0xD4,0x12,0xF2,0x6D,0x4A,0xC5,0xEB,0xB8,0x85,0xC9
                ,0x0B,0xE8,0x25,0x5B,0x50,0xC1,0x7E,0xA9,0xC3,0x66,0xC2,0x8E,0x46,0x2A,0x59,0x91,0x02,0xE2,0x62,0xEC
                ,0xC4,0x94,0xC7,0xF4,0xBD,0xCB,0x96,0x69,0x1A,0xB1,0x9F,0xED,0x44,0xB6,0x77,0xDF,0x93,0x07,0x73,0xA6
                ,0x29,0x61,0xEE,0x24,0x17,0x53,0x51,0x03,0x88,0x19,0x0D,0xD6,0x76,0x56,0x63,0x30),
                array(0xE8,0xF6,0x73,0x1D,0x72,0x98,0xFC,0xC0,0x17,0x14,0x33,0x03,0x5B,0x00,0x53,0x82,0xBA,0x92,0xAA,0x15
                ,0x85,0x86,0x6F,0xB1,0x81,0x79,0x77,0x6E,0x45,0x58,0xC6,0x5E,0xBF,0x94,0x67,0xEE,0xF2,0x0E,0x71,0xBB
                ,0xDF,0x74,0xA7,0x2B,0x7F,0x5D,0xEB,0x2D,0x07,0xFD,0xA4,0xF0,0xED,0x26,0xD6,0x3B,0xF4,0xDD,0xDB,0x05
                ,0x78,0x31,0xC5,0xB2,0x29,0x2F,0x89,0xE0,0x08,0xA1,0x61,0x9F,0x42,0x68,0x39,0x8B,0x27,0x5A,0x16,0x06
                ,0xD2,0x30,0x8F,0xD3,0x64,0xFA,0x8E,0x47,0x3D,0x1C,0x48,0x9A,0x9D,0x6A,0xAC,0x24,0x6C,0x88,0x18,0x6B
                ,0xF9,0x7B,0xB7,0x34,0x12,0xE4,0x3A,0xDE,0x91,0xCD,0x52,0x57,0x95,0x40,0x7C,0xB8,0x59,0x1A,0xF5,0x9B
                ,0x37,0x9E,0xC3,0xBD,0xD7,0x21,0x0A,0x75,0x65,0xD4,0x09,0xA3,0xF7,0x96,0x84,0x19,0x4C,0x5F,0x7A,0xDC
                ,0xA6,0xBE,0xBC,0xE1,0x80,0xCA,0x66,0x1E,0x62,0xFB,0x44,0x6D,0xC4,0x55,0x01,0x83,0x04,0xAF,0x0F,0x76
                ,0xFE,0xCE,0x20,0xEF,0x9C,0x41,0xC7,0x5C,0x10,0x0D,0xAB,0x97,0x2A,0xD1,0x32,0x1F,0xB6,0xE7,0xE5,0x36
                ,0xDA,0x51,0xC8,0x23,0xE6,0x43,0xEA,0xAD,0x8C,0x3E,0x11,0xB3,0x25,0x70,0x87,0xB9,0x35,0x50,0x0C,0xB0
                ,0xB4,0xA0,0xCC,0x3F,0x38,0x2E,0xF3,0x7D,0x4B,0x4D,0x22,0xA2,0x8A,0x49,0xF8,0x54,0x69,0x3C,0xE3,0xF1
                ,0x2C,0xEC,0xCB,0x4A,0x1B,0xD0,0x99,0x56,0xD9,0xE9,0x4E,0xFF,0xAE,0xD8,0x93,0xD5,0x28,0x13,0xCF,0x90
                ,0x7E,0xB5,0xC2,0xA9,0x4F,0x63,0x46,0x0B,0x02,0xA5,0x60,0xC9,0xA8,0xE2,0x8D,0xC1),
                array(0xF0,0x37,0xB8,0x5B,0x48,0x6D,0x2F,0x96,0x60,0xB5,0x07,0xDE,0xA9,0xFE,0x0D,0x14,0xD6,0x45,0x7A,0xCF
                ,0x9B,0x68,0xEA,0xE7,0x78,0x41,0x63,0xC6,0x9F,0x55,0xED,0xF7,0xAF,0xC1,0xB7,0x6E,0xFC,0xA1,0x15,0xC9
                ,0x92,0x8C,0x98,0x93,0xE3,0x21,0xA3,0xC8,0x89,0x4B,0xE1,0xCB,0x74,0x1B,0x0B,0x20,0x76,0x94,0x26,0xF5
                ,0x71,0x35,0xDC,0x02,0xA2,0xFF,0x3E,0x66,0x4A,0xFA,0xBF,0xB0,0x54,0x99,0x95,0x23,0x3C,0x31,0xC4,0x97
                ,0x7B,0xE2,0xC0,0x17,0x7C,0x4D,0x03,0x83,0x51,0x44,0xF4,0xD9,0x70,0x82,0xD2,0x65,0xF6,0x7D,0x84,0xAA
                ,0x50,0x7E,0xC7,0xFD,0x88,0x4C,0x30,0x91,0xB6,0xE5,0xBC,0x27,0x56,0x2A,0x05,0x40,0xF1,0xDF,0xC2,0x75
                ,0x80,0x90,0x6F,0x0E,0x42,0x0A,0x12,0xD1,0x79,0xDD,0xCC,0x4E,0xA8,0xB2,0x1F,0x08,0x9E,0x5C,0x24,0xA5
                ,0xBD,0xCD,0xCA,0x3A,0x00,0x1C,0x25,0x47,0xD0,0xE6,0x69,0x67,0xE0,0x9C,0x0F,0x8A,0xA4,0xEF,0x22,0xF8
                ,0x09,0x2C,0xA0,0xA7,0x85,0x5A,0x5D,0x61,0xE9,0x49,0xEC,0xB9,0xFB,0xE4,0xDA,0x52,0x04,0x2B,0xC5,0x46
                ,0xAB,0x38,0x2D,0x81,0xD4,0xBB,0x01,0xC3,0x5E,0x3D,0x8F,0x13,0x29,0x1D,0xF9,0x19,0x8E,0x9A,0x6C,0xF2
                ,0x8D,0xAE,0xD8,0x1A,0x34,0x64,0x8B,0xE8,0x2E,0x39,0x57,0x06,0xEB,0xD5,0x3F,0xD7,0x28,0x43,0x59,0x16
                ,0x62,0xAC,0x77,0x5F,0x11,0xCE,0x18,0x86,0x9D,0x73,0xAD,0x58,0x53,0xF3,0x7F,0x87,0xB1,0x72,0x33,0x32
                ,0x1E,0xB3,0x4F,0xD3,0x36,0x0C,0x6B,0x6A,0xEE,0xBE,0x3B,0xDB,0xBA,0xB4,0xA6,0x10),
                array(0x43,0xC9,0x5F,0x80,0x0F,0xBE,0xFE,0xA3,0xAC,0xFB,0xF8,0x23,0xA1,0xCA,0x30,0xA2,0xE6,0xF0,0xEE,0xF9
                ,0xEA,0xBF,0x0D,0xE5,0xF7,0x26,0xFA,0xF1,0xC4,0xF6,0xB2,0xCB,0xDF,0xA9,0x45,0x89,0xD7,0x7B,0x7E,0x54
                ,0x3C,0xD5,0x7A,0x1B,0x0A,0x19,0x69,0x6B,0x5B,0x09,0x87,0x5A,0xE2,0xCF,0x20,0xD8,0xEB,0xB0,0xB8,0xB1
                ,0x60,0x3E,0xEC,0x93,0x05,0xFD,0x03,0x31,0x66,0x12,0x55,0x13,0x1F,0xCC,0x24,0x67,0x72,0x8D,0xC0,0x92
                ,0xF5,0xA0,0x4D,0xE0,0x1A,0xA6,0x07,0x1C,0x2B,0x2C,0xDD,0xAE,0x9F,0xDA,0x63,0x83,0xAB,0x50,0x6D,0x59
                ,0x5E,0xCD,0x4F,0x9D,0x8F,0x57,0x90,0xE9,0xE3,0x2F,0x4B,0x77,0x76,0xB3,0x36,0x3D,0xE4,0x51,0xFF,0x5D
                ,0x91,0xBA,0xD3,0x81,0x04,0x88,0x40,0xBB,0xDB,0x0B,0x1D,0x41,0x84,0x4C,0x74,0x16,0x42,0x08,0x3F,0x95
                ,0xF2,0x28,0x00,0x01,0x71,0x6E,0xC1,0x11,0xA7,0x17,0x27,0xB4,0x18,0x7F,0x9B,0x96,0x53,0xD2,0xD0,0x82
                ,0x68,0x56,0xBC,0x35,0x6F,0x52,0x25,0xD9,0x33,0x75,0x49,0x46,0x78,0x61,0x0C,0x34,0x32,0xAA,0x86,0xA4
                ,0x4A,0x9C,0x47,0xC5,0xDE,0x7D,0xBD,0x3A,0xF4,0x6C,0x5C,0xC3,0x21,0x3B,0x99,0xB7,0x6A,0x9A,0xEF,0xB5
                ,0x14,0xDC,0x9E,0xE8,0x79,0x22,0xAF,0x02,0x39,0xB6,0x2E,0x64,0x1E,0xC8,0x10,0x48,0xCE,0xA8,0x85,0xED
                ,0x58,0xE1,0xC2,0xE7,0x98,0x73,0xAD,0x94,0x44,0x37,0x38,0x97,0x8B,0xD4,0xA5,0xD6,0xC7,0x15,0x62,0x70
                ,0x2A,0x06,0xFC,0x8C,0x7C,0xF3,0x2D,0xC6,0xB9,0x4E,0x8A,0x29,0x8E,0x65,0x0E,0xD1),
                array(0x2D,0x64,0x68,0x4C,0xD1,0xF9,0x42,0xFE,0xD6,0x7D,0xD7,0x72,0x78,0xB5,0x59,0x61,0x02,0xC4,0x84,0x43
                ,0x39,0x6A,0x8E,0xC2,0x09,0xAD,0x0B,0xAE,0xC9,0x8C,0xA8,0x70,0xF1,0x32,0x97,0xB9,0x16,0x0E,0xE3,0x2E
                ,0x6D,0xCF,0x14,0x9C,0x89,0xFB,0xC7,0x83,0x4F,0xAF,0x24,0x12,0x41,0x19,0xE0,0xAA,0x55,0x96,0x9D,0xA9
                ,0x38,0x92,0x8A,0x3F,0xB3,0xCC,0xC8,0xEE,0x6C,0x29,0x13,0x53,0xC3,0xEB,0x34,0x1A,0xA2,0xF3,0xAB,0x3A
                ,0x07,0xBB,0x8F,0xB0,0x30,0xAC,0xF2,0xEF,0x03,0x77,0x73,0xBE,0x6E,0x4E,0xCD,0x51,0x7F,0xBD,0x18,0xFF
                ,0x5E,0x1B,0x4B,0x5B,0xF8,0x1D,0xDA,0x47,0x7C,0x98,0xDC,0x6B,0xA6,0x65,0x2B,0xF7,0x22,0x7A,0xBC,0xFD
                ,0x9F,0x9A,0x91,0x26,0xC0,0x3E,0x27,0x50,0x2A,0x44,0x7E,0x08,0xF5,0x94,0xF0,0xA4,0x21,0x3D,0x52,0x00
                ,0xDF,0xB1,0x23,0x48,0x36,0xD0,0x95,0xE6,0x01,0x1F,0xB7,0x49,0x56,0x1E,0xBA,0x1C,0x45,0xA0,0xF6,0x9E
                ,0xDB,0x90,0xD9,0x8B,0x82,0xF4,0xA1,0x0A,0x86,0x04,0x85,0x2F,0x35,0xB4,0x54,0xFC,0xA7,0x0F,0x99,0x5F
                ,0xD2,0xD4,0x25,0xCE,0x2C,0x17,0x81,0x69,0xDE,0x79,0x0D,0x76,0x28,0x67,0x57,0xE8,0xA5,0xE5,0xE1,0x20
                ,0xB8,0x74,0x66,0x6F,0x4D,0xD8,0x05,0x4A,0x46,0x60,0xD3,0x11,0x93,0xA3,0x10,0x3C,0xDD,0xFA,0x37,0xE4
                ,0x58,0xE9,0x40,0xC1,0x80,0xB2,0xB6,0x87,0x31,0x71,0x8D,0x33,0xC5,0x5A,0x75,0x63,0x3B,0x62,0xE2,0x5D
                ,0x5C,0xEA,0xE7,0xCA,0x15,0xD5,0x9B,0xCB,0xC6,0x0C,0x88,0x7B,0x06,0xEC,0xED,0xBF),
                array(0xC3,0x37,0xA2,0x1D,0x29,0x57,0xD8,0x88,0xB3,0xEC,0x4D,0x91,0xE9,0x03,0x0B,0x59,0xC1,0x21,0xBD,0xC5
                ,0xAA,0xA9,0x22,0x5F,0x2C,0x07,0xCD,0x09,0xF9,0x3D,0x2D,0xF1,0x64,0x47,0x95,0x9E,0x82,0x76,0x80,0x8F
                ,0x4E,0x04,0x30,0x4A,0xAC,0x41,0xC2,0xDC,0x3F,0x84,0x52,0x34,0xB7,0xFE,0x4C,0x11,0x46,0x02,0x0F,0xEF
                ,0x97,0xA4,0xF6,0xBF,0xAF,0xD7,0x27,0xF4,0x93,0x0D,0x5E,0xFB,0x90,0xC4,0xE0,0x13,0x78,0x01,0xE3,0xCE
                ,0x24,0xA6,0x9A,0x1B,0x58,0x16,0xEB,0x0A,0xC7,0x35,0x77,0x7F,0xA7,0x6B,0x3A,0x00,0xFF,0x10,0x40,0x28
                ,0x79,0x9B,0x4B,0xF0,0xCC,0x4F,0xD5,0x6D,0xAB,0x85,0xAE,0x1E,0x2F,0x1F,0x6C,0x50,0xEE,0xFA,0xB5,0x7D
                ,0x89,0x38,0x0C,0xB1,0x83,0x17,0x53,0xA1,0xBC,0xB6,0x2A,0xDF,0x60,0x56,0x39,0x2B,0x72,0x31,0x5B,0x61
                ,0xDE,0xC9,0x2E,0x7E,0x74,0x06,0xD0,0x19,0xE2,0xF8,0xB9,0x8D,0xB2,0xC6,0x32,0x51,0xDB,0x9F,0x43,0x1A
                ,0xED,0x8C,0x87,0xB8,0xD4,0xCA,0x23,0x66,0xA0,0x54,0xD6,0x67,0x65,0xFD,0xB4,0x3C,0xBB,0x73,0xBE,0xB0
                ,0x15,0x48,0x5D,0xE5,0xF7,0xD9,0x9D,0x6F,0xA5,0xD2,0x92,0x45,0x6A,0xCB,0xC8,0xF2,0x42,0x99,0x25,0x75
                ,0xE6,0x8B,0xE8,0x81,0xFC,0x0E,0x1C,0xD3,0x44,0x86,0x55,0x18,0x7C,0x3B,0x5A,0x26,0xA8,0xAD,0xF3,0xBA
                ,0x14,0x70,0x7B,0x6E,0x12,0x63,0x8E,0x20,0x68,0x7A,0x96,0x71,0x69,0x3E,0xE4,0x36,0xE7,0xCF,0xDA,0x94
                ,0x33,0x62,0x49,0xDD,0x5C,0xF5,0xE1,0x08,0xD1,0xEA,0xC0,0x05,0x8A,0xA3,0x98,0x9C),
                array(0x69,0x4D,0x9A,0x06,0xC1,0x8D,0xAA,0xE8,0xF0,0x82,0x71,0x6D,0xD4,0xBC,0xBB,0x16,0xB2,0x33,0x22,0xF5
                ,0x1B,0xEC,0xCC,0x91,0xE9,0x4C,0x17,0xF4,0xBE,0x40,0xDD,0x20,0xDA,0x84,0x93,0xEB,0x59,0x7B,0x74,0x9B
                ,0x5B,0x21,0xC2,0x47,0x23,0x81,0xA6,0xCB,0x38,0x7F,0x88,0x5E,0x09,0xB6,0xC9,0xFC,0xE5,0xAF,0x07,0x2F
                ,0xD9,0xB3,0xFD,0x51,0x87,0x8B,0xE0,0x0F,0xED,0xD5,0xDC,0x10,0x85,0x18,0xCD,0xD6,0x43,0x4A,0xC3,0x15
                ,0x02,0x5A,0xC5,0xF8,0x8C,0x97,0x8F,0x83,0xC0,0x04,0x1D,0xAD,0x55,0x4F,0x9F,0x2D,0xD7,0x1A,0x00,0x98
                ,0x6A,0xC8,0xE3,0xD8,0x12,0x60,0x25,0x78,0x11,0x50,0x03,0x24,0x95,0xB8,0x58,0xF1,0xEA,0xA7,0x27,0x48
                ,0x08,0xB0,0x68,0xD0,0x28,0x42,0xFB,0x99,0xF9,0x3A,0x34,0x30,0xAE,0x80,0x94,0xE1,0xCF,0xFA,0x73,0xF6
                ,0x53,0x46,0xA8,0x90,0xA0,0x7D,0x9E,0x39,0x13,0x05,0x2C,0xF3,0x6C,0x5C,0x44,0x2B,0xDF,0xEE,0x01,0x3C
                ,0x72,0xB5,0xE4,0xCE,0x79,0xBD,0x66,0xB7,0x31,0xA9,0xCA,0x0C,0xC7,0xA5,0xDE,0xBA,0x0E,0xFE,0x3B,0x4E
                ,0x63,0xBF,0xB9,0x0B,0xE7,0x49,0xDB,0xA3,0x14,0xFF,0x45,0x0D,0x6B,0x7E,0xC4,0x26,0x2E,0x77,0x70,0x1C
                ,0x3E,0x8E,0x6F,0x36,0xE2,0xE6,0xA1,0x3D,0x56,0x37,0x0A,0xEF,0xB1,0xD2,0x61,0x62,0xD1,0x1F,0x67,0xF7
                ,0xF2,0xA2,0x7A,0x41,0x32,0x76,0x3F,0x75,0x1E,0x29,0x65,0x64,0xB4,0x52,0x19,0xAB,0x5F,0x89,0x9C,0x8A
                ,0x92,0xD3,0x6E,0x35,0x4B,0x9D,0x86,0xA4,0x5D,0x96,0x57,0x2A,0xC6,0x54,0x7C,0xAC),
                array(0xBA,0x30,0x58,0xEE,0xB3,0xFF,0x19,0x16,0x43,0xE2,0x2E,0x91,0xB5,0xA1,0xE0,0xF1,0xE7,0x1F,0x93,0xB2
                ,0x46,0x5F,0xDE,0x42,0x33,0xE6,0xE4,0x2F,0xFC,0x6D,0x98,0x41,0x9D,0xA7,0xEC,0xE5,0x86,0x78,0xBB,0x47
                ,0x5A,0x08,0x5B,0x50,0x28,0x24,0x18,0xF5,0x3C,0xD9,0x22,0xB9,0xAE,0x3F,0x29,0x1B,0x88,0x0A,0x9A,0x68
                ,0x38,0xDB,0xDC,0xC6,0xAB,0x3A,0x32,0x84,0x13,0x04,0x71,0x39,0x1A,0x64,0x62,0xD8,0x2A,0x0D,0xBF,0x00
                ,0xD6,0x9B,0xAF,0x96,0xF6,0x73,0xC4,0xD4,0xD0,0x63,0x9C,0x9E,0x1C,0x4A,0x57,0xAA,0x27,0x6F,0x9F,0x4C
                ,0x80,0xF2,0x36,0x3E,0xB1,0x4D,0x55,0x94,0xE3,0xD3,0x5C,0x44,0x6A,0x01,0x85,0x6C,0x99,0x67,0x09,0xCF
                ,0x05,0x6E,0x0F,0xDD,0x2B,0x49,0xF7,0xBD,0x74,0x37,0xF4,0xAD,0xFD,0x07,0xBE,0x54,0xEF,0x0B,0x8A,0x8C
                ,0x53,0xC8,0x7A,0x97,0x8D,0x35,0xBC,0x12,0x76,0xEB,0x5E,0x17,0xD5,0x2D,0x26,0xC3,0xC2,0x03,0xDA,0xA2
                ,0x8B,0x56,0xE9,0xA0,0xE1,0x45,0x87,0x75,0xC1,0xF0,0xB6,0x0E,0x2C,0x20,0x7F,0x0C,0xB7,0x1D,0x8F,0x7B
                ,0x21,0xE8,0x11,0x70,0x8E,0x15,0x4B,0x51,0x06,0xEA,0xA8,0xA5,0x83,0xF9,0x3B,0xCC,0x14,0xA6,0xD7,0x10
                ,0xA3,0xCB,0x48,0x4E,0xC9,0x23,0xD2,0xF3,0x77,0x95,0xB4,0x65,0xFA,0x40,0xAC,0x89,0x34,0x7D,0xFE,0x82
                ,0xF8,0xDF,0xB8,0x60,0x7E,0x02,0xC0,0x25,0xCA,0x7C,0x72,0xD1,0x61,0xCD,0x81,0x4F,0xED,0x52,0x3D,0x90
                ,0x1E,0x31,0xC7,0xB0,0x5D,0xA9,0x66,0x79,0xC5,0xFB,0x92,0xCE,0x6B,0xA4,0x69,0x59),
                array(0x89,0xBF,0xD9,0x9F,0x15,0xA0,0x46,0xB1,0xDB,0x37,0xF5,0x39,0xA2,0xC6,0x4D,0x49,0x17,0xA6,0x62,0x00
                ,0xB4,0x3F,0xFB,0x85,0xBE,0xFD,0xA3,0xC0,0xAE,0x73,0x42,0xE2,0x51,0xD5,0xB3,0x75,0x2D,0x6A,0x38,0xE9
                ,0x60,0x86,0x82,0xF4,0x31,0xDA,0x74,0xB7,0x08,0x21,0x7C,0x72,0x70,0x69,0xE4,0x6B,0x8A,0x01,0x56,0xA4
                ,0x90,0x5A,0x35,0x0F,0x7D,0x5D,0x02,0x3D,0xD7,0x1E,0xEE,0xBA,0xC8,0x78,0x58,0x26,0x3E,0x0A,0xFF,0x91
                ,0x81,0x09,0x63,0x65,0xD1,0x53,0x4C,0x99,0x13,0x32,0x57,0xDD,0xAA,0xAC,0xA8,0x0B,0xF2,0x50,0x6F,0x83
                ,0x2B,0xFE,0x07,0xAD,0xCB,0x03,0xC4,0x93,0xE5,0xFA,0xC3,0xF6,0x0E,0xF7,0x30,0xEA,0x9C,0x5F,0x7B,0xA9
                ,0x5B,0x92,0x34,0xAF,0xB2,0x96,0x18,0x64,0xAB,0x6C,0x2E,0xCA,0xE1,0x1A,0x2A,0xD0,0x3A,0xF1,0x95,0x6E
                ,0x11,0xB6,0x97,0x98,0xD8,0xA1,0x16,0x22,0x48,0xE6,0x5C,0x4B,0x5E,0x20,0x84,0x80,0x05,0xDE,0xCF,0x8D
                ,0xCC,0x52,0x1D,0x33,0xF0,0xD6,0xE7,0x8C,0x66,0xC7,0x71,0xDC,0xEB,0x0D,0xFC,0x2F,0xD4,0xEC,0x76,0xD2
                ,0x1B,0xB0,0x7F,0x59,0xBC,0xC2,0x9A,0x94,0x77,0x7A,0xB5,0xF8,0x41,0x47,0x61,0x12,0x54,0xC1,0xBB,0xE3
                ,0x7E,0x25,0x8F,0xB9,0xF9,0xB8,0x9D,0x28,0x55,0x27,0x24,0x19,0x04,0x43,0x2C,0x88,0xC5,0x3C,0x14,0xCD
                ,0x9B,0x8B,0x36,0xA7,0x9E,0xED,0xA5,0x10,0xF3,0x4A,0x40,0x79,0xBD,0x87,0xCE,0x8E,0x45,0x23,0xE8,0x1C
                ,0x4E,0x3B,0x1F,0x68,0xEF,0x0C,0x29,0xE0,0x67,0x6D,0x44,0x4F,0xD3,0xC9,0x06,0xDF));
                

  $THE_MATRIX_CONVERTION = array(array(0x25,0x17,0x37,0x14,0x19,0x47,0x20,0x2E,0x10,0x4E,0x3A,0x11,0x00,0x33,0x23,0x36,0x3D,0x3E,0x04,0x2C
                            ,0x44,0x49,0x0A,0x2A,0x0D,0x3F,0x12,0x15,0x42,0x48,0x01,0x27,0x3C,0x3B,0x0F,0x0B,0x2D,0x34,0x09,0x2B
                            ,0x1D,0x45,0x22,0x41,0x1A,0x28,0x35,0x31,0x4B,0x46,0x43,0x30,0x4A,0x38,0x1E,0x4D,0x39,0x03,0x0C,0x29
                            ,0x26,0x4F,0x18,0x08,0x2F,0x06,0x07,0x40,0x1F,0x02,0x0E,0x13,0x24,0x05,0x16,0x1B,0x4C,0x1C,0x21,0x32),
                            array(0x22,0x3B,0x0E,0x0F,0x17,0x14,0x0C,0x1E,0x40,0x3F,0x19,0x27,0x2C,0x3C,0x0A,0x1C,0x01,0x03,0x08,0x4B
                            ,0x4D,0x45,0x46,0x3D,0x41,0x2F,0x31,0x34,0x1F,0x36,0x2E,0x02,0x26,0x4E,0x42,0x25,0x1A,0x4C,0x30,0x43
                            ,0x05,0x11,0x3A,0x13,0x06,0x29,0x09,0x07,0x0B,0x1D,0x18,0x2B,0x16,0x38,0x23,0x10,0x4A,0x20,0x1B,0x00
                            ,0x0D,0x12,0x49,0x4F,0x15,0x33,0x21,0x39,0x35,0x32,0x28,0x04,0x3E,0x24,0x37,0x2A,0x48,0x2D,0x47,0x44),
                            array(0x3C,0x47,0x28,0x30,0x03,0x38,0x4C,0x44,0x14,0x04,0x2D,0x37,0x43,0x2E,0x21,0x0E,0x10,0x1F,0x0D,0x01
                            ,0x27,0x4E,0x1C,0x39,0x07,0x33,0x09,0x2A,0x25,0x1D,0x3F,0x1E,0x23,0x32,0x2C,0x42,0x48,0x35,0x24,0x3D
                            ,0x2B,0x0C,0x05,0x41,0x16,0x13,0x17,0x49,0x1A,0x4B,0x29,0x46,0x3A,0x0B,0x12,0x0A,0x19,0x45,0x00,0x36
                            ,0x34,0x02,0x3B,0x11,0x31,0x22,0x3E,0x1B,0x15,0x4D,0x06,0x4F,0x18,0x26,0x20,0x4A,0x40,0x08,0x2F,0x0F),
                            array(0x4A,0x3C,0x3D,0x24,0x45,0x44,0x2A,0x0D,0x36,0x2C,0x0F,0x19,0x2B,0x4E,0x27,0x1D,0x07,0x33,0x3B,0x1A
                            ,0x22,0x4C,0x14,0x3F,0x40,0x30,0x1B,0x41,0x02,0x2D,0x00,0x17,0x34,0x21,0x12,0x13,0x1E,0x32,0x2F,0x0E
                            ,0x49,0x1C,0x46,0x39,0x4D,0x26,0x0C,0x08,0x28,0x10,0x05,0x47,0x11,0x01,0x3A,0x1F,0x0B,0x06,0x2E,0x35
                            ,0x4B,0x25,0x0A,0x04,0x29,0x4F,0x42,0x18,0x48,0x03,0x37,0x43,0x20,0x23,0x15,0x38,0x31,0x09,0x16,0x3E),
                            array(0x07,0x39,0x4A,0x01,0x2C,0x19,0x45,0x1E,0x44,0x11,0x00,0x1C,0x48,0x20,0x42,0x18,0x30,0x26,0x1D,0x12
                            ,0x0E,0x23,0x34,0x17,0x05,0x25,0x3D,0x2A,0x49,0x38,0x0A,0x0B,0x03,0x3F,0x15,0x22,0x31,0x10,0x2E,0x13
                            ,0x21,0x43,0x40,0x02,0x4F,0x1F,0x4D,0x4B,0x4C,0x3A,0x16,0x3B,0x32,0x0C,0x33,0x28,0x27,0x37,0x3C,0x35
                            ,0x08,0x46,0x09,0x06,0x0D,0x29,0x41,0x1A,0x4E,0x0F,0x2B,0x24,0x14,0x04,0x1B,0x3E,0x2D,0x47,0x36,0x2F),
                            array(0x1F,0x36,0x41,0x2A,0x3A,0x10,0x31,0x4B,0x28,0x4E,0x2B,0x11,0x17,0x43,0x29,0x0C,0x47,0x06,0x2D,0x21
                            ,0x2E,0x22,0x39,0x0F,0x14,0x15,0x0D,0x05,0x4C,0x03,0x3C,0x38,0x20,0x42,0x3D,0x4F,0x18,0x40,0x49,0x2C
                            ,0x19,0x33,0x46,0x1E,0x02,0x09,0x35,0x13,0x16,0x4D,0x04,0x24,0x45,0x27,0x37,0x32,0x12,0x34,0x00,0x0B
                            ,0x08,0x1A,0x30,0x23,0x1B,0x26,0x07,0x44,0x1D,0x0E,0x1C,0x3F,0x48,0x0A,0x3B,0x3E,0x2F,0x4A,0x01,0x25),
                            array(0x1F,0x3A,0x1B,0x33,0x20,0x3B,0x37,0x18,0x36,0x1D,0x1E,0x1C,0x06,0x11,0x2B,0x32,0x2E,0x43,0x16,0x40
                            ,0x2C,0x0B,0x2A,0x0C,0x27,0x38,0x0F,0x01,0x2D,0x3E,0x45,0x25,0x3C,0x00,0x0A,0x47,0x4D,0x13,0x15,0x17
                            ,0x30,0x31,0x12,0x0D,0x4B,0x3D,0x08,0x48,0x22,0x04,0x41,0x24,0x09,0x44,0x19,0x14,0x29,0x4C,0x4F,0x21
                            ,0x03,0x2F,0x26,0x42,0x3F,0x4E,0x23,0x34,0x28,0x10,0x05,0x49,0x39,0x46,0x07,0x02,0x35,0x4A,0x0E,0x1A),
                            array(0x01,0x29,0x3A,0x15,0x0E,0x25,0x31,0x1B,0x0A,0x38,0x2D,0x10,0x09,0x2F,0x41,0x4A,0x2E,0x40,0x4B,0x27
                            ,0x32,0x02,0x0B,0x39,0x23,0x16,0x14,0x19,0x22,0x07,0x37,0x0D,0x43,0x17,0x00,0x3F,0x28,0x1C,0x1A,0x1F
                            ,0x4C,0x44,0x03,0x1D,0x4F,0x47,0x18,0x4D,0x49,0x34,0x13,0x33,0x26,0x42,0x30,0x0C,0x0F,0x2A,0x2B,0x3E
                            ,0x12,0x35,0x3C,0x1E,0x24,0x36,0x11,0x45,0x06,0x48,0x05,0x4E,0x20,0x46,0x04,0x08,0x2C,0x3D,0x21,0x3B),
                            array(0x2A,0x30,0x0A,0x09,0x00,0x2F,0x42,0x0E,0x4B,0x1E,0x27,0x41,0x39,0x2B,0x38,0x46,0x07,0x2C,0x04,0x14
                            ,0x32,0x1B,0x28,0x19,0x4F,0x43,0x3F,0x0D,0x0F,0x4A,0x18,0x35,0x37,0x11,0x47,0x1C,0x0B,0x25,0x17,0x23
                            ,0x13,0x4C,0x36,0x10,0x24,0x01,0x3C,0x49,0x16,0x40,0x44,0x48,0x34,0x12,0x1F,0x20,0x4D,0x02,0x1D,0x06
                            ,0x05,0x3B,0x0C,0x03,0x4E,0x1A,0x08,0x22,0x3A,0x15,0x21,0x33,0x45,0x2D,0x2E,0x31,0x26,0x3D,0x3E,0x29),
                            array(0x47,0x20,0x4A,0x48,0x1C,0x16,0x40,0x4B,0x3A,0x29,0x0D,0x04,0x41,0x3F,0x01,0x0A,0x3D,0x3E,0x42,0x06
                            ,0x35,0x22,0x21,0x00,0x49,0x43,0x2A,0x07,0x0E,0x32,0x0F,0x39,0x08,0x02,0x34,0x45,0x23,0x0B,0x33,0x25
                            ,0x24,0x1A,0x46,0x1E,0x2B,0x3B,0x05,0x44,0x14,0x26,0x37,0x18,0x4F,0x1B,0x4E,0x19,0x15,0x4C,0x36,0x31
                            ,0x3C,0x0C,0x10,0x2E,0x03,0x2C,0x38,0x1F,0x30,0x2F,0x09,0x1D,0x4D,0x2D,0x11,0x12,0x28,0x17,0x13,0x27),
                            array(0x4A,0x4C,0x17,0x2C,0x48,0x45,0x07,0x33,0x01,0x30,0x36,0x12,0x27,0x40,0x09,0x29,0x3C,0x05,0x1C,0x16
                            ,0x3E,0x3D,0x04,0x34,0x15,0x0E,0x28,0x1A,0x4E,0x13,0x4B,0x1F,0x42,0x2F,0x0F,0x26,0x37,0x31,0x0C,0x25
                            ,0x24,0x02,0x44,0x2B,0x20,0x18,0x2D,0x35,0x3A,0x1E,0x06,0x0A,0x00,0x11,0x49,0x38,0x21,0x10,0x39,0x03
                            ,0x41,0x2E,0x19,0x2A,0x0B,0x4D,0x3F,0x3B,0x08,0x47,0x22,0x23,0x1D,0x1B,0x0D,0x4F,0x14,0x43,0x32,0x46),
                            array(0x1D,0x19,0x45,0x38,0x46,0x26,0x37,0x2A,0x42,0x12,0x48,0x0E,0x11,0x25,0x40,0x44,0x3E,0x08,0x49,0x1A
                            ,0x27,0x2F,0x47,0x1F,0x41,0x34,0x22,0x4D,0x00,0x2E,0x0F,0x3F,0x10,0x4E,0x28,0x18,0x4C,0x01,0x15,0x43
                            ,0x32,0x4F,0x17,0x36,0x4A,0x31,0x39,0x14,0x24,0x0C,0x20,0x30,0x0A,0x21,0x33,0x2C,0x23,0x0B,0x05,0x03
                            ,0x0D,0x1B,0x3A,0x09,0x16,0x06,0x3D,0x07,0x4B,0x13,0x02,0x2D,0x1C,0x2B,0x3C,0x1E,0x3B,0x35,0x04,0x29),
                            array(0x48,0x18,0x03,0x00,0x41,0x04,0x0F,0x10,0x02,0x39,0x3F,0x4D,0x32,0x4C,0x4B,0x0E,0x19,0x1D,0x2F,0x37
                            ,0x16,0x4F,0x29,0x12,0x3A,0x2B,0x2E,0x1A,0x42,0x26,0x36,0x2D,0x06,0x1C,0x1E,0x33,0x24,0x05,0x01,0x11
                            ,0x0A,0x3C,0x1F,0x3E,0x45,0x2C,0x21,0x22,0x44,0x35,0x31,0x0B,0x17,0x38,0x49,0x30,0x4A,0x40,0x20,0x25
                            ,0x27,0x07,0x09,0x0D,0x28,0x2A,0x47,0x43,0x3B,0x0C,0x15,0x3D,0x34,0x08,0x46,0x13,0x14,0x1B,0x4E,0x23),
                            array(0x3E,0x45,0x0A,0x34,0x09,0x17,0x1B,0x2F,0x01,0x28,0x22,0x3B,0x26,0x2C,0x4B,0x4D,0x48,0x08,0x30,0x1E
                            ,0x13,0x1F,0x02,0x1C,0x29,0x32,0x11,0x35,0x1A,0x14,0x00,0x12,0x4C,0x37,0x3C,0x39,0x23,0x46,0x18,0x40
                            ,0x36,0x04,0x44,0x49,0x19,0x24,0x06,0x03,0x2A,0x47,0x21,0x0D,0x33,0x05,0x27,0x41,0x0B,0x4A,0x2D,0x20
                            ,0x16,0x2E,0x3D,0x1D,0x4E,0x43,0x10,0x25,0x4F,0x0E,0x07,0x0C,0x3A,0x15,0x3F,0x42,0x38,0x2B,0x0F,0x31),
                            array(0x12,0x27,0x38,0x1C,0x07,0x15,0x48,0x33,0x44,0x32,0x39,0x4E,0x3C,0x42,0x17,0x47,0x18,0x3D,0x04,0x2E
                            ,0x30,0x4C,0x3E,0x19,0x0B,0x24,0x1E,0x34,0x06,0x4B,0x36,0x23,0x2A,0x00,0x2C,0x14,0x41,0x0D,0x09,0x1D
                            ,0x03,0x4F,0x21,0x2B,0x13,0x29,0x16,0x40,0x1A,0x4A,0x3F,0x46,0x31,0x20,0x01,0x26,0x10,0x49,0x22,0x35
                            ,0x0E,0x43,0x0F,0x3A,0x11,0x25,0x08,0x3B,0x37,0x28,0x02,0x1F,0x2F,0x4D,0x1B,0x05,0x0A,0x45,0x2D,0x0C),
                            array(0x4A,0x2B,0x12,0x1A,0x20,0x13,0x42,0x33,0x43,0x03,0x0A,0x4B,0x10,0x3F,0x09,0x29,0x18,0x2A,0x21,0x3D
                            ,0x4D,0x07,0x19,0x1E,0x02,0x0C,0x0F,0x3E,0x23,0x3A,0x0B,0x17,0x0D,0x49,0x16,0x1C,0x36,0x22,0x48,0x01
                            ,0x1F,0x04,0x2D,0x35,0x31,0x40,0x3C,0x39,0x45,0x27,0x44,0x06,0x30,0x34,0x26,0x0E,0x08,0x37,0x15,0x41
                            ,0x2F,0x11,0x1D,0x4F,0x3B,0x24,0x4E,0x2C,0x28,0x46,0x32,0x14,0x00,0x1B,0x47,0x25,0x38,0x2E,0x4C,0x05));


/*******************************************************************************

                         ROTINA PARA CALCULO DE CRC

*******************************************************************************/



  
   $CRC32Table = array(
       0x00000000, 0x77073096, 0xEE0E612C, 0x990951BA,
       0x076DC419, 0x706AF48F, 0xE963A535, 0x9E6495A3,
       0x0EDB8832, 0x79DCB8A4, 0xE0D5E91E, 0x97D2D988,
       0x09B64C2B, 0x7EB17CBD, 0xE7B82D07, 0x90BF1D91,

       0x1DB71064, 0x6AB020F2, 0xF3B97148, 0x84BE41DE,
       0x1ADAD47D, 0x6DDDE4EB, 0xF4D4B551, 0x83D385C7,
       0x136C9856, 0x646BA8C0, 0xFD62F97A, 0x8A65C9EC,
       0x14015C4F, 0x63066CD9, 0xFA0F3D63, 0x8D080DF5,

       0x3B6E20C8, 0x4C69105E, 0xD56041E4, 0xA2677172,
       0x3C03E4D1, 0x4B04D447, 0xD20D85FD, 0xA50AB56B,
       0x35B5A8FA, 0x42B2986C, 0xDBBBC9D6, 0xACBCF940,
       0x32D86CE3, 0x45DF5C75, 0xDCD60DCF, 0xABD13D59,

       0x26D930AC, 0x51DE003A, 0xC8D75180, 0xBFD06116,
       0x21B4F4B5, 0x56B3C423, 0xCFBA9599, 0xB8BDA50F,
       0x2802B89E, 0x5F058808, 0xC60CD9B2, 0xB10BE924,
       0x2F6F7C87, 0x58684C11, 0xC1611DAB, 0xB6662D3D,

       0x76DC4190, 0x01DB7106, 0x98D220BC, 0xEFD5102A,
       0x71B18589, 0x06B6B51F, 0x9FBFE4A5, 0xE8B8D433,
       0x7807C9A2, 0x0F00F934, 0x9609A88E, 0xE10E9818,
       0x7F6A0DBB, 0x086D3D2D, 0x91646C97, 0xE6635C01,

       0x6B6B51F4, 0x1C6C6162, 0x856530D8, 0xF262004E,
       0x6C0695ED, 0x1B01A57B, 0x8208F4C1, 0xF50FC457,
       0x65B0D9C6, 0x12B7E950, 0x8BBEB8EA, 0xFCB9887C,
       0x62DD1DDF, 0x15DA2D49, 0x8CD37CF3, 0xFBD44C65,

       0x4DB26158, 0x3AB551CE, 0xA3BC0074, 0xD4BB30E2,
       0x4ADFA541, 0x3DD895D7, 0xA4D1C46D, 0xD3D6F4FB,
       0x4369E96A, 0x346ED9FC, 0xAD678846, 0xDA60B8D0,
       0x44042D73, 0x33031DE5, 0xAA0A4C5F, 0xDD0D7CC9,

       0x5005713C, 0x270241AA, 0xBE0B1010, 0xC90C2086,
       0x5768B525, 0x206F85B3, 0xB966D409, 0xCE61E49F,
       0x5EDEF90E, 0x29D9C998, 0xB0D09822, 0xC7D7A8B4,
       0x59B33D17, 0x2EB40D81, 0xB7BD5C3B, 0xC0BA6CAD,

       0xEDB88320, 0x9ABFB3B6, 0x03B6E20C, 0x74B1D29A,
       0xEAD54739, 0x9DD277AF, 0x04DB2615, 0x73DC1683,
       0xE3630B12, 0x94643B84, 0x0D6D6A3E, 0x7A6A5AA8,
       0xE40ECF0B, 0x9309FF9D, 0x0A00AE27, 0x7D079EB1,

       0xF00F9344, 0x8708A3D2, 0x1E01F268, 0x6906C2FE,
       0xF762575D, 0x806567CB, 0x196C3671, 0x6E6B06E7,
       0xFED41B76, 0x89D32BE0, 0x10DA7A5A, 0x67DD4ACC,
       0xF9B9DF6F, 0x8EBEEFF9, 0x17B7BE43, 0x60B08ED5,

       0xD6D6A3E8, 0xA1D1937E, 0x38D8C2C4, 0x4FDFF252,
       0xD1BB67F1, 0xA6BC5767, 0x3FB506DD, 0x48B2364B,
       0xD80D2BDA, 0xAF0A1B4C, 0x36034AF6, 0x41047A60,
       0xDF60EFC3, 0xA867DF55, 0x316E8EEF, 0x4669BE79,

       0xCB61B38C, 0xBC66831A, 0x256FD2A0, 0x5268E236,
       0xCC0C7795, 0xBB0B4703, 0x220216B9, 0x5505262F,
       0xC5BA3BBE, 0xB2BD0B28, 0x2BB45A92, 0x5CB36A04,
       0xC2D7FFA7, 0xB5D0CF31, 0x2CD99E8B, 0x5BDEAE1D,

       0x9B64C2B0, 0xEC63F226, 0x756AA39C, 0x026D930A,
       0x9C0906A9, 0xEB0E363F, 0x72076785, 0x05005713,
       0x95BF4A82, 0xE2B87A14, 0x7BB12BAE, 0x0CB61B38,
       0x92D28E9B, 0xE5D5BE0D, 0x7CDCEFB7, 0x0BDBDF21,

       0x86D3D2D4, 0xF1D4E242, 0x68DDB3F8, 0x1FDA836E,
       0x81BE16CD, 0xF6B9265B, 0x6FB077E1, 0x18B74777,
       0x88085AE6, 0xFF0F6A70, 0x66063BCA, 0x11010B5C,
       0x8F659EFF, 0xF862AE69, 0x616BFFD3, 0x166CCF45,

       0xA00AE278, 0xD70DD2EE, 0x4E048354, 0x3903B3C2,
       0xA7672661, 0xD06016F7, 0x4969474D, 0x3E6E77DB,
       0xAED16A4A, 0xD9D65ADC, 0x40DF0B66, 0x37D83BF0,
       0xA9BCAE53, 0xDEBB9EC5, 0x47B2CF7F, 0x30B5FFE9,

       0xBDBDF21C, 0xCABAC28A, 0x53B39330, 0x24B4A3A6,
       0xBAD03605, 0xCDD70693, 0x54DE5729, 0x23D967BF,
       0xB3667A2E, 0xC4614AB8, 0x5D681B02, 0x2A6F2B94,
       0xB40BBE37, 0xC30C8EA1, 0x5A05DF1B, 0x2D02EF8D);



function CalcCRC32($buffer, $bufsize)
{
  global $CRC32Table;
  
  $resultado = 0xFFFFFFFF;
  
  $i = 0;
  for ($i=0;$i<$bufsize;$i++)
     $resultado = ((hexdec(substr(sprintf('%08X', $resultado), 0, 6))) ^ ($CRC32Table[(ord($buffer[$i]) ^ ($resultado & 0x000000FF))]));
  
  return ~$resultado;
}


function MontaInt64($Valor1, $Valor2)
{
  $resultado = array();
  
  $Valor1 = sprintf('%08X', $Valor1);
  $resultado[] = hexdec('0x' . substr($Valor1, 0, 2));
  $resultado[] = hexdec('0x' . substr($Valor1, 2, 2));
  $resultado[] = hexdec('0x' . substr($Valor1, 4, 2));
  $resultado[] = hexdec('0x' . substr($Valor1, 6, 2));
  $Valor2 = sprintf('%08X', $Valor2);
  $resultado[] = hexdec('0x' . substr($Valor2, 0, 2));
  $resultado[] = hexdec('0x' . substr($Valor2, 2, 2));
  $resultado[] = hexdec('0x' . substr($Valor2, 4, 2));
  $resultado[] = hexdec('0x' . substr($Valor2, 6, 2));
  
  return $resultado;
}

function RetornaNibble64($Chave, $PosicaoNibble)
{
  //PosicaoNibble:
  //bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb => Int64
  //\--/\--/\--/\--/\--/\--/\--/\--/\--/\--/\--/\--/\--/\--/\--/\--/
  // 15  14  13  12  11  10  9   8   7   6   5   4   3   2   1   0
  $resultado = thirtyTwoBitIntval($Chave[(15 - $PosicaoNibble) / 2]);//como aqui a pilha está "normal", entao preciso inverter os bytes manualmente
  if (($PosicaoNibble % 2) == 0)
    $resultado = $resultado & 0x0F;
    else
      $resultado = ($resultado & 0xF0) >> 4;

  return $resultado;
}

function _Criptografar64($CHAVE, $Buffer, $TamanhoBuffer)
{
  global $THE_MATRIX_CONVERTION;
  global $THE_MATRIX;
  
  for ($i=0;$i<$TamanhoBuffer;$i++)
    $Buffer[$i] = chr($THE_MATRIX[$THE_MATRIX_CONVERTION[RetornaNibble64($CHAVE, ($i % 16))][(thirtyTwoBitIntval(($i/16))) % 80]][ord($Buffer[$i])]);
     
  return $Buffer;
}

function PosicaoItemVetor($item, $linha)
{
  global $THE_MATRIX;
  
  for ($i=0;$i<256;$i++)
    if ($THE_MATRIX[$linha][$i] == $item) return $i;
    
  return 0;
}

function _DeCriptografar64($CHAVE, $Buffer, $TamanhoBuffer)
{
  global $THE_MATRIX_CONVERTION;
  
  for ($i=0;$i<$TamanhoBuffer;$i++)
    $Buffer[$i] = chr(PosicaoItemVetor(ord($Buffer[$i]), $THE_MATRIX_CONVERTION[RetornaNibble64($CHAVE, ($i % 16))][(thirtyTwoBitIntval(($i/16))) % 80]));
    
  return $Buffer;
}

function Criptografar64($CHAVE1, $CHAVE2, $Buffer, $TamanhoBuffer)
{
  $CHAVE = MontaInt64(CalcCRC32($CHAVE1, strlen($CHAVE1)), CalcCRC32($CHAVE2, strlen($CHAVE2)));
  return _Criptografar64($CHAVE, $Buffer, $TamanhoBuffer);
}

function DeCriptografar64($CHAVE1, $CHAVE2, $Buffer, $TamanhoBuffer)
{
  $CHAVE = MontaInt64(CalcCRC32($CHAVE1, strlen($CHAVE1)), CalcCRC32($CHAVE2, strlen($CHAVE2)));
  return _DeCriptografar64($CHAVE, $Buffer, $TamanhoBuffer);
}

function Codifica($Dados, $tamanho)
{
    $resultado = "";

    for ($i=0;$i<$tamanho;$i++)
    {
        $resultado .= sprintf("%02X", ord($Dados[$i]));
    }
    
    return $resultado;
}

function Decodifica($dados)
{
  $tamanho = thirtyTwoBitIntval((strlen($dados) / 2));
  $resultado = "";

  for ($i=0;$i<$tamanho;$i++)
    $resultado .= chr(hexdec('0x' . substr($dados, $i * 2, 2)));
    
  return $resultado;
}

function  EncriptaECodifica($CHAVE1, $CHAVE2, $Buffer, $TamanhoBuffer)
{
  return Codifica(Criptografar64($CHAVE1, $CHAVE2, $Buffer, $TamanhoBuffer), $TamanhoBuffer); 
}

function DecodificaEDecripta($CHAVE1, $CHAVE2, $dados)
{
  $tamanho = thirtyTwoBitIntval((strlen($dados)/2));
  return DeCriptografar64($CHAVE1, $CHAVE2, Decodifica($dados), $tamanho); 
}

function EncriptaECodificaString($CHAVE1, $CHAVE2, $Texto)
{
  return EncriptaECodifica($CHAVE1, $CHAVE2, $Texto, strlen($Texto));
}

function DecodificaEDecriptaString($CHAVE1, $CHAVE2, $Texto)
{
  return DecodificaEDecripta($CHAVE1, $CHAVE2, $Texto);
}

?>
