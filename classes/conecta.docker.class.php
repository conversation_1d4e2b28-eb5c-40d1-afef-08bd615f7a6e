<?php

/**
 * Classe de conexão com banco de dados otimizada para Docker
 * Versão melhorada da conecta.class.php original
 */
class conecta {

    public $link = NULL;
    public $tenant = NULL;
    public $database = NULL;
    
    // Pool de conexões para Swoole
    private static $connectionPool = [];
    private static $maxConnections = 20;
    private static $currentConnections = 0;

    function __construct($link = NULL, $tenant = NULL) {
        
        if ($link !== NULL) {
            $this->link = $link;
            return;
        }

        // Usar variáveis de ambiente para configuração
        $host = $_ENV['DB_HOST'] ?? 'lettorestage-db.cb0yc0yuuqot.us-west-2.rds.amazonaws.com';
        $port = $_ENV['DB_PORT'] ?? 3306;
        $username = $_ENV['DB_USERNAME'] ?? 'admin';
        $password = $_ENV['DB_PASSWORD'] ?? 'JASHDgvHAS%D$A412TFDSDASDJhgVADKJHASD';
        
        // Determinar tenant e database
        if ($tenant !== NULL) {
            $this->tenant = $tenant;
            $this->database = $tenant;
        } else {
            // Tentar detectar tenant do host
            $this->detectTenant();
        }

        // Criar conexão
        $this->connect($host, $port, $username, $password);
    }

    private function detectTenant() {
        global $TENANT;
        
        if (isset($TENANT) && is_array($TENANT)) {
            $this->tenant = $TENANT['tenant'] ?? 'default';
            $this->database = $TENANT['database'] ?? $this->tenant;
        } else {
            // Fallback para detecção por host ou ambiente Docker
            if (getenv('DB_NAME')) {
                $this->tenant = 'docker';
                $this->database = getenv('DB_NAME');
            } else {
                $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
                if (preg_match("/\.?([A-Za-z0-9-]+)\.lettore/", $host, $matches)) {
                    $this->tenant = $matches[1];
                    $this->database = $this->tenant;
                } else {
                    $this->tenant = 'default';
                    $this->database = 'lettore_dev';  // Database padrão Docker
                }
            }
        }
    }

    private function connect($host, $port, $username, $password) {
        try {
            // Tentar reutilizar conexão do pool (para Swoole)
            $connectionKey = "{$host}:{$port}:{$username}:{$this->database}";
            
            if (isset(self::$connectionPool[$connectionKey]) && 
                self::$connectionPool[$connectionKey]->ping()) {
                $this->link = self::$connectionPool[$connectionKey];
                return;
            }

            // Criar nova conexão
            $this->link = new mysqli($host, $username, $password, $this->database, $port);
            
            if ($this->link->connect_error) {
                throw new Exception("Erro de conexão: " . $this->link->connect_error);
            }

            // Configurar charset UTF8MB4 para MySQL 8.0
            $this->link->set_charset('utf8mb4');
            
            // Configurações específicas MySQL 8.0
            $this->link->query("SET sql_mode=(SELECT REPLACE(@@sql_mode,'ONLY_FULL_GROUP_BY',''))");
            $this->link->query("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");
            
            // Configurações de timeout
            $this->link->options(MYSQLI_OPT_CONNECT_TIMEOUT, 30);
            $this->link->options(MYSQLI_OPT_READ_TIMEOUT, 60);
            
            // Adicionar ao pool se não exceder limite
            if (self::$currentConnections < self::$maxConnections) {
                self::$connectionPool[$connectionKey] = $this->link;
                self::$currentConnections++;
            }
            
        } catch (Exception $e) {
            error_log("Erro na conexão com banco: " . $e->getMessage());
            throw $e;
        }
    }

    function __destruct() {
        // Não fechar conexão se estiver no pool (para Swoole)
        // A conexão será reutilizada
    }

    public function query($sql) {
        if (!$this->link) {
            throw new Exception("Conexão com banco não estabelecida");
        }
        
        $result = $this->link->query($sql);
        
        if (!$result) {
            error_log("Erro SQL: " . $this->link->error . " | Query: " . $sql);
            throw new Exception("Erro na consulta: " . $this->link->error);
        }
        
        return $result;
    }

    public function escape_string($string) {
        if (!$this->link) {
            return addslashes($string);
        }
        return $this->link->real_escape_string($string);
    }

    public function insert_id() {
        return $this->link ? $this->link->insert_id : 0;
    }

    public function affected_rows() {
        return $this->link ? $this->link->affected_rows : 0;
    }

    public function num_rows($result) {
        return $result ? $result->num_rows : 0;
    }

    public function fetch_array($result, $type = MYSQLI_ASSOC) {
        return $result ? $result->fetch_array($type) : false;
    }

    public function fetch_assoc($result) {
        return $result ? $result->fetch_assoc() : false;
    }

    public function free_result($result) {
        if ($result) {
            $result->free();
        }
    }

    // Método para limpar pool de conexões (útil para manutenção)
    public static function clearConnectionPool() {
        foreach (self::$connectionPool as $connection) {
            if ($connection) {
                $connection->close();
            }
        }
        self::$connectionPool = [];
        self::$currentConnections = 0;
    }

    // Método para verificar saúde das conexões
    public static function healthCheck() {
        $healthy = 0;
        $total = count(self::$connectionPool);
        
        foreach (self::$connectionPool as $key => $connection) {
            if ($connection && $connection->ping()) {
                $healthy++;
            } else {
                unset(self::$connectionPool[$key]);
                self::$currentConnections--;
            }
        }
        
        return [
            'total' => $total,
            'healthy' => $healthy,
            'current' => self::$currentConnections
        ];
    }
}
