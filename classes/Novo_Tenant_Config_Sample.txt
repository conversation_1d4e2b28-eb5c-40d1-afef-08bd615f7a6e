
    "[#TENANTSLUG#]" => array(
        # Des<PERSON><PERSON><PERSON>
        "tenant" => "[#TENANTSLUG#]",
        "nome" => "[#TITULO#]",
        "host" => "editor.[#TENANTSLUG#].lettore.com.br",

        "api_login" => "<EMAIL>",
        "api_senha" => "0d1cfb9187807af6683704c920a23239bf94aca6",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#286a3c",
            "secondary_color" => "#33904f",
            "text_color"       => "#FFFFFF"
        ),
        # Data Base
        "mysql" => array(
            "host" => "*********",
            "port" => 3306,
            "user" => "jurid",
            "pass" => 'IWthc2htaXIkcyE=',
           "base" => "[#TENANTSLUG#]_ebooks_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/[#TENANTSLUG#]/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/[#TENANTSLUG#]/"
    ),

    #NOVOTENANT

