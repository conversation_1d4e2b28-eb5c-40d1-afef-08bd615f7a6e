<?php

/**
 * Classe para gerenciar sessões no Swoole usando Redis
 */
class SwooleSession {
    private $redis;
    private $sessionId;
    private $sessionName = 'PHPSESSID';
    private $data = [];
    private $isStarted = false;
    
    public function __construct() {
        $this->redis = new Redis();
        $this->redis->connect('lettore-redis', 6379);
    }
    
    /**
     * Inicia a sessão
     */
    public function start($response = null) {
        if ($this->isStarted) {
            return true;
        }
        
        // Verificar se há cookie de sessão
        $this->sessionId = $_COOKIE[$this->sessionName] ?? null;
        
        if (!$this->sessionId) {
            // Gerar novo ID de sessão
            $this->sessionId = $this->generateSessionId();
            
            // Enviar cookie se response estiver disponível
            if ($response) {
                $response->cookie($this->sessionName, $this->sessionId, time() + 3600, '/', '', false, true);
            }
        }
        
        // Carregar dados da sessão do Redis
        $this->loadSessionData();
        
        $this->isStarted = true;
        return true;
    }
    
    /**
     * Gera um novo ID de sessão
     */
    private function generateSessionId() {
        return bin2hex(random_bytes(16));
    }
    
    /**
     * Carrega dados da sessão do Redis
     */
    private function loadSessionData() {
        $key = "session:" . $this->sessionId;
        $data = $this->redis->get($key);
        
        if ($data) {
            $this->data = unserialize($data);
        } else {
            $this->data = [];
        }
    }
    
    /**
     * Salva dados da sessão no Redis
     */
    public function save() {
        if (!$this->isStarted) {
            return false;
        }
        
        $key = "session:" . $this->sessionId;
        $serialized = serialize($this->data);
        
        // Salvar com TTL de 1 hora
        $this->redis->setex($key, 3600, $serialized);
        
        return true;
    }
    
    /**
     * Define um valor na sessão
     */
    public function set($key, $value) {
        $this->data[$key] = $value;
    }
    
    /**
     * Obtém um valor da sessão
     */
    public function get($key, $default = null) {
        return $this->data[$key] ?? $default;
    }
    
    /**
     * Verifica se uma chave existe na sessão
     */
    public function has($key) {
        return isset($this->data[$key]);
    }
    
    /**
     * Remove uma chave da sessão
     */
    public function unset($key) {
        unset($this->data[$key]);
    }
    
    /**
     * Obtém todos os dados da sessão
     */
    public function getData() {
        return $this->data;
    }
    
    /**
     * Define todos os dados da sessão
     */
    public function setData($data) {
        $this->data = $data;
    }
    
    /**
     * Destrói a sessão
     */
    public function destroy() {
        if (!$this->isStarted) {
            return false;
        }
        
        $key = "session:" . $this->sessionId;
        $this->redis->del($key);
        
        $this->data = [];
        $this->sessionId = null;
        $this->isStarted = false;
        
        return true;
    }
    
    /**
     * Obtém o ID da sessão
     */
    public function getId() {
        return $this->sessionId;
    }
    
    /**
     * Obtém o nome da sessão
     */
    public function getName() {
        return $this->sessionName;
    }
}
