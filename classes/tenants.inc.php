<?php

$TENANTS_CONFIG = array(
     "arraeseditores" => array(
        # Descricao
        "tenant" => "arraeseditores",
        "nome" => "Arraes Editores",
        "host" => "editor.arraeseditores.lettore.com.br",

	"api_login" => "admin",
	"api_senha" => "j.u.r.1.d..4.d..m.1.n...",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#000000",
            "secondary_color" => "#dc9814",
            "text_color"       => "#FFFFFF"
        ),
        # Data Base
        "mysql" => array(
            "host" => "rds-camus-prod.cjcp55kirbrs.us-east-1.rds.amazonaws.com",
            "port" => 3306,
            "user" => "lettore",
            "pass" => "JXtxdccEU21",
            "base" => "arraeseditores_ebooks_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/arraeseditores/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/arraeseditores/"
    ),
    "hexag" => array(
        # Descricao
        "tenant" => "hexag",
        "nome" => "E-books Hexag",
        "host" => "editor.hexag.lettore.com.br",

	"api_login" => "<EMAIL>",
	"api_senha" => "0d1cfb9187807af6683704c920a23239bf94aca6",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#286a3c",
            "secondary_color" => "#33904f",
            "text_color"       => "#FFFFFF"
        ),
        # Data Base
        "mysql" => array(
            "host" => "rds-camus-prod.cjcp55kirbrs.us-east-1.rds.amazonaws.com",
            "port" => 3306,
            "user" => "lettore",
            "pass" => "JXtxdccEU21",
           "base" => "hexag_ebooks_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/hexag/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/hexag/"
    ),
    "vanguarda" => array(
        # Descricao
        "tenant" => "vanguarda",
        "nome" => "Revista Vanguarda Jurídica",
        "host" => "revista.vanguardajuridica.com.br",

	"api_login" => "<EMAIL>",
	"api_senha" => "0d1cfb9187807af6683704c920a23239bf94aca6",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#146b04",
            "secondary_color" => "#2879b0",
            "text_color"       => "#FFFFFF"
        ),

        # Data Base
        "mysql" => array(
            "host" => "********",
            "port" => 3306,
            "user" => "root",
            "pass" => "anVyMWRkNHQ0YjRzMw==",
            "base" => "vanguarda_ebooks_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/vanguardajuridica/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/vanguardajuridica/"
    ),
    "ltr" => array(
        # Descricao
        "tenant" => "ltr",
        "nome" => "E-books LTr",
        "host" => "editor.ltr.lettore.com.br",
        "host" => "editor.ltr.codell.com.br",

	"api_login" => "admin",
	"api_senha" => "3..l.3.t.3.3.r.r.3...4.d.1.m...",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#7b0a09",
            "secondary_color" => "#680706",
            "text_color"       => "#FFFFFF"
        ),

        # Data Base
        "mysql" => array(
            "host" => "rds-camus-prod.cjcp55kirbrs.us-east-1.rds.amazonaws.com",
            "port" => 3306,
            "user" => "lettore",
            "pass" => "JXtxdccEU21",
            "base" => "wool_montagem_ltr",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/ltr/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/ltr/"
    ),

    "intersaberes" => array(
        # Descricao
        "tenant" => "intersaberes",
        "nome" => "Intersaberes E-books",
        "host" => "editor.intersaberes.lettore.com.br",

	"api_login" => "jurid.ebook",
	"api_senha" => "7c4a8d09ca3762af61e59520943dc26494f8941b",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#99242d",
            "secondary_color" => "#7b141c",
            "text_color"       => "#FFFFFF"
        ),

        # Data Base
        "mysql" => array(
            "host" => "********",
            "port" => 3306,
            "user" => "root",
            "pass" => "anVyMWRkNHQ0YjRzMw==",
            "base" => "intersaberes_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/intersaberes/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/intersaberes/"
    ),

    "camplearning" => array(
        # Descricao
        "tenant" => "camplearning",
        "nome" => "Camplearning E-books",
        "host" => "editor.camplearning.lettore.com.br",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#1c7097",
            "secondary_color" => "#175f80",
            "text_color"       => "#FFFFFF"
        ),

        # Data Base
        "mysql" => array(
            "host" => "********",
            "port" => 3306,
            "user" => "root",
            "pass" => "anVyMWRkNHQ0YjRzMw==",
            "base" => "intersaberes_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/camplearning/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/camplearning/"
    ),

    "jurid" => array(
        # Descricao
        "tenant" => "jurid",
        "nome" => "Jurid E-books",
        "host" => "editor.jurid.lettore.com.br:8080",

	    "api_login" => "admin",
	    "api_senha" => "j.u.r.1.d..4.d..m.1.n...",

        
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#308ccb",
            "secondary_color" => "#2879b0",
            "text_color"       => "#FFFFFF"
        ),

        # Data Base
        "mysql" => array(
            "host" => "lettore-db",
            "port" => 3306,
            "user" => "lettore_user",
            "pass" => "lettore_password",
            "base" => "lettore_dev",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/jurid/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/jurid/"
    ),

    "casamayor" => array(
        # Descricao
        "tenant" => "casamayor",
        "nome" => "Editora Casa Mayor",
        "host" => "editor.casamayor.lettore.com.br",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#99242d",
            "secondary_color" => "#7f0404",
            "text_color"       => "#FFFFFF"
        ),

        # Data Base
        "mysql" => array(
            "host" => "********",
            "port" => 3306,
            "user" => "root",
            "pass" => "anVyMWRkNHQ0YjRzMw==",
            "base" => "casamayor_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => false,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/casamayor/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/casamayor/"
    ),

    "voxlex" => array(
        # Descricao
        "tenant" => "voxlex",
        "nome" => "VOXLEX",
        "host" => "editor.voxlex.lettore.com.br",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#EF9B11",
            "secondary_color" => "#E77817",
            "text_color"       => "#4D4948"
        ),

        # Data Base
        "mysql" => array(
            "host" => "********",
            "port" => 3306,
            "user" => "root",
            "pass" => "anVyMWRkNHQ0YjRzMw==",
            "base" => "voxlex_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => false,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/voxlex/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/voxlex/"
    ),

    "zakarewicz" => array(
        # Descricao
        "tenant" => "zakarewicz",
        "nome" => "Zakarewicz",
        "host" => "editor.zakarewicz.lettore.com.br",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#ce171f",
            "secondary_color" => "#a40f15",
            "text_color"      => "#ffffff"
        ),

        # Data Base
        "mysql" => array(
            "host" => "********",
            "port" => 3306,
            "user" => "root",
            "pass" => "anVyMWRkNHQ0YjRzMw==",
            "base" => "zakarewicz_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => false,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/zakarewicz/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/zakarewicz/"
    ),


    "cpiuris" => array(
        # Descricao
        "tenant" => "cpiuris",
        "nome" => "cpiuris E-books",
        "host" => "editor.cpiuris.lettore.com.br",

	"api_login" => "<EMAIL>",
	"api_senha" => "0d1cfb9187807af6683704c920a23239bf94aca6",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#308ccb",
            "secondary_color" => "#2879b0",
            "text_color"       => "#FFFFFF"
        ),

        # Data Base
        "mysql" => array(
            "host" => "rds-camus-prod.cjcp55kirbrs.us-east-1.rds.amazonaws.com",
            "port" => 3306,
            "user" => "lettore",
            "pass" => "JXtxdccEU21",
            "base" => "cpiuris_ebooks_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/cpiuris/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/cpiuris/"
    ),

    "testtnt" => array(
        # Descricao
        "tenant" => "testtnt",
        "nome" => "Venturoli E-books",
        "host" => "editor.venturoli.lettore.com.br",

	"api_login" => "admin",
	"api_senha" => "j.u.r.1.d..4.d..m.1.n...",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#308ccb",
            "secondary_color" => "#2879b0",
            "text_color"       => "#FFFFFF"
        ),

        # Data Base
        "mysql" => array(
            "host" => "rds-camus-prod.cjcp55kirbrs.us-east-1.rds.amazonaws.com",
            "port" => 3306,
            "user" => "lettore",
            "pass" => "JXtxdccEU21",
            "base" => "testtnt_ebooks_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/testtnt/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/testtnt/"
    ),
    "noeses" => array(
        # Descricao
        "tenant" => "noeses",
        "nome" => "Editora Noeses",
        "host" => "editor.noeses.lettore.com.br",

	"api_login" => "<EMAIL>",
	"api_senha" => "0d1cfb9187807af6683704c920a23239bf94aca6",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#308ccb",
            "secondary_color" => "#2879b0",
            "text_color"       => "#FFFFFF"
        ),

        # Data Base
        "mysql" => array(
            "host" => "rds-camus-prod.cjcp55kirbrs.us-east-1.rds.amazonaws.com",
            "port" => 3306,
            "user" => "lettore",
            "pass" => "JXtxdccEU21",
            "base" => "noeses_ebooks_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/noeses/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/noeses/"
    ),

    "rtm" => array(
        # Descricao
        "tenant" => "rtm",
        "nome" => "Editora RTM",
        "host" => "editor.rtm.lettore.com.br",

	"api_login" => "<EMAIL>",
	"api_senha" => "0d1cfb9187807af6683704c920a23239bf94aca6",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#308ccb",
            "secondary_color" => "#2879b0",
            "text_color"       => "#FFFFFF"
        ),

        # Data Base
        "mysql" => array(
            "host" => "rds-camus-prod.cjcp55kirbrs.us-east-1.rds.amazonaws.com",
            "port" => 3306,
            "user" => "lettore",
            "pass" => "JXtxdccEU21",
            "base" => "rtm_ebooks_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/rtm/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/rtm/"
    ),

    "gz" => array(
        # Descricao
        "tenant" => "gz",
        "nome" => "gz E-books",
        "host" => "editor.gz.lettore.com.br",

	"api_login" => "admin",
	"api_senha" => "j.u.r.1.d..4.d..m.1.n...",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#308ccb",
            "secondary_color" => "#2879b0",
            "text_color"       => "#FFFFFF"
        ),

        # Data Base
        "mysql" => array(
            "host" => "rds-camus-prod.cjcp55kirbrs.us-east-1.rds.amazonaws.com",
            "port" => 3306,
            "user" => "lettore",
            "pass" => "JXtxdccEU21",
           "base" => "gz_ebooks_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/gz/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/gz/"
    ),

    "biblos" => array(
        # Descricao
        "tenant" => "biblos",
        "nome" => "Bibliotek",
        "host" => "editor.biblos.lettore.com.br",

	"api_login" => "<EMAIL>",
	"api_senha" => "0d1cfb9187807af6683704c920a23239bf94aca6",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
		"primary_color"   => "#88b0bf",
            "secondary_color" => "#2b697e",
            "text_color"       => "#134152"
        ),

        # Data Base
        "mysql" => array(
            "host" => "rds-camus-prod.cjcp55kirbrs.us-east-1.rds.amazonaws.com",
            "port" => 3306,
            "user" => "lettore",
            "pass" => "JXtxdccEU21",
            "base" => "biblos_ebooks_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/biblos/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/biblos/"
    ),

    
    "mizuno" => array(
        # Descricao
        "tenant" => "mizuno",
        "nome" => "mizuno",
        "host" => "editor.mizuno.lettore.com.br",

        "api_login" => "admin",
	"api_senha" => "j.u.r.1.d..4.d..m.1.n...",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#286a3c",
            "secondary_color" => "#33904f",
            "text_color"       => "#FFFFFF"
        ),
        # Data Base
        "mysql" => array(
            "host" => "rds-camus-prod.cjcp55kirbrs.us-east-1.rds.amazonaws.com",
            "port" => 3306,
            "user" => "lettore",
            "pass" => "JXtxdccEU21",
           "base" => "mizuno_ebooks_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/mizuno/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/mizuno/"
    ),

    
    "perto" => array(
        # Descricao
        "tenant" => "perto",
        "nome" => "perto",
        "host" => "editor.perto.lettore.com.br",

        "api_login" => "<EMAIL>",
        "api_senha" => "0d1cfb9187807af6683704c920a23239bf94aca6",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#286a3c",
            "secondary_color" => "#33904f",
            "text_color"       => "#FFFFFF"
        ),
        # Data Base
        "mysql" => array(
            "host" => "rds-camus-prod.cjcp55kirbrs.us-east-1.rds.amazonaws.com",
            "port" => 3306,
            "user" => "lettore",
            "pass" => "JXtxdccEU21",
           "base" => "perto_ebooks_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/perto/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/perto/"
    ),

    
    "rtx" => array(
        # Descricao
        "tenant" => "rtx",
        "nome" => "rtx",
        "host" => "editor.rtx.lettore.com.br",

        "api_login" => "<EMAIL>",
        "api_senha" => "0d1cfb9187807af6683704c920a23239bf94aca6",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#286a3c",
            "secondary_color" => "#33904f",
            "text_color"       => "#FFFFFF"
        ),
        # Data Base
        "mysql" => array(
            "host" => "rds-camus-prod.cjcp55kirbrs.us-east-1.rds.amazonaws.com",
            "port" => 3306,
            "user" => "lettore",
            "pass" => "JXtxdccEU21",
           "base" => "rtx_ebooks_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/rtx/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/rtx/"
    ),

    
    "freitasbastos" => array(
        # Descricao
        "tenant" => "freitasbastos",
        "nome" => "freitasbastos",
        "host" => "editor.freitasbastos.lettore.com.br",

        "api_login" => "<EMAIL>",
        "api_senha" => "0d1cfb9187807af6683704c920a23239bf94aca6",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#286a3c",
            "secondary_color" => "#33904f",
            "text_color"       => "#FFFFFF"
        ),
        # Data Base
        "mysql" => array(
            "host" => "rds-camus-prod.cjcp55kirbrs.us-east-1.rds.amazonaws.com",
            "port" => 3306,
            "user" => "lettore",
            "pass" => "JXtxdccEU21",
           "base" => "freitasbastos_ebooks_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/freitasbastos/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/freitasbastos/"
    ),

    
    "lexmagister" => array(
        # Descricao
        "tenant" => "lexmagister",
        "nome" => "lexmagister",
        "host" => "editor.lexmagister.lettore.com.br",

        "api_login" => "<EMAIL>",
        "api_senha" => "0d1cfb9187807af6683704c920a23239bf94aca6",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#286a3c",
            "secondary_color" => "#33904f",
            "text_color"       => "#FFFFFF"
        ),
        # Data Base
        "mysql" => array(
            "host" => "rds-camus-prod.cjcp55kirbrs.us-east-1.rds.amazonaws.com",
            "port" => 3306,
            "user" => "lettore",
            "pass" => "JXtxdccEU21",
            "base" => "lexmagister_ebooks_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/lexmagister/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/lexmagister/"
    ),

    
    "sandbox2" => array(
        # Descricao
        "tenant" => "sandbox2",
        "nome" => "sandbox2",
        "host" => "editor.sandbox2.lettore.com.br",

        "api_login" => "<EMAIL>",
        "api_senha" => "0d1cfb9187807af6683704c920a23239bf94aca6",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#286a3c",
            "secondary_color" => "#33904f",
            "text_color"       => "#FFFFFF"
        ),
        # Data Base
        "mysql" => array(
            "host" => "rds-camus-prod.cjcp55kirbrs.us-east-1.rds.amazonaws.com",
            "port" => 3306,
            "user" => "lettore",
            "pass" => "JXtxdccEU21",
           "base" => "sandbox2_ebooks_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/sandbox2/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/sandbox2/"
    ),

    
    "legis" => array(
        # Descricao
        "tenant" => "legis",
        "nome" => "legis",
        "host" => "editor.legis.lettore.com.br",

        "api_login" => "<EMAIL>",
        "api_senha" => "0d1cfb9187807af6683704c920a23239bf94aca6",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#286a3c",
            "secondary_color" => "#33904f",
            "text_color"       => "#FFFFFF"
        ),
        # Data Base
        "mysql" => array(
            "host" => "rds-camus-prod.cjcp55kirbrs.us-east-1.rds.amazonaws.com",
            "port" => 3306,
            "user" => "lettore",
            "pass" => "JXtxdccEU21",
           "base" => "legis_ebooks_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/legis/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/legis/"
    ),

    
    "ebramec" => array(
        # Descricao
        "tenant" => "ebramec",
        "nome" => "ebramec",
        "host" => "editor.ebramec.lettore.com.br",

        "api_login" => "<EMAIL>",
        "api_senha" => "0d1cfb9187807af6683704c920a23239bf94aca6",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#286a3c",
            "secondary_color" => "#33904f",
            "text_color"       => "#FFFFFF"
        ),
        # Data Base
        "mysql" => array(
            "host" => "rds-camus-prod.cjcp55kirbrs.us-east-1.rds.amazonaws.com",
            "port" => 3306,
            "user" => "lettore",
            "pass" => "JXtxdccEU21",
           "base" => "ebramec_ebooks_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/ebramec/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/ebramec/"
    ),

    
    "noeses_test" => array(
        # Descricao
        "tenant" => "noeses_test",
        "nome" => "noeses_test",
        "host" => "editor.noeses_test.lettore.com.br",

        "api_login" => "<EMAIL>",
        "api_senha" => "0d1cfb9187807af6683704c920a23239bf94aca6",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#286a3c",
            "secondary_color" => "#33904f",
            "text_color"       => "#FFFFFF"
        ),
        # Data Base
        "mysql" => array(
            "host" => "rds-camus-prod.cjcp55kirbrs.us-east-1.rds.amazonaws.com",
            "port" => 3306,
            "user" => "lettore",
            "pass" => "JXtxdccEU21",
           "base" => "noeses_test_ebooks_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/noeses_test/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/noeses_test/"
    ),

    
    "noeses_test" => array(
        # Descricao
        "tenant" => "noeses_test",
        "nome" => "Noeses Homolog",
        "host" => "editor.noeses_test.lettore.com.br",

        "api_login" => "<EMAIL>",
        "api_senha" => "0d1cfb9187807af6683704c920a23239bf94aca6",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#286a3c",
            "secondary_color" => "#33904f",
            "text_color"       => "#FFFFFF"
        ),
        # Data Base
        "mysql" => array(
            "host" => "rds-camus-prod.cjcp55kirbrs.us-east-1.rds.amazonaws.com",
            "port" => 3306,
            "user" => "lettore",
            "pass" => "JXtxdccEU21",
           "base" => "noeses_test_ebooks_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/noeses_test/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/noeses_test/"
    ),
    "lacier" => array(
        # Descricao
        "tenant" => "lacier",
        "nome" => "Lacier",
        "host" => "editor.lacier.lettore.com.br",

        "api_login" => "<EMAIL>",
        "api_senha" => "94d521c943526f1896179e8be7a7d7e428ee7af7",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#286a3c",
            "secondary_color" => "#33904f",
            "text_color"       => "#FFFFFF"
        ),
        # Data Base
        "mysql" => array(
            "host" => "rds-camus-prod.cjcp55kirbrs.us-east-1.rds.amazonaws.com",
            "port" => 3306,
            "user" => "lettore",
            "pass" => "JXtxdccEU21",
            "base" => "lacier_ebooks_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/lacier/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/lacier/"
    ),
     "ibragesp" => array(
        # Descricao
        "tenant" => "ibragesp",
        "nome" => "Ibragesp",
        "host" => "editor.ibragesp.lettore.com.br",

        "api_login" => "admin",
        "api_senha" => "j.u.r.1.d..4.d..m.1.n...",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#286a3c",
            "secondary_color" => "#33904f",
            "text_color"       => "#FFFFFF"
        ),
        # Data Base
        "mysql" => array(
            "host" => "rds-camus-prod.cjcp55kirbrs.us-east-1.rds.amazonaws.com",
            "port" => 3306,
            "user" => "lettore",
            "pass" => "JXtxdccEU21",
            "base" => "ibragesp_ebooks_montagem",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/ibragesp/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/ibragesp/"
     ),
     "juridmais" => array(
        # Descricao
        "tenant" => "juridmais",
        "nome" => "Jurid mais",
        "host" => "editor.juridmais.lettore.com.br",

        "api_login" => "admin",
        "api_senha" => "j.u.r.1.d..4.d..m.1.n...",

        # Customização
        "style" => array(
            # Cores basicas da tenant, nao usamos hoje, mas um dia geraremos o css
            # baseado nessas coras, e ai o item acima sai também
            "primary_color"   => "#286a3c",
            "secondary_color" => "#33904f",
            "text_color"       => "#FFFFFF"
        ),
        # Data Base - configuração para desenvolvimento
        "mysql" => array(
            "host" => "lettore-db",
            "port" => 3306,
            "user" => "lettore_user",
            "pass" => "lettore_password",
            "base" => "lettore_dev",
            "tratar_aspas" => False,
        ),

        # Paths e JBE
        "porcentagem_desconto" => 0,
        "gerar_jbe" => true,
        "path_arquivos_jbe" => ROOT_SYS."/obras_montadas/juridmais/",
        "path_arquivos_idx" => ROOT_SYS."/indices_montados/juridmais/"
     ),
    #NOVOTENANT



















);

/*
 * Função usada para encontrar a tenant correta de acordo com
 * o domínio chamado pelo usuário.
 * Ricardo Falasca - 12.11.2014
 */
function encontra_tenant($dominio, $nome_tenant = '')
{
     global $TENANTS_CONFIG;

     $tenant_correta = null;

     function valida_dominio_tenant($dominio, $tenant, $ignore = true)
     {
          if (!isset($tenant['dominios']) && $ignore)
               return $tenant;

          # verifico se é o domínio principal
          if (isset($tenant['dominios']))
          {
               if (strcmp(mb_strtolower($dominio), mb_strtolower($tenant['dominios']['principal'])) == 0)
               {
                    return $tenant;
                    # break;
               }
               else
               {
                    # vou verificar todos os aliases cadastrados, qualquer um deles, fará
                    # um redirecionamento para o domínio principal.

                    if (isset($tenant['dominios']['aliases']))
                    {
                         foreach($tenant['dominios']['aliases'] as $alias)
                         {
                              if (strcmp(mb_strtolower($dominio), mb_strtolower($alias)) == 0)
                              {
                                   header("Location: http://" . $tenant['dominios']['principal']);
                                   die();
                              }
                         }

                    }
               }
          }
          else
          {
               if (isset($tenant['host']))
               {
                    if (strcmp(mb_strtolower($dominio), mb_strtolower($tenant['host'])) == 0)
                    {
                         return $tenant;
                         # break;
                    }
               }
          }
     }




     # se passou diretamente o nome da tenant,
     # vamos testar se existe configurações para ela.
     if (isset($TENANTS_CONFIG[trim($nome_tenant)]))
     {
          $tenant_correta = valida_dominio_tenant($dominio, $TENANTS_CONFIG[trim($nome_tenant)]);

          if (is_array($tenant_correta))
               return $tenant_correta;
     }

     foreach($TENANTS_CONFIG as $tenant)
     {
          $tenant_correta = valida_dominio_tenant($dominio, $tenant, false);

          if (is_array($tenant_correta))
               return $tenant_correta;
     }

     return null;
}

?>

