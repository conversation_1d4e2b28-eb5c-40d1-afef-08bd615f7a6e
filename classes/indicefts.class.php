<?php

require_once("conecta.class.php");
require_once("simplecrypt.class.php");

/*
 * Classe ArquivoIndiceFts
 * --
 * Gera um arquivo de índice para pesquisa Full Text (FTS)
 * em SQLite3 com conteúdo codificado.
 */
class ArquivoIndiceFts extends conecta 
{

    private $_idebook;
    private $_compactar;
    private $_tenant;

    function __construct($link = NULL, $tenant = NULL, $idEbook = 0, $compactarArquivo = true)
    {
$fp = fopen("debug.ArquivoIndiceFTS.log", "a");
fwrite($fp, "\n------------".date("H:i:s"));
        global $TENANT;

        parent::__construct($link, $tenant);
fwrite($fp, "\nPassando no construct...");
        $this->_idebook = $idEbook;
        $this->_compactar = $compactarArquivo;
        $this->_tenant = $TENANT;

fwrite($fp, "\nVai executar geraArquivo...");
        $this->geraArquivo();

fwrite($fp, "\nPassou da funcao geraArquivo...");
        if ($compactarArquivo)
            $this->compactaArquivo();
fwrite($fp, "\nPassou da funcao compactaArquivo... terminando construct");
fclose($fp);
    }

    function __destruct()
    {
        parent::__destruct();
    }

    private function removeAcentuacao($texto)
    {
        # Removendo os acentos
        $trocarIsso = array('à','á','â','ã','ä','å','ç','è','é','ê','ë','ì','í','î','ï',
                            'ñ','ò','ó','ô','õ','ö','ù','ü','ú','ÿ','À','Á','Â','Ã','Ä',
                            'Å','Ç','È','É','Ê','Ë','Ì','Í','Î','Ï','Ñ','Ò','Ó','Ô','Õ',
                            'Ö','O','Ù','Ü','Ú','Ÿ');

        $porIsso = array('a','a','a','a','a','a','c','e','e','e','e','i','i','i','i',
                         'n','o','o','o','o','o','u','u','u','y','A','A','A','A','A',
                         'A','C','E','E','E','E','I','I','I','I','N','O','O','O','O',
                         'O','O','U','U','U','Y', '');

        $titletext = str_replace($trocarIsso, $porIsso, $texto);

        # colocando caixa-baixa
        return mb_strtolower($titletext);
    }

    private function limpaTexto($texto)
    {
        $texto = preg_replace('/[^A-Za-z0-9\/ ]/', 
                              '',
                              $this->removeAcentuacao(Encoding::toUTF8($texto)));

        $remover = array('no','na','nos','nas','em','de','des','dos','das',
                         'do','da','in','on','or','and','e','o','a','an',
                         'as','es','os','la','le','les','los','las','ou',
                         'n','ao','um','uma','uns','umas', 'me');

        $arr = explode(" ", $texto);

        $texto = '';
        foreach($arr as $palavra)
        {
            if (!in_array(trim($palavra), $remover))
                $texto .= " " . Codifica(trim($palavra), mb_strlen(trim($palavra))); 
                // . " " . Decodifica(Codifica(trim($palavra), mb_strlen(trim($palavra))));
        }

        return $texto;
    }

    private function parsePalavras($texto)
    {
        /*
            Modelo:
            --
            <div class="palavra" style="left: 721px; top: 30px; width: 58px; height: 28px;">NR-10</div>
            <div class="palavra" style="left: 259px; top: 124px; width: 114px; height: 24px;">SEGURAN?A</div>
        */
        $texto = str_ireplace("</div><div", 
                              "</div>\n<div",
                              $texto);

        preg_match_all('!<div([^>]*)class="palavra"([^>]*)>([^<\/].+)</div>!',
                        $texto,
                        $matches);

        $palavras = '';

        if ((isset($matches[3])) && (count($matches[3]) > 0))
            foreach($matches[3] as $p)
            {
                $palavras .= $this->limpaTexto(sprintf(" %s", str_ireplace("</div>", "", $p)));
            }

        $palavras = preg_replace("/\s+/", " ", $palavras);

        return Encoding::toLatin1(trim($palavras));
    }

    private function compactaArquivo()
    {
        $arquivoOrigem = sprintf("%s%d.dat",
                                 $this->_tenant['path_arquivos_idx'],
                                 $this->_idebook);

        $arquivoDestino = sprintf("%s%d.zip",
                                 $this->_tenant['path_arquivos_idx'],
                                 $this->_idebook);

        $zip = new ZipArchive();

        if ($zip->open($arquivoDestino, ZIPARCHIVE::CREATE | ZIPARCHIVE::CM_REDUCE_4) !== true)
        {
            die("Não foi possível abrir o arquivo {$arquivoDestino}");
        }


        $zip->addFile($arquivoOrigem);
        $zip->close();
        rename($arquivoDestino, str_ireplace(".zip", ".idx", $arquivoDestino));
        unlink($arquivoOrigem);
    }

    private function geraArquivo()
    {
$fp = fopen("debug.geraArquivo.log","a");
fwrite($fp, "\n---------".date("H:i:s"));
fwrite($fp, "\nEntrando na funcao geraArquivo");
        if (!file_exists($this->_tenant['path_arquivos_idx']))
            mkdir($this->_tenant['path_arquivos_idx'], 0777, true);
fwrite($fp, "\nPassou pelo mkdir");

        $arquivo_sqlite3 = sprintf("%s%d.dat",
                                    $this->_tenant['path_arquivos_idx'],
                                    $this->_idebook);
fwrite($fp, "\nGerou nomedoarquivo sqlite3: (".$arquivo_sqlite3.")");

        # excluindo arquivo de destino caso já exista um anterior.
        if (file_exists($arquivo_sqlite3)) {
	    fwrite($fp, "\nEntrou no unlink, apagando arquivo (".$arquivo_sqlite3.")");
            unlink($arquivo_sqlite3);
	}
fwrite($fp, "\nPassou o unlink...(".$this->_idebook.")");

        $paginas = array();

	$qryStr = sprintf("SELECT numeropagina,texto FROM paginas WHERE (idobra = %d) ORDER BY numeropagina ASC", $this->_idebook);
fwrite($fp, "\nMontando query para executar...(".$qryStr.")");

	$resultado = $this->query($qryStr);
fwrite($fp, "\nExecutou a query...");
/***
        $resultado = $this->query(sprintf("SELECT numeropagina,texto FROM paginas " . 
                                  " WHERE (idobra = %d) " . 
                                  " ORDER BY numeropagina ASC", $this->_idebook));
***/

fwrite($fp, "\nPassou a query numeropagina,texto...");

        if ($resultado->result())
        {
fwrite($fp, "\nEntrando no result...");
            $sqlite = new SQLite3($arquivo_sqlite3);
            if ($sqlite)
                $sqlite->exec("CREATE VIRTUAL TABLE " . 
                              " IF NOT EXISTS e{$this->_idebook} USING FTS4 " . 
                              "(p INTEGER NOT NULL DEFAULT 0, " . 
                              " t TEXT COLLATE translit_ascii)");
            else
                die("Falha ao criar arquivo Sqlite3.");

fwrite($fp, "\nCriou arquivo sqlite...");
            foreach($resultado->fetch() as $l)
            {
                $sqlite->exec(sprintf("INSERT INTO e%d VALUES (%d, '%s')",
                                    $this->_idebook, 
                                    $l['numeropagina'],
                                    $this->parsePalavras($l['texto'])));

            }
fwrite($fp, "\nInseriu dados no arquivo sqlite...");
        }
fwrite($fp, "\nPassou a geracao do sqlite...");
fclose($fp);
        $sqlite->close();


        #printf("Índice do e-book [%d] gerado com sucesso.\n", $this->_idebook);
    }

}

?>
