<?php

require_once("conecta.class.php");

class Login extends Conecta {

  public $usuario = null;
  public $senha = null;

  function __construct() {

    parent::__construct();
  }

  function __destruct() {
    parent::__destruct();
  }

  public function setUsuario($usuario) {
    $this->usuario = $usuario;
  }

  public function setSenha($senha) {
    $this->senha = sha1($senha);
  }

  public function getUsuario($usuario) {
    $this->usuario = $usuario;
  }

  public function getSenha($senha) {
    $this->senha = $senha;
  }


  public function login(&$usuario = null) {
    # Busca o usuário no banco de dados
    $query = $this->query(sprintf("SELECT * FROM usuarios WHERE (usuario like '%s') AND ativo = 1", $this->usuario));
    if(!$query) return -1;

    # Verifica se teve 1 registro encontrado
    $rows = $query->rows();
    if(!$rows) return -2;

    # Pega o primeiro registro encontrado.
    $fetch = $query->fetch();
    $fetch = $fetch[0];

    $usuario = $fetch;

    $_SESSION["ULTIMO_USUARIO_AUTENTICADO"] = $fetch;

    # Compara o usuário e a senha passada, com o registro encontradoa através do login
    if( $fetch["senha"] == $this->senha ):
      $usuario = $fetch;
      return 0;
    endif;

    return -3;

  }

}

?>
