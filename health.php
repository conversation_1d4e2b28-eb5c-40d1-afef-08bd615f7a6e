<?php

/**
 * Health check endpoint para Docker
 */

header('Content-Type: application/json');

$health = [
    'status' => 'ok',
    'timestamp' => date('Y-m-d H:i:s'),
    'checks' => []
];

try {
    // Verificar conexão com banco de dados
    $dbHost = $_ENV['DB_HOST'] ?? 'lettorestage-db.cb0yc0yuuqot.us-west-2.rds.amazonaws.com';
    $dbUser = $_ENV['DB_USERNAME'] ?? 'admin';
    $dbPass = $_ENV['DB_PASSWORD'] ?? 'JASHDgvHAS%D$A412TFDSDASDJhgVADKJHASD';
    $dbPort = $_ENV['DB_PORT'] ?? 3306;
    
    $startTime = microtime(true);
    $connection = new mysqli($dbHost, $dbUser, $dbPass, 'information_schema', $dbPort);
    $dbTime = round((microtime(true) - $startTime) * 1000, 2);
    
    if ($connection->connect_error) {
        $health['checks']['database'] = [
            'status' => 'error',
            'message' => 'Connection failed: ' . $connection->connect_error,
            'response_time_ms' => $dbTime
        ];
        $health['status'] = 'error';
    } else {
        $health['checks']['database'] = [
            'status' => 'ok',
            'message' => 'Connected successfully',
            'response_time_ms' => $dbTime
        ];
        $connection->close();
    }
    
} catch (Exception $e) {
    $health['checks']['database'] = [
        'status' => 'error',
        'message' => $e->getMessage(),
        'response_time_ms' => 0
    ];
    $health['status'] = 'error';
}

try {
    // Verificar conexão com Redis
    $startTime = microtime(true);
    $redis = new Redis();
    $redis->connect('redis', 6379, 2); // timeout de 2 segundos
    $redisTime = round((microtime(true) - $startTime) * 1000, 2);
    
    $redis->ping();
    $health['checks']['redis'] = [
        'status' => 'ok',
        'message' => 'Connected successfully',
        'response_time_ms' => $redisTime
    ];
    $redis->close();
    
} catch (Exception $e) {
    $health['checks']['redis'] = [
        'status' => 'error',
        'message' => $e->getMessage(),
        'response_time_ms' => 0
    ];
    $health['status'] = 'error';
}

// Verificar espaço em disco
$diskFree = disk_free_space('/var/www/html');
$diskTotal = disk_total_space('/var/www/html');
$diskUsage = round((($diskTotal - $diskFree) / $diskTotal) * 100, 2);

$health['checks']['disk'] = [
    'status' => $diskUsage > 90 ? 'warning' : 'ok',
    'usage_percent' => $diskUsage,
    'free_bytes' => $diskFree,
    'total_bytes' => $diskTotal
];

// Verificar memória
$memoryUsage = memory_get_usage(true);
$memoryLimit = ini_get('memory_limit');
$memoryLimitBytes = $memoryLimit === '-1' ? PHP_INT_MAX : (int)$memoryLimit * 1024 * 1024;
$memoryPercent = round(($memoryUsage / $memoryLimitBytes) * 100, 2);

$health['checks']['memory'] = [
    'status' => $memoryPercent > 80 ? 'warning' : 'ok',
    'usage_percent' => $memoryPercent,
    'usage_bytes' => $memoryUsage,
    'limit_bytes' => $memoryLimitBytes
];

// Verificar diretórios importantes
$directories = [
    '/var/www/html/obras_montadas',
    '/var/www/html/indices_montados',
    '/var/www/html/capas_temp',
    '/var/www/html/downloads'
];

$directoriesOk = true;
foreach ($directories as $dir) {
    if (!is_dir($dir) || !is_writable($dir)) {
        $directoriesOk = false;
        break;
    }
}

$health['checks']['directories'] = [
    'status' => $directoriesOk ? 'ok' : 'error',
    'message' => $directoriesOk ? 'All directories accessible' : 'Some directories not accessible'
];

// Status HTTP baseado nos checks
$httpStatus = 200;
if ($health['status'] === 'error') {
    $httpStatus = 503; // Service Unavailable
} elseif (in_array('warning', array_column($health['checks'], 'status'))) {
    $httpStatus = 200; // OK mas com warnings
}

http_response_code($httpStatus);
echo json_encode($health, JSON_PRETTY_PRINT);
