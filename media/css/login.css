html { height: 100%; margin: 0; font-family: Arial, sans-serif; box-sizing: border-box;}

body { background: #fff; height: 100%; margin: 0; font-size:12px; box-sizing: border-box;}

  body a { text-decoration:none; color:#7790c6; }

.jb-login-bg {  background-color: #616161; height: 100%; }

 .align-center { text-align:center; margin:5px 0 30px;}

 .hide { display:none!important; }

/* FORM */

.jb-form-signin, .jb-novasenha
{
    padding: 15px;
    margin: 0 auto;
}
.jb-form-signin .jb-form-signin-heading, .jb-form-signin .checkbox
{
    margin-bottom: 10px;
}
.jb-form-signin .checkbox,
{
    font-weight: normal;
}
.jb-form-signin .form-control
{
    position: relative;
    font-size: 16px;
    height: auto;
    padding: 10px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.jb-form-signin .form-control:focus
{
    z-index: 2;
}

.jb-form-signin .jb-login-email { background: #FFF url('../img/login-user.png') no-repeat 9px 14px; border-radius: 4px; border: solid 1px #CCCCCC; box-shadow: none; height: 45px; letter-spacing: normal; line-height: 22px; margin-bottom: 15px; padding-left:30px!important; }

.jb-form-signin .jb-login-password { background: #FFF url('../img/login-password.png') no-repeat 9px 14px; border-radius: 4px; border: solid 1px #CCCCCC; box-shadow: none; height: 45px; letter-spacing: normal; line-height: 22px; margin-bottom: 15px; padding-left:30px!important; }

.jb-login-btn { margin-bottom:10px; background:#616161; box-shadow:none; border:0; padding:10px 20px; border-radius:4px; display: inline-block; width: auto;  height:45px; color:#FFFFFF; font-size:18px; }
.jb-login-btn:hover { background-color:#313131; -webkit-transition: all .4s; -moz-transition: all .4s; transition: all .4s; cursor: pointer; color:#FFFFFF;}

.jb-form-signin {
  padding: 15px;
  margin: 0 auto;
  padding-top: 10%;
}

.jb-form-signin .checkbox {
  font-weight: normal; float:left;

}.jb-form-signin .esqueceu-senha {
  font-weight: normal; float:right; padding-top:2px;
}

.jb-form-signin .form-control:focus {
  z-index: 2;
}
.jb-software-version { font-size:11px; text-align:center; color:#FFF; }

.jb-login-box { background: #FFF; border-radius: 5px; padding:40px 35px; width:370px; margin: 0 auto;}

  .jb-login-box .jb-title { padding-bottom:10px; }


/* ALERTAS */

.jb-error-red { border:solid 1px #ebccd1; padding:10px; border-radius:4px; color:#a94442; background:#f2dede; margin-bottom:20px; }

.jb-error-yellow { border:solid 1px #faebcc; padding:10px; border-radius:4px; color:#8a6d3b; background:#fcf8e3; margin-bottom:20px; }

.jb-error-green { border:solid 1px #d6e9c6; padding:10px; border-radius:4px; color:#3c763d; background:#dff0d8; margin-bottom:20px; }

.jb-error-blue { border:solid 1px #bce8f1; padding:10px; border-radius:4px; color:#31708f; background:#d9edf7; margin-bottom:20px; }

/* REQUISITOS TÉCNICOS */

.container { padding-top:30px; }

.jb-requisistos-tecnicos { background:#FFFFFF; margin:0 auto; padding:50px; border-radius:4px; height:100%; width:600px;  }

  .jb-requisistos-tecnicos h2 { font-size:16px; }

  .jb-requisistos-tecnicos p { font-size:14px; }

    .jb-requisistos-tecnicos-lista { font-size:14px; line-height:18px; }

.center-line {
   width: 100%; 
   text-align: center; 
   border-bottom: 1px solid rgba(255,255,255, 0.7); 
   line-height: 0.1em;
   margin: 10px 0 20px; 
   background-color: transparent;
}

.center-line span { 
    padding:0 10px; 
    background-color: #616161;
}


.requisitos-content {  
    font-family: 'Source Sans Pro', sans-serif;
    color: #FFF;
}

.requisitos-content a {
  font-weight: bold;
  font-size: 15px;
  color: #FFF;
}  

.requisitos-content h3  { 
  font-size: 18px;
  font-weight: 400;
  margin-bottom: 50px;
} 

.requisitos-content .container {
  min-height: 100vh;
}


.requisitos-content h4 {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 30px;
}


.requisitos-content .plataformas > div {
  margin-bottom: 100px;
  font-size: 13px;
  line-height: 22px;
}

.requisitos-content .recomendacoes {
 display: inline-block;
}

.glyphicon-refresh-animate {
    -animation: spin 1.4s infinite linear;
    -webkit-animation: spin2 1.4s infinite linear;
}


@-webkit-keyframes spin2 {
    from { -webkit-transform: rotate(0deg);}
    to { -webkit-transform: rotate(360deg);}
}

@keyframes spin {
    from { transform: scale(1) rotate(0deg);}
    to { transform: scale(1) rotate(360deg);}
}



