* { margin: 0; padding: 0; list-style: none; vertical-align: baseline; font-family: 'Source Sans Pro', sans-serif; }
body {  padding-bottom: 50px; }

a:hover, a:visited, a:focus { outline: none; text-decoration: none; }

/* HELPERS */
.main-title h2 {padding: 0 15px; font-size: 22px; color: #828282; margin: 30px 0 10px;}

.be-remove-margin { margin: 0 !important }
.be-remove-paddin { padding: 0 !important }

.be-margin-bottom-sm { margin-bottom: 30px; }
.be-margin-top-sm { margin-top: 15px; }

.be-margin-top-md { margin-top: 50px; }

.divisor-lg { border-bottom: 1px solid #e7e7e7; padding-bottom: 20px; margin-bottom: 20px; display: block; clear: both;}

.clear-both { clear: both !important; }

.opacity-2 {opacity: 0.2 !important; margin-bottom: 10px; }


form h4 { font-size: 14px; font-weight: bold; color: #666; border-bottom: 1px solid #e1e1e1; padding-bottom: 10px; margin-bottom: 15px;}
 .row.row-margin { margin-bottom: 15px;}
 .row.row-margin > div { margin-bottom: 15px; }
form .row { background-color: #f1f1f1; padding:10px 0px;}
form { font-size: 14px;  }


.label { margin-left: 5px; opacity: 0.5; cursor: pointer; }
.label.active { opacity: 1 }

.main-content {width: 100%; position: relative; padding-top: 20px; }

.main-header { 
        height:50px; 
        background: #616161; 
        -webkit-box-shadow: 0px 3px 25px 0px rgba(0,0,0,0.3);
        -moz-box-shadow: 0px 3px 25px 0px rgba(0,0,0,0.3);
        box-shadow: 0px 3px 25px 0px rgba(0,0,0,0.3);
        z-index: 1000;
    }

    .main-header h1 { margin: 0; line-height:50px;}

    .btn-access-ebooks { } 
    .btn-access-ebooks ul { margin: 0px; padding:0px;} 
    .btn-access-ebooks ul li { position: relative; margin: 0px; padding:0px; display: inline-block; line-height:50px; padding: 0px 15px; color: #FFF; cursor: pointer;} 
    .btn-access-ebooks ul li .badge { position: absolute; top: 25px; left: 30px; border: 2px solid #313131; background-color: #7b0a09; color: #FFF; } 
    .btn-access-ebooks ul li img { margin-right: 5px; } 
    .btn-access-ebooks ul li:hover { background-color: #313131; } 


    .btn-access-ebooks ul li > div { display: none;
                                     position: absolute;
                                     top: 100%;
                                     left: 0;
                                     width: 100%;
                                     line-height: 100%;
                                     background-color: #FFF;
                                     -webkit-box-shadow: 1px 0px 15px 2px rgba(0,0,0,0.1);
                                     -moz-box-shadow: 1px 0px 15px 2px rgba(0,0,0,0.1);
                                     box-shadow: 1px 0px 15px 2px rgba(0,0,0,0.1);
                                     border-radius: 0px 0px 3px 3px;
                                     border:1px solid #e1e1e1;
                                   }

    .btn-access-ebooks ul li:hover > div { display: block; }
    .btn-access-ebooks ul li > div a { display: block; padding:20px 15px; height: auto; color: #000; opacity: 0.5; font-size: 14px; border-bottom: 1px solid #d1d1d1; }
    .btn-access-ebooks ul li > div a:hover { opacity: 1; background-color: #f1f1f1; }
    .btn-access-ebooks ul li > div a .fa { margin-right: 5px; }

    .btn-access-ebooks ul li .menu-drop {}
    .btn-access-ebooks ul li:hover .menu-drop { display: block; }

    .btn-access-ebooks .menu-drop { line-height: 20px;  display: none; width: 300px;  position: absolute; background-color: #FFF; z-index: 9999; top: 100%; left: 0; padding: 0px; }
    
    .btn-access-ebooks .menu-drop ul { padding: 0px;
                                       margin: 0px;
                                       vertical-align: top;
                                     }

    .btn-access-ebooks .menu-drop ul li {   
                                            margin: 0px !important;
                                            line-height: normal;
                                            background-color: #f1f1f1;
                                            display: inline-block;
                                            min-height: 70px;
                                            width: 100%;
                                            color: #000;
                                            padding: 10px 15px 10px 50px; 
                                   }
    .btn-access-ebooks .menu-drop img { 
                                            width: 30px;
                                            height: auto;
                                            position: absolute;
                                            left: 15px
                                      }

.brandlogo { 
    line-height: 30px;
    padding: 10px;
}

.brandlogo img{ 
    margin: 0 auto;
}       

.progress { margin: 0px; height: 10px; }        


table tr th { vertical-align: middle !important;  font-size: 11px;}                
table tr td { vertical-align: middle !important; }     


    .be-pagination {  }
    .be-pagination ul { margin: 0px; padding: 0px; }
    .be-pagination ul li { display: inline-block; vertical-align: top; margin: 0 2px; cursor: pointer;}
    .be-pagination ul li { padding: 5px 10px; border: 1px solid #e1e1e1; color: #434343; display: inline-block;}
    .be-pagination ul li a { color: #434343; }
    .be-pagination ul li:not(.pag-active):hover { background: #e1e1e1; -webkit-transition: all .4s; -moz-transition: all .4s; transition: all .4s;}
    
    .be-pagination ul li.no-border { cursor: default; }
    .be-pagination ul li.no-border:hover { background: none; }

    .be-pagination ul li.disabled { color: #ccc; border-color: #e1e1e1; }
    .be-pagination ul li.disabled:hover { color: #ccc; border-color: #e1e1e1; cursor: default; background: none; }

    .btn-pag a {font-size: 13px !important; text-transform: uppercase; color: #434343; border: 1px solid #e1e1e1; background: #f9f9f9; padding: 10px 25px !important;}
    .btn-pag .fa {margin: 0 5px;}
    
    .pag-active { color: #666 !important; font-weight: 600; cursor: default !important; }



.filtros { margin-bottom: 15px; }


.pagina-obra {  
    text-align: center;
    position: absolute;
    left: 330px;
    top: 65px;
}

.pagina-obra #formato2 {  background-color: #FFF;   } 
.pagina-obra .palavra { position: absolute; background-color: #060; font-size: 10px; color: #fff; opacity: 0.8; z-index: 15;}
.pagina-obra .marcacao { position: absolute; background-color: #f0ad4e; font-size: 10px; color: #fff; opacity: 0.8; z-index: 15;}


.pagina-obra .nivel { position: absolute; background-color: #600; font-size: 14px; color: #fff; opacity: 0.8; z-index: 15;}


.pagina-obra .nivel.nivel1 {  background-color: #cc0018; }
.pagina-obra .nivel.nivel2 {  background-color: #cc0093; }
.pagina-obra .nivel.nivel3 {  background-color: #7e00cc; }
.pagina-obra .nivel.nivel4 {  background-color: #1000cc; }
.pagina-obra .nivel.nivel5 {  background-color: #00b4cc; }
.pagina-obra .nivel.nivel6 {  background-color: #00cc52; }
.pagina-obra .nivel.nivel7 {  background-color: #a3cc00; }
.pagina-obra .nivel.nivel8 {  background-color: #ccab00; }
.pagina-obra .nivel.nivel9 {  background-color: #cc6e00; }
.pagina-obra .nivel.nivel10 {  background-color: #dd55e7; }

.item-nivel1 { font-size: 1em;  background-color: #cc0018; display: inline-block; width: 16px; height: 16px; padding: 0px 2px; text-align: center; margin-right: 5px; }
.item-nivel2 { font-size: 1em;  background-color: #cc0093; display: inline-block; width: 16px; height: 16px; padding: 0px 2px; text-align: center; margin-right: 5px; }
.item-nivel3 { font-size: 1em;  background-color: #7e00cc; display: inline-block; width: 16px; height: 16px; padding: 0px 2px; text-align: center; margin-right: 5px; }
.item-nivel4 { font-size: 1em;  background-color: #1000cc; display: inline-block; width: 16px; height: 16px; padding: 0px 2px; text-align: center; margin-right: 5px; }
.item-nivel5 { font-size: 1em;  background-color: #00b4cc; display: inline-block; width: 16px; height: 16px; padding: 0px 2px; text-align: center; margin-right: 5px; }
.item-nivel6 { font-size: 1em;  background-color: #00cc52; display: inline-block; width: 16px; height: 16px; padding: 0px 2px; text-align: center; margin-right: 5px; }
.item-nivel7 { font-size: 1em;  background-color: #a3cc00; display: inline-block; width: 16px; height: 16px; padding: 0px 2px; text-align: center; margin-right: 5px; }
.item-nivel8 { font-size: 1em;  background-color: #ccab00; display: inline-block; width: 16px; height: 16px; padding: 0px 2px; text-align: center; margin-right: 5px; }
.item-nivel9 { font-size: 1em;  background-color: #dd55e7; display: inline-block; width: 16px; height: 16px; padding: 0px 2px; text-align: center; margin-right: 5px; }
.item-nivel10 { font-size: 1em;  background-color: #dd55e7; display: inline-block; width: 16px; height: 16px; padding: 0px 2px; text-align: center; margin-right: 5px; }

.pagina-obra .grifo { position: absolute; background-color: #060;  opacity: 0.3; z-index: 25;}

.pagina-obra img { width: 100%; height: 100%; z-index: 5; }

.pagina-obra.ocultar-palavra .palavra { display: none; }
.pagina-obra.ocultar-sumario .nivel { display: none; }
.pagina-obra.ocultar-marcacao .marcacao { display: none; }


.controles > div > div { margin-bottom: 15px;}
.controles > div > span {  padding: 12px 20px;}
.controles .titulo { font-size: 11px;  color: #fff; margin-bottom: 10px; display: inline-block;}


.controles-inside { 
    padding: 4px;
    padding: 15px;

}
.controles { 
             position: absolute;
             top: 65px;
             left: 15px;
             width: 300px;
             color: #FFF;
             text-align: center;
             margin-right: 15px;
             padding: 15px;
             background-color: #222;

          }

.controles hr { border-top-color: #444;}

.indice-sumario { font-size: 11px;}
.indice-sumario ul { list-style: inside; padding: 0px; list-style: none;     }
.indice-sumario ul li { border-bottom: 1px dotted #444; padding: 4px 0px; position: relative; padding-right: 14px; word-break: break-all; }
.indice-sumario ul li span:hover { color: #cc0018; cursor: pointer; }
.indice-sumario ul li span {  position: absolute; right: 0; top: 5px;}

@media (max-width: 1000px) {

    .controles { float: none; width: auto;}
    .controles hr { display: none;}
    .controles > div > div { float: none; width: auto; display: inline-block; margin-bottom: 0px;}
    .controles > div > div.detalhes { display: none;}
    .pagina-obra #formato2 {  background-color: #FFF; margin: 0 auto !important;  } 
    .indice-sumario { display: block !important; margin-top: 10px;}
}

.grey { background-color: #ccc; }

.glyphicon-refresh-animate {
    -animation: spin .7s infinite linear;
    -webkit-animation: spin2 .7s infinite linear;
}

@-webkit-keyframes spin2 {
    from { -webkit-transform: rotate(0deg);}
    to { -webkit-transform: rotate(360deg);}
}

@keyframes spin {
    from { transform: scale(1) rotate(0deg);}
    to { transform: scale(1) rotate(360deg);}
}

.acoes .btn { display: inline; font-size: 11px; margin-right: 2px;}
.acoes .btn .hover { display: none;}
.acoes .btn:hover .hover { display: inline;}


.sumario_1 { padding-left: 0px;}
.sumario_2 { padding-left: 15px;}
.sumario_3 { padding-left: 30px;}
.sumario_4 { padding-left: 45px;}
.sumario_5 { padding-left: 60px;}
.sumario_6 { padding-left: 75px;}
.sumario_7 { padding-left: 90px;}
.sumario_8 { padding-left: 105px;}
.sumario_9 { padding-left: 120px;}


.btn-cinza {
  background-color: #667c87;
  border-color: #667c87;
  color: #FFF;
  opacity: 0.5;
}



.btn-cinza:hover,
.btn-cinza:focus,
.btn-cinza:active,
.btn-cinza.active {
  background-color: #5b6f78;
  border-color: #50616a;
  color: #FFF;
  opacity: 1;
}
.btn-cinza.disabled:hover,
.btn-cinza.disabled:focus,
.btn-cinza.disabled:active,
.btn-cinza.disabled.active,
.btn-cinza[disabled]:hover,
.btn-cinza[disabled]:focus,
.btn-cinza[disabled]:active,
.btn-cinza[disabled].active,
fieldset[disabled] .btn-cinza:hover,
fieldset[disabled] .btn-cinza:focus,
fieldset[disabled] .btn-cinza:active,
fieldset[disabled] .btn-cinza.active {
  background-color: #667c87;
  border-color: #667c87;
}


.btn-marcacao.active { opacity: 0.3; }


.btn-sumario { padding: 0px 8px; display: inline-block; }
.btn-sumario1  { background-color: hsla(360, 100%, 20%, 1); }
.btn-sumario2  { background-color: hsla(360, 100%, 25%, 1); }
.btn-sumario3  { background-color: hsla(360, 100%, 30%, 1); }
.btn-sumario4  { background-color: hsla(360, 100%, 35%, 1); }
.btn-sumario5  { background-color: hsla(360, 100%, 40%, 1); }
.btn-sumario6  { background-color: hsla(360, 100%, 45%, 1); }
.btn-sumario7  { background-color: hsla(360, 100%, 50%, 1); }
.btn-sumario8  { background-color: hsla(360, 100%, 55%, 1); }
.btn-sumario9  { background-color: hsla(360, 100%, 60%, 1); }
.btn-sumario10 { background-color: hsla(360, 100%, 65%, 1); }
.btn-sumario11 { background-color: hsla(360, 100%, 70%, 1); }
.btn-sumario12 { background-color: hsla(360, 100%, 75%, 1); }
.btn-sumario13 { background-color: hsla(360, 100%, 78%, 1); }
.btn-sumario14 { background-color: hsla(360, 100%, 81%, 1); }
.btn-sumario15 { background-color: hsla(360, 100%, 85%, 1); }

.pagina-obra .nivel.nivel1  {  background-color: #cc0018; }
.pagina-obra .nivel.nivel2  {  background-color: #cc0093; }
.pagina-obra .nivel.nivel3  {  background-color: #7e00cc; }
.pagina-obra .nivel.nivel4  {  background-color: #1000cc; }
.pagina-obra .nivel.nivel5  {  background-color: #00b4cc; }
.pagina-obra .nivel.nivel6  {  background-color: #00cc52; }
.pagina-obra .nivel.nivel7  {  background-color: #a3cc00; }
.pagina-obra .nivel.nivel8  {  background-color: #ccab00; }
.pagina-obra .nivel.nivel9  {  background-color: #cc6e00; }
.pagina-obra .nivel.nivel10 {  background-color: #dd55e7; }
.pagina-obra .nivel.nivel11 {  background-color: #dd55e7; }
.pagina-obra .nivel.nivel12 {  background-color: #dd55e7; }
.pagina-obra .nivel.nivel13 {  background-color: #dd55e7; }
.pagina-obra .nivel.nivel14 {  background-color: #dd55e7; } 
.pagina-obra .nivel.nivel15 {  background-color: #dd55e7; }

.pagina-obra .nivel:after {  font-size: 18px; position: absolute; top:0; padding:0px 7px; right: -28px; font-weight: bold; }
.pagina-obra .nivel.nivel1:after  { content: "1";  background-color: #cc0018;}
.pagina-obra .nivel.nivel2:after  { content: "2";  background-color: #cc0093;}
.pagina-obra .nivel.nivel3:after  { content: "3";  background-color: #7e00cc;}
.pagina-obra .nivel.nivel4:after  { content: "4";  background-color: #1000cc;}
.pagina-obra .nivel.nivel5:after  { content: "5";  background-color: #00b4cc;}
.pagina-obra .nivel.nivel6:after  { content: "6";  background-color: #00cc52;}
.pagina-obra .nivel.nivel7:after  { content: "7";  background-color: #a3cc00;}
.pagina-obra .nivel.nivel8:after  { content: "8";  background-color: #ccab00;}
.pagina-obra .nivel.nivel9:after  { content: "9";  background-color: #cc6e00;}
.pagina-obra .nivel.nivel10:after { content: "10"; background-color: #dd55e7;}
.pagina-obra .nivel.nivel11:after { content: "11"; background-color: #dd55e7;}
.pagina-obra .nivel.nivel12:after { content: "12"; background-color: #dd55e7;}
.pagina-obra .nivel.nivel13:after { content: "13"; background-color: #dd55e7;}
.pagina-obra .nivel.nivel14:after { content: "14"; background-color: #dd55e7;}
.pagina-obra .nivel.nivel15:after { content: "15"; background-color: #dd55e7;}



.vertical-alignment-helper {
    display:table;
    height: 100%;
    width: 100%;
    pointer-events:none; /* This makes sure that we can still click outside of the modal to close it */
}
.vertical-align-center {
    /* To center vertically */
    display: table-cell;
    vertical-align: middle;
    pointer-events:none;
    padding-bottom: 200px;
}
.modal-content {
    /* Bootstrap sets the size of the modal in the modal-dialog class, we need to inherit it */
    width:inherit;
    height:inherit;
    /* To center horizontally */
    margin: 0 auto;
    pointer-events: all;
}

/*

.btn-nivel { padding: 0px 8px; display: inline-block; }

.btn-nivel1 {
  background-color: #cc0018;
  border-color: #cc0018;
}
.btn-nivel1:hover,
.btn-nivel1:focus,
.btn-nivel1:active,
.btn-nivel1.active {
  background-color: #b30015;
  border-color: #990012;
}
.btn-nivel1.disabled:hover,
.btn-nivel1.disabled:focus,
.btn-nivel1.disabled:active,
.btn-nivel1.disabled.active,
.btn-nivel1[disabled]:hover,
.btn-nivel1[disabled]:focus,
.btn-nivel1[disabled]:active,
.btn-nivel1[disabled].active,
fieldset[disabled] .btn-nivel1:hover,
fieldset[disabled] .btn-nivel1:focus,
fieldset[disabled] .btn-nivel1:active,
fieldset[disabled] .btn-nivel1.active {
  background-color: #cc0018;
  border-color: #cc0018;
}

.btn-nivel2 {
  background-color: #cc0093;
  border-color: #cc0093;
}
.btn-nivel2:hover,
.btn-nivel2:focus,
.btn-nivel2:active,
.btn-nivel2.active {
  background-color: #b30081;
  border-color: #99006e;
}
.btn-nivel2.disabled:hover,
.btn-nivel2.disabled:focus,
.btn-nivel2.disabled:active,
.btn-nivel2.disabled.active,
.btn-nivel2[disabled]:hover,
.btn-nivel2[disabled]:focus,
.btn-nivel2[disabled]:active,
.btn-nivel2[disabled].active,
fieldset[disabled] .btn-nivel2:hover,
fieldset[disabled] .btn-nivel2:focus,
fieldset[disabled] .btn-nivel2:active,
fieldset[disabled] .btn-nivel2.active {
  background-color: #cc0093;
  border-color: #cc0093;
}


.btn-nivel3 {
  background-color: #7e00cc;
  border-color: #7e00cc;
}
.btn-nivel3:hover,
.btn-nivel3:focus,
.btn-nivel3:active,
.btn-nivel3.active {
  background-color: #6e00b3;
  border-color: #5e0099;
}
.btn-nivel3.disabled:hover,
.btn-nivel3.disabled:focus,
.btn-nivel3.disabled:active,
.btn-nivel3.disabled.active,
.btn-nivel3[disabled]:hover,
.btn-nivel3[disabled]:focus,
.btn-nivel3[disabled]:active,
.btn-nivel3[disabled].active,
fieldset[disabled] .btn-nivel3:hover,
fieldset[disabled] .btn-nivel3:focus,
fieldset[disabled] .btn-nivel3:active,
fieldset[disabled] .btn-nivel3.active {
  background-color: #7e00cc;
  border-color: #7e00cc;
}

.btn-nivel4 {
  background-color: #1000cc;
  border-color: #1000cc;
}
.btn-nivel4:hover,
.btn-nivel4:focus,
.btn-nivel4:active,
.btn-nivel4.active {
  background-color: #0e00b3;
  border-color: #0c0099;
}
.btn-nivel4.disabled:hover,
.btn-nivel4.disabled:focus,
.btn-nivel4.disabled:active,
.btn-nivel4.disabled.active,
.btn-nivel4[disabled]:hover,
.btn-nivel4[disabled]:focus,
.btn-nivel4[disabled]:active,
.btn-nivel4[disabled].active,
fieldset[disabled] .btn-nivel4:hover,
fieldset[disabled] .btn-nivel4:focus,
fieldset[disabled] .btn-nivel4:active,
fieldset[disabled] .btn-nivel4.active {
  background-color: #1000cc;
  border-color: #1000cc;
}

.btn-nivel5 {
  background-color: #00b4cc;
  border-color: #00b4cc;
}
.btn-nivel5:hover,
.btn-nivel5:focus,
.btn-nivel5:active,
.btn-nivel5.active {
  background-color: #009eb3;
  border-color: #008799;
}
.btn-nivel5.disabled:hover,
.btn-nivel5.disabled:focus,
.btn-nivel5.disabled:active,
.btn-nivel5.disabled.active,
.btn-nivel5[disabled]:hover,
.btn-nivel5[disabled]:focus,
.btn-nivel5[disabled]:active,
.btn-nivel5[disabled].active,
fieldset[disabled] .btn-nivel5:hover,
fieldset[disabled] .btn-nivel5:focus,
fieldset[disabled] .btn-nivel5:active,
fieldset[disabled] .btn-nivel5.active {
  background-color: #00b4cc;
  border-color: #00b4cc;
}

.btn-nivel6 {
  background-color: #00cc52;
  border-color: #00cc52;
}
.btn-nivel6:hover,
.btn-nivel6:focus,
.btn-nivel6:active,
.btn-nivel6.active {
  background-color: #00b348;
  border-color: #00993e;
}
.btn-nivel6.disabled:hover,
.btn-nivel6.disabled:focus,
.btn-nivel6.disabled:active,
.btn-nivel6.disabled.active,
.btn-nivel6[disabled]:hover,
.btn-nivel6[disabled]:focus,
.btn-nivel6[disabled]:active,
.btn-nivel6[disabled].active,
fieldset[disabled] .btn-nivel6:hover,
fieldset[disabled] .btn-nivel6:focus,
fieldset[disabled] .btn-nivel6:active,
fieldset[disabled] .btn-nivel6.active {
  background-color: #00cc52;
  border-color: #00cc52;
}

.btn-nivel7 {
  background-color: #a3cc00;
  border-color: #a3cc00;
}
.btn-nivel7:hover,
.btn-nivel7:focus,
.btn-nivel7:active,
.btn-nivel7.active {
  background-color: #8fb300;
  border-color: #7a9900;
}
.btn-nivel7.disabled:hover,
.btn-nivel7.disabled:focus,
.btn-nivel7.disabled:active,
.btn-nivel7.disabled.active,
.btn-nivel7[disabled]:hover,
.btn-nivel7[disabled]:focus,
.btn-nivel7[disabled]:active,
.btn-nivel7[disabled].active,
fieldset[disabled] .btn-nivel7:hover,
fieldset[disabled] .btn-nivel7:focus,
fieldset[disabled] .btn-nivel7:active,
fieldset[disabled] .btn-nivel7.active {
  background-color: #a3cc00;
  border-color: #a3cc00;
}

.btn-nivel8 {
  background-color: #ccab00;
  border-color: #ccab00;
}
.btn-nivel8:hover,
.btn-nivel8:focus,
.btn-nivel8:active,
.btn-nivel8.active {
  background-color: #b39600;
  border-color: #998000;
}
.btn-nivel8.disabled:hover,
.btn-nivel8.disabled:focus,
.btn-nivel8.disabled:active,
.btn-nivel8.disabled.active,
.btn-nivel8[disabled]:hover,
.btn-nivel8[disabled]:focus,
.btn-nivel8[disabled]:active,
.btn-nivel8[disabled].active,
fieldset[disabled] .btn-nivel8:hover,
fieldset[disabled] .btn-nivel8:focus,
fieldset[disabled] .btn-nivel8:active,
fieldset[disabled] .btn-nivel8.active {
  background-color: #ccab00;
  border-color: #ccab00;
}

.btn-nivel9 {
  background-color: #cc6e00;
  border-color: #cc6e00;
}
.btn-nivel9:hover,
.btn-nivel9:focus,
.btn-nivel9:active,
.btn-nivel9.active {
  background-color: #b36000;
  border-color: #995300;
}
.btn-nivel9.disabled:hover,
.btn-nivel9.disabled:focus,
.btn-nivel9.disabled:active,
.btn-nivel9.disabled.active,
.btn-nivel9[disabled]:hover,
.btn-nivel9[disabled]:focus,
.btn-nivel9[disabled]:active,
.btn-nivel9[disabled].active,
fieldset[disabled] .btn-nivel9:hover,
fieldset[disabled] .btn-nivel9:focus,
fieldset[disabled] .btn-nivel9:active,
fieldset[disabled] .btn-nivel9.active {
  background-color: #cc6e00;
  border-color: #cc6e00;
}


.btn-nivel9 {
  background-color: #dd55e7;
  border-color: #dd55e7;
}
.btn-nivel9:hover,
.btn-nivel9:focus,
.btn-nivel9:active,
.btn-nivel9.active {
  background-color: #dd5500;
  border-color: #995300;
}
.btn-nivel9.disabled:hover,
.btn-nivel9.disabled:focus,
.btn-nivel9.disabled:active,
.btn-nivel9.disabled.active,
.btn-nivel9[disabled]:hover,
.btn-nivel9[disabled]:focus,
.btn-nivel9[disabled]:active,
.btn-nivel9[disabled].active,
fieldset[disabled] .btn-nivel9:hover,
fieldset[disabled] .btn-nivel9:focus,
fieldset[disabled] .btn-nivel9:active,
fieldset[disabled] .btn-nivel9.active {
  background-color: #dd55e7;
  border-color: #dd55e7;
}*/






