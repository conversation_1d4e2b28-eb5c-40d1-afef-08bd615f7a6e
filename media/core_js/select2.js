/*
Copyright 2012 <PERSON>

Version: 3.0 Timestamp: Tue Jul 31 21:09:16 PDT 2012

Licensed under the Apache License, Version 2.0 (the "License"); you may not use this work except in
compliance with the License. You may obtain a copy of the License in the LICENSE file, or at:

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software distributed under the License is
distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and limitations under the License.
*/
(function(f){"undefined"==typeof f.fn.each2&&f.fn.extend({each2:function(h){for(var l=f([0]),j=-1,m=this.length;++j<m&&(l.context=l[0]=this[j])&&!1!==h.call(l[0],j,l););return this}})})(jQuery);
(function(f,h){function l(a){return a&&"string"===typeof a?a.replace("&","&amp;"):a}function j(a,b){var c=0,d=b.length,g;if("undefined"===typeof a)return-1;if(a.constructor===String)for(;c<d;c+=1){if(0===a.localeCompare(b[c]))return c}else for(;c<d;c+=1)if(g=b[c],g.constructor===String){if(0===g.localeCompare(a))return c}else if(g===a)return c;return-1}function m(a,b){return a===b?!0:a===h||b===h||null===a||null===b?!1:a.constructor===String?0===a.localeCompare(b):b.constructor===String?0===b.localeCompare(a):
!1}function y(a,b){var c,d,g;if(null===a||1>a.length)return[];c=a.split(b);d=0;for(g=c.length;d<g;d+=1)c[d]=f.trim(c[d]);return c}function z(a,b){var c;return function(){window.clearTimeout(c);c=window.setTimeout(b,a)}}function i(a){a.preventDefault();a.stopPropagation()}function A(a,b,c){var d=a.toUpperCase().indexOf(b.toUpperCase()),b=b.length;0>d?c.push(a):(c.push(a.substring(0,d)),c.push("<span class='select2-match'>"),c.push(a.substring(d,d+b)),c.push("</span>"),c.push(a.substring(d+b,a.length)))}
function B(a){var b,c=0,d=null,g=a.quietMillis||100;return function(k){window.clearTimeout(b);b=window.setTimeout(function(){var b=c+=1,g=a.data,e=a.transport||f.ajax,h=a.type||"GET",g=g.call(this,k.term,k.page,k.context);null!==d&&d.abort();d=e.call(null,{url:a.url,dataType:a.dataType,data:g,type:h,success:function(d){b<c||(d=a.results(d,k.page),k.callback(d))}})},g)}}function C(a){var b=a,c,d=function(a){return""+a.text};f.isArray(b)||(d=b.text,f.isFunction(d)||(c=b.text,d=function(a){return a[c]}),
b=b.results);return function(a){var c=a.term,e={};if(c==="")a.callback({results:b});else{e.results=f(b).filter(function(){return a.matcher(c,d(this))}).get();a.callback(e)}}}function D(a){return f.isFunction(a)?a:function(b){var c=b.term,d={results:[]};f(a).each(function(){var a=this.text!==h,f=a?this.text:this;if(""===c||b.matcher(c,f))d.results.push(a?this:{id:this,text:this})});b.callback(d)}}function s(a){if(f.isFunction(a))return!0;if(!a)return fasle;throw Error("formatterName must be a function or a falsy value");
}function t(a){return f.isFunction(a)?a():a}function v(a,b){var c=function(){};c.prototype=new a;c.prototype.constructor=c;c.prototype.parent=a.prototype;c.prototype=f.extend(c.prototype,b);return c}if(window.Select2===h){var e,u,w,x,r;e={TAB:9,ENTER:13,ESC:27,SPACE:32,LEFT:37,UP:38,RIGHT:39,DOWN:40,SHIFT:16,CTRL:17,ALT:18,PAGE_UP:33,PAGE_DOWN:34,HOME:36,END:35,BACKSPACE:8,DELETE:46,isArrow:function(a){a=a.which?a.which:a;switch(a){case e.LEFT:case e.RIGHT:case e.UP:case e.DOWN:return!0}return!1},
isControl:function(a){a=a.which?a.which:a;switch(a){case e.SHIFT:case e.CTRL:case e.ALT:return!0}return a.metaKey?!0:!1},isFunctionKey:function(a){a=a.which?a.which:a;return 112<=a&&123>=a}};f(document).delegate("*","mousemove",function(a){f.data(document,"select2-lastpos",{x:a.pageX,y:a.pageY})});f(document).ready(function(){f(document).delegate("*","mousedown touchend",function(a){var b=f(a.target).closest("div.select2-container").get(0),c;b?f(document).find("div.select2-container-active").each(function(){this!==
b&&f(this).data("select2").blur()}):(b=f(a.target).closest("div.select2-drop").get(0),f(document).find("div.select2-drop-active").each(function(){this!==b&&f(this).data("select2").blur()}));b=f(a.target);c=b.attr("for");"LABEL"===a.target.tagName&&(c&&0<c.length)&&(b=f("#"+c),b=b.data("select2"),b!==h&&(b.focus(),a.preventDefault()))})});u=v(Object,{bind:function(a){var b=this;return function(){a.apply(b,arguments)}},init:function(a){var b,c;this.opts=a=this.prepareOpts(a);this.id=a.id;a.element.data("select2")!==
h&&null!==a.element.data("select2")&&this.destroy();this.enabled=!0;this.container=this.createContainer();var d=!1,g;this.body=function(){!1===d&&(g=a.element.closest("body"),d=!0);return g};a.element.attr("class")!==h&&this.container.addClass(a.element.attr("class"));this.container.css(t(a.containerCss));this.container.addClass(t(a.containerCssClass));this.opts.element.data("select2",this).hide().after(this.container);this.container.data("select2",this);this.dropdown=this.container.find(".select2-drop");
this.dropdown.css(t(a.dropdownCss));this.dropdown.addClass(t(a.dropdownCssClass));this.dropdown.data("select2",this);this.results=b=this.container.find(".select2-results");this.search=c=this.container.find("input.select2-input");c.attr("tabIndex",this.opts.element.attr("tabIndex"));this.resultsPage=0;this.context=null;this.initContainer();this.initContainerWidth();this.results.bind("mousemove",function(a){var b=f.data(document,"select2-lastpos");(b===h||b.x!==a.pageX||b.y!==a.pageY)&&f(a.target).trigger("mousemove-filtered",
a)});this.dropdown.delegate(".select2-results","mousemove-filtered",this.bind(this.highlightUnderEvent));var k=this.results,e=z(80,function(a){k.trigger("scroll-debounced",a)});k.bind("scroll",function(a){0<=j(a.target,k.get())&&e(a)});this.dropdown.delegate(".select2-results","scroll-debounced",this.bind(this.loadMoreIfNeeded));f.fn.mousewheel&&b.mousewheel(function(a,c,d,g){c=b.scrollTop();0<g&&0>=c-g?(b.scrollTop(0),i(a)):0>g&&b.get(0).scrollHeight-b.scrollTop()+g<=b.height()&&(b.scrollTop(b.get(0).scrollHeight-
b.height()),i(a))});c.bind("keydown",function(){f.data(c,"keyup-change-value")===h&&f.data(c,"keyup-change-value",c.val())});c.bind("keyup",function(){var a=f.data(c,"keyup-change-value");a!==h&&c.val()!==a&&(f.removeData(c,"keyup-change-value"),c.trigger("keyup-change"))});c.bind("keyup-change",this.bind(this.updateResults));c.bind("focus",function(){c.addClass("select2-focused");" "===c.val()&&c.val("")});c.bind("blur",function(){c.removeClass("select2-focused")});this.dropdown.delegate(".select2-results",
"mouseup",this.bind(function(a){0<f(a.target).closest(".select2-result-selectable:not(.select2-disabled)").length?(this.highlightUnderEvent(a),this.selectHighlighted(a)):this.focusSearch();i(a)}));this.dropdown.bind("click mouseup mousedown",function(a){a.stopPropagation()});f.isFunction(this.opts.initSelection)&&(this.initSelection(),this.monitorSource());a.element.is(":disabled")&&this.disable()},destroy:function(){var a=this.opts.element.data("select2");a!==h&&(a.container.remove(),a.dropdown.remove(),
a.opts.element.removeData("select2").unbind(".select2").show())},prepareOpts:function(a){var b,c,d;b=a.element;"select"===b.get(0).tagName.toLowerCase()&&(this.select=c=a.element);a.separator=a.separator||",";c&&f.each("id multiple ajax query createSearchChoice initSelection data tags".split(" "),function(){if(this in a)throw Error("Option '"+this+"' is not allowed for Select2 when attached to a <select> element.");});a=f.extend({},{populateResults:function(b,c,d){var e,E=this.opts.id;e=function(b,
c,g){var k,i,j,p,q,n,m;k=0;for(i=b.length;k<i;k=k+1){j=b[k];p=E(j)!==h;q="children"in j&&j.children.length>0;n=f("<li></li>");n.addClass("select2-results-dept-"+g);n.addClass("select2-result");n.addClass(p?"select2-result-selectable":"select2-result-unselectable");q&&n.addClass("select2-result-with-children");p=f("<div></div>");p.addClass("select2-result-label");m=a.formatResult(j,p,d);m!==h&&p.html(l(m));n.append(p);if(q){q=f("<ul></ul>");q.addClass("select2-result-sub");e(j.children,q,g+1);n.append(q)}n.data("select2-data",
j);c.append(n)}};e(c,b,0)}},f.fn.select2.defaults,a);"function"!==typeof a.id&&(d=a.id,a.id=function(a){return a[d]});if(c)a.query=this.bind(function(a){var c={results:[],more:false},d=a.term,e,i,j;j=function(b,c){var f;if(b.is("option"))a.matcher(d,b.text(),b)&&c.push({id:b.attr("value"),text:b.text(),element:b.get()});else if(b.is("optgroup")){f={text:b.attr("label"),children:[],element:b.get()};b.children().each2(function(a,b){j(b,f.children)});f.children.length>0&&c.push(f)}};e=b.children();if(this.getPlaceholder()!==
h&&e.length>0){i=e[0];f(i).text()===""&&(e=e.not(i))}e.each2(function(a,b){j(b,c.results)});a.callback(c)}),a.id=function(a){return a.id};else if(!("query"in a))if("ajax"in a){if((c=a.element.data("ajax-url"))&&0<c.length)a.ajax.url=c;a.query=B(a.ajax)}else"data"in a?a.query=C(a.data):"tags"in a&&(a.query=D(a.tags),a.createSearchChoice=function(a){return{id:a,text:a}},a.initSelection=function(b,c){var d=[];f(y(b.val(),a.separator)).each(function(){var b=this,c=this,g=a.tags;f.isFunction(g)&&(g=g());
f(g).each(function(){if(m(this.id,b)){c=this.text;return false}});d.push({id:b,text:c})});c(d)});if("function"!==typeof a.query)throw"query function not defined for Select2 "+a.element.attr("id");return a},monitorSource:function(){this.opts.element.bind("change.select2",this.bind(function(){!0!==this.opts.element.data("select2-change-triggered")&&this.initSelection()}))},triggerChange:function(a){a=a||{};a=f.extend({},a,{type:"change",val:this.val()});this.opts.element.data("select2-change-triggered",
!0);this.opts.element.trigger(a);this.opts.element.data("select2-change-triggered",!1);this.opts.element.click()},enable:function(){this.enabled||(this.enabled=!0,this.container.removeClass("select2-container-disabled"))},disable:function(){this.enabled&&(this.close(),this.enabled=!1,this.container.addClass("select2-container-disabled"))},opened:function(){return this.container.hasClass("select2-dropdown-open")},positionDropdown:function(){var a=this.container.offset(),b=this.container.outerHeight(),
c=this.container.outerWidth(),d=this.dropdown.outerHeight(),g=f(window).scrollTop()+document.documentElement.clientHeight,b=a.top+b,g=b+d<=g,e=a.top-d>=this.body().scrollTop(),h;this.dropdown.hasClass("select2-drop-above")?(h=!0,!e&&g&&(h=!1)):(h=!1,!g&&e&&(h=!0));h?(b=a.top-d,this.container.addClass("select2-drop-above"),this.dropdown.addClass("select2-drop-above")):(this.container.removeClass("select2-drop-above"),this.dropdown.removeClass("select2-drop-above"));this.dropdown.css({top:b,left:a.left,
width:c})},shouldOpen:function(){var a;if(this.opened())return!1;a=jQuery.Event("open");this.opts.element.trigger(a);return!a.isDefaultPrevented()},clearDropdownAlignmentPreference:function(){this.container.removeClass("select2-drop-above");this.dropdown.removeClass("select2-drop-above")},open:function(){if(!this.shouldOpen())return!1;window.setTimeout(this.bind(this.opening),1);return!0},opening:function(){this.clearDropdownAlignmentPreference();" "===this.search.val()&&this.search.val("");this.dropdown.addClass("select2-drop-active");
this.container.addClass("select2-dropdown-open").addClass("select2-container-active");this.updateResults(!0);this.dropdown[0]!==this.body().children().last()[0]&&this.dropdown.detach().appendTo(this.body());this.dropdown.show();this.ensureHighlightVisible();this.positionDropdown();this.focusSearch()},close:function(){this.opened()&&(this.clearDropdownAlignmentPreference(),this.dropdown.hide(),this.container.removeClass("select2-dropdown-open").removeClass("select2-container-active"),this.results.empty(),
this.clearSearch(),this.opts.element.trigger(jQuery.Event("close")))},clearSearch:function(){},ensureHighlightVisible:function(){var a=this.results,b,c,d,g;c=this.highlight();0>c||(0==c?a.scrollTop(0):(b=a.find(".select2-result-selectable"),d=f(b[c]),g=d.offset().top+d.outerHeight(),c===b.length-1&&(b=a.find("li.select2-more-results"),0<b.length&&(g=b.offset().top+b.outerHeight())),b=a.offset().top+a.outerHeight(),g>b&&a.scrollTop(a.scrollTop()+(g-b)),d=d.offset().top-a.offset().top,0>d&&a.scrollTop(a.scrollTop()+
d)))},moveHighlight:function(a){for(var b=this.results.find(".select2-result-selectable"),c=this.highlight();-1<c&&c<b.length;){var c=c+a,d=f(b[c]);if(d.hasClass("select2-result-selectable")&&!d.hasClass("select2-disabled")){this.highlight(c);break}}},highlight:function(a){var b=this.results.find(".select2-result-selectable").not(".select2-disabled");if(0===arguments.length)return j(b.filter(".select2-highlighted")[0],b.get());a>=b.length&&(a=b.length-1);0>a&&(a=0);b.removeClass("select2-highlighted");
f(b[a]).addClass("select2-highlighted");this.ensureHighlightVisible()},countSelectableResults:function(){return this.results.find(".select2-result-selectable").not(".select2-disabled").length},highlightUnderEvent:function(a){a=f(a.target).closest(".select2-result-selectable");if(0<a.length&&!a.is(".select2-highlighted")){var b=this.results.find(".select2-result-selectable");this.highlight(b.index(a))}else 0==a.length&&this.results.find(".select2-highlighted").removeClass("select2-highlighted")},loadMoreIfNeeded:function(){var a=
this.results,b=a.find("li.select2-more-results"),c,d=this.resultsPage+1,f=this,e=this.search.val(),h=this.context;0!==b.length&&(c=b.offset().top-a.offset().top-a.height(),0>=c&&(b.addClass("select2-active"),this.opts.query({term:e,page:d,context:h,matcher:this.opts.matcher,callback:this.bind(function(c){f.opts.populateResults.call(this,a,c.results,{term:e,page:d,context:h});!0===c.more?(b.detach().appendTo(a.children(":last")).text(f.opts.formatLoadMore(d+1)),window.setTimeout(function(){f.loadMoreIfNeeded()},
10)):b.remove();f.positionDropdown();f.resultsPage=d})})))},updateResults:function(a){function b(){g.scrollTop(0);d.removeClass("select2-active");j.positionDropdown()}function c(a){g.html(l(a));b()}var d=this.search,g=this.results,e=this.opts,i,j=this;if(!(!0!==a&&(!1===this.showSearchInput||!this.opened()))){d.addClass("select2-active");if(1<=e.maximumSelectionSize&&(i=this.data(),f.isArray(i)&&i.length>=e.maximumSelectionSize&&s(e.formatSelectionTooBig,"formatSelectionTooBig"))){c("<li class='select2-selection-limit'>"+
e.formatSelectionTooBig(e.maximumSelectionSize)+"</li>");return}d.val().length<e.minimumInputLength&&s(e.formatInputTooShort,"formatInputTooShort")?c("<li class='select2-no-results'>"+e.formatInputTooShort(d.val(),e.minimumInputLength)+"</li>"):(this.resultsPage=1,e.query({term:d.val(),page:this.resultsPage,context:null,matcher:e.matcher,callback:this.bind(function(i){var o;this.context=i.context===h?null:i.context;this.opts.createSearchChoice&&""!==d.val()&&(o=this.opts.createSearchChoice.call(null,
d.val(),i.results),o!==h&&null!==o&&j.id(o)!==h&&null!==j.id(o)&&0===f(i.results).filter(function(){return m(j.id(this),j.id(o))}).length&&i.results.unshift(o));0===i.results.length&&s(e.formatNoMatches,"formatNoMatches")?c("<li class='select2-no-results'>"+e.formatNoMatches(d.val())+"</li>"):(g.empty(),j.opts.populateResults.call(this,g,i.results,{term:d.val(),page:this.resultsPage,context:null}),!0===i.more&&s(e.formatLoadMore,"formatLoadMore")&&(g.children().filter(":last").append("<li class='select2-more-results'>"+
l(e.formatLoadMore(this.resultsPage))+"</li>"),window.setTimeout(function(){j.loadMoreIfNeeded()},10)),this.postprocessResults(i,a),b())})}))}},cancel:function(){this.close()},blur:function(){this.close();this.container.removeClass("select2-container-active");this.dropdown.removeClass("select2-drop-active");this.search[0]===document.activeElement&&this.search.blur();this.clearSearch();this.selection.find(".select2-search-choice-focus").removeClass("select2-search-choice-focus")},focusSearch:function(){window.setTimeout(this.bind(function(){this.search.focus();
this.search.val(this.search.val())}),10)},selectHighlighted:function(){var a=this.highlight(),b=this.results.find(".select2-highlighted").not(".select2-disabled"),c=b.closest(".select2-result-selectable").data("select2-data");c&&(b.addClass("select2-disabled"),this.highlight(a),this.onSelect(c))},getPlaceholder:function(){return this.opts.element.attr("placeholder")||this.opts.element.attr("data-placeholder")||this.opts.element.data("placeholder")||this.opts.placeholder},initContainerWidth:function(){var a=
function(){var a,c,d,e;if("off"===this.opts.width)return null;if("element"===this.opts.width)return 0===this.opts.element.outerWidth()?"auto":this.opts.element.outerWidth()+"px";if("copy"===this.opts.width||"resolve"===this.opts.width){a=this.opts.element.attr("style");if(a!==h){a=a.split(";");d=0;for(e=a.length;d<e;d+=1)if(c=a[d].replace(/\s/g,"").match(/width:(([-+]?([0-9]*\.)?[0-9]+)(px|em|ex|%|in|cm|mm|pt|pc))/),null!==c&&1<=c.length)return c[1]}return"resolve"===this.opts.width?(a=this.opts.element.css("width"),
0<a.indexOf("%")?a:0===this.opts.element.outerWidth()?"auto":this.opts.element.outerWidth()+"px"):null}return f.isFunction(this.opts.width)?this.opts.width():this.opts.width}.call(this);null!==a&&this.container.attr("style","width: "+a)}});w=v(u,{createContainer:function(){return f("<div></div>",{"class":"select2-container"}).html("    <a href='javascript:void(0)' class='select2-choice'>   <span></span><abbr class='select2-search-choice-close' style='display:none;'></abbr>   <div><b></b></div></a>    <div class='select2-drop select2-offscreen'>   <div class='select2-search'>       <input type='text' autocomplete='off' class='select2-input'/>   </div>   <ul class='select2-results'>   </ul></div>")},
opening:function(){this.search.show();this.parent.opening.apply(this,arguments);this.dropdown.removeClass("select2-offscreen")},close:function(){this.opened()&&(this.parent.close.apply(this,arguments),this.dropdown.removeAttr("style").addClass("select2-offscreen").insertAfter(this.selection).show())},focus:function(){this.close();this.selection.focus()},isFocused:function(){return this.selection[0]===document.activeElement},cancel:function(){this.parent.cancel.apply(this,arguments);this.selection.focus()},
initContainer:function(){var a,b=this.dropdown;this.selection=a=this.container.find(".select2-choice");this.search.bind("keydown",this.bind(function(a){if(this.enabled)if(a.which===e.PAGE_UP||a.which===e.PAGE_DOWN)i(a);else if(this.opened())switch(a.which){case e.UP:case e.DOWN:this.moveHighlight(a.which===e.UP?-1:1);i(a);break;case e.TAB:case e.ENTER:this.selectHighlighted();i(a);break;case e.ESC:this.cancel(a),i(a)}else a.which===e.TAB||(e.isControl(a)||e.isFunctionKey(a)||a.which===e.ESC)||this.open()}));
this.search.bind("focus",this.bind(function(){this.selection.attr("tabIndex","-1")}));this.search.bind("blur",this.bind(function(){this.opened()||this.container.removeClass("select2-container-active");window.setTimeout(this.bind(function(){this.selection.attr("tabIndex",this.opts.element.attr("tabIndex"))}),10)}));a.bind("mousedown",this.bind(function(a){this.opened()?(this.close(),this.selection.focus()):this.enabled&&this.open();i(a)}));b.bind("mousedown",this.bind(function(){this.search.focus()}));
a.bind("focus",this.bind(function(){this.container.addClass("select2-container-active");this.search.attr("tabIndex","-1")}));a.bind("blur",this.bind(function(){this.container.removeClass("select2-container-active");window.setTimeout(this.bind(function(){this.search.attr("tabIndex",this.opts.element.attr("tabIndex"))}),10)}));a.bind("keydown",this.bind(function(a){if(this.enabled)if(a.which===e.PAGE_UP||a.which===e.PAGE_DOWN)i(a);else if(!(a.which===e.TAB||e.isControl(a)||e.isFunctionKey(a)||a.which===
e.ESC)){this.open();if(a.which!==e.ENTER&&!(48>a.which)){var b=String.fromCharCode(a.which).toLowerCase();a.shiftKey&&(b=b.toUpperCase());this.search.val(b)}i(a)}}));a.delegate("abbr","mousedown",this.bind(function(a){this.enabled&&(this.clear(),i(a),this.close(),this.triggerChange(),this.selection.focus())}));this.setPlaceholder();this.search.bind("focus",this.bind(function(){this.container.addClass("select2-container-active")}))},clear:function(){this.opts.element.val("");this.selection.find("span").empty();
this.selection.removeData("select2-data");this.setPlaceholder()},initSelection:function(){if(""===this.opts.element.val())this.close(),this.setPlaceholder();else{var a=this;this.opts.initSelection.call(null,this.opts.element,function(b){b!==h&&null!==b&&(a.updateSelection(b),a.close(),a.setPlaceholder())})}},prepareOpts:function(){var a=this.parent.prepareOpts.apply(this,arguments);"select"===a.element.get(0).tagName.toLowerCase()&&(a.initSelection=function(a,c){var d=a.find(":selected");f.isFunction(c)&&
c({id:d.attr("value"),text:d.text()})});return a},setPlaceholder:function(){var a=this.getPlaceholder();""===this.opts.element.val()&&a!==h&&!(this.select&&""!==this.select.find("option:first").text())&&(this.selection.find("span").html(l(a)),this.selection.addClass("select2-default"),this.selection.find("abbr").hide())},postprocessResults:function(a,b){var c=0,d=this,e=!0;this.results.find(".select2-result-selectable").each2(function(a,b){if(m(d.id(b.data("select2-data")),d.opts.element.val()))return c=
a,!1});this.highlight(c);!0===b&&(e=this.showSearchInput=a.results.length>=this.opts.minimumResultsForSearch,this.dropdown.find(".select2-search")[e?"removeClass":"addClass"]("select2-search-hidden"),f(this.dropdown,this.container)[e?"addClass":"removeClass"]("select2-with-searchbox"))},onSelect:function(a){var b=this.opts.element.val();this.opts.element.val(this.id(a));this.updateSelection(a);this.close();this.selection.focus();m(b,this.id(a))||this.triggerChange()},updateSelection:function(a){var b=
this.selection.find("span");this.selection.data("select2-data",a);b.empty();a=this.opts.formatSelection(a,b);a!==h&&b.append(l(a));this.selection.removeClass("select2-default");this.opts.allowClear&&this.getPlaceholder()!==h&&this.selection.find("abbr").show()},val:function(){var a,b=null,c=this;if(0===arguments.length)return this.opts.element.val();a=arguments[0];if(this.select)this.select.val(a).find(":selected").each2(function(a,c){b={id:c.attr("value"),text:c.text()};return!1}),this.updateSelection(b),
this.setPlaceholder();else{if(this.opts.initSelection===h)throw Error("cannot call val() if initSelection() is not defined");a?this.opts.initSelection(this.opts.element,function(a){c.opts.element.val(!a?"":c.id(a));c.updateSelection(a);c.setPlaceholder()}):this.clear()}},clearSearch:function(){this.search.val("")},data:function(a){var b;if(0===arguments.length)return b=this.selection.data("select2-data"),b==h&&(b=null),b;!a||""===a?this.clear():(this.opts.element.val(!a?"":this.id(a)),this.updateSelection(a))}});
x=v(u,{createContainer:function(){return f("<div></div>",{"class":"select2-container select2-container-multi"}).html("    <ul class='select2-choices'>  <li class='select2-search-field'>    <input type='text' autocomplete='off' style='width: 25px;' class='select2-input'>  </li></ul><div class='select2-drop select2-drop-multi' style='display:none;'>   <ul class='select2-results'>   </ul></div>")},prepareOpts:function(){var a=this.parent.prepareOpts.apply(this,arguments);"select"===a.element.get(0).tagName.toLowerCase()&&
(a.initSelection=function(a,c){var d=[];a.find(":selected").each2(function(a,b){d.push({id:b.attr("value"),text:b.text()})});f.isFunction(c)&&c(d)});return a},initContainer:function(){var a;this.searchContainer=this.container.find(".select2-search-field");this.selection=a=this.container.find(".select2-choices");this.search.bind("keydown",this.bind(function(b){if(this.enabled){if(b.which===e.BACKSPACE&&""===this.search.val()){this.close();var c;c=a.find(".select2-search-choice-focus");if(0<c.length){this.unselect(c.first());
this.search.width(10);i(b);return}c=a.find(".select2-search-choice");0<c.length&&c.last().addClass("select2-search-choice-focus")}else a.find(".select2-search-choice-focus").removeClass("select2-search-choice-focus");if(this.opened())switch(b.which){case e.UP:case e.DOWN:this.moveHighlight(b.which===e.UP?-1:1);i(b);return;case e.ENTER:case e.TAB:this.selectHighlighted();i(b);return;case e.ESC:this.cancel(b);i(b);return}b.which===e.TAB||(e.isControl(b)||e.isFunctionKey(b)||b.which===e.BACKSPACE||b.which===
e.ESC)||(this.open(),(b.which===e.PAGE_UP||b.which===e.PAGE_DOWN)&&i(b))}}));this.search.bind("keyup",this.bind(this.resizeSearch));this.search.bind("blur",this.bind(function(){this.container.removeClass("select2-container-active")}));this.container.delegate(".select2-choices","mousedown",this.bind(function(a){this.enabled&&(this.clearPlaceholder(),this.open(),this.focusSearch(),a.preventDefault())}));this.container.delegate(".select2-choices","focus",this.bind(function(){this.enabled&&(this.container.addClass("select2-container-active"),
this.dropdown.addClass("select2-drop-active"),this.clearPlaceholder())}));this.clearSearch()},enable:function(){this.enabled||(this.parent.enable.apply(this,arguments),this.search.removeAttr("disabled"))},disable:function(){this.enabled&&(this.parent.disable.apply(this,arguments),this.search.attr("disabled",!0))},initSelection:function(){""===this.opts.element.val()&&(this.updateSelection([]),this.close(),this.clearSearch());if(this.select||""!==this.opts.element.val()){var a=this;this.opts.initSelection.call(null,
this.opts.element,function(b){if(b!==h&&b!==null){a.updateSelection(b);a.close();a.clearSearch()}})}},clearSearch:function(){var a=this.getPlaceholder();a!==h&&0===this.getVal().length&&!1===this.search.hasClass("select2-focused")?(this.search.val(a).addClass("select2-default"),this.resizeSearch()):this.search.val(" ").width(10)},clearPlaceholder:function(){this.search.hasClass("select2-default")?this.search.val("").removeClass("select2-default"):" "===this.search.val()&&this.search.val("")},opening:function(){this.parent.opening.apply(this,
arguments);this.clearPlaceholder();this.resizeSearch();this.focusSearch()},close:function(){this.opened()&&this.parent.close.apply(this,arguments)},focus:function(){this.close();this.search.focus()},isFocused:function(){return this.search.hasClass("select2-focused")},updateSelection:function(a){var b=[],c=[],d=this;f(a).each(function(){0>j(d.id(this),b)&&(b.push(d.id(this)),c.push(this))});a=c;this.selection.find(".select2-search-choice").remove();f(a).each(function(){d.addSelectedChoice(this)});
d.postprocessResults()},onSelect:function(a){this.addSelectedChoice(a);this.select&&this.postprocessResults();this.opts.closeOnSelect?(this.close(),this.search.width(10)):(this.search.width(10),this.resizeSearch(),0<this.countSelectableResults()?this.positionDropdown():this.close());this.triggerChange({added:a});this.focusSearch()},cancel:function(){this.close();this.focusSearch()},addSelectedChoice:function(a){var b=f("<li class='select2-search-choice'>    <div></div>    <a href='javascript:void(0)' class='select2-search-choice-close' tabindex='-1'></a></li>"),
c=this.id(a),d=this.getVal(),e;e=this.opts.formatSelection(a,b);b.find("div").replaceWith("<div>"+l(e)+"</div>");b.find(".select2-search-choice-close").bind("click dblclick",this.bind(function(a){this.enabled&&(f(a.target).closest(".select2-search-choice").fadeOut("fast").animate({width:"hide"},50,this.bind(function(){this.unselect(f(a.target));this.selection.find(".select2-search-choice-focus").removeClass("select2-search-choice-focus");this.close();this.focusSearch()})).dequeue(),i(a))})).bind("focus",
this.bind(function(){this.enabled&&(this.container.addClass("select2-container-active"),this.dropdown.addClass("select2-drop-active"))}));b.data("select2-data",a);b.insertBefore(this.searchContainer);d.push(c);this.setVal(d)},unselect:function(a){var b=this.getVal(),c,d,a=a.closest(".select2-search-choice");if(0===a.length)throw"Invalid argument: "+a+". Must be .select2-search-choice";c=a.data("select2-data");d=j(this.id(c),b);0<=d&&(b.splice(d,1),this.setVal(b),this.select&&this.postprocessResults());
a.remove();this.triggerChange({removed:c})},postprocessResults:function(){var a=this.getVal(),b=this.results.find(".select2-result-selectable"),c=this.results.find(".select2-result-with-children"),d=this;b.each2(function(b,c){var f=d.id(c.data("select2-data"));0<=j(f,a)?c.addClass("select2-disabled").removeClass("select2-result-selectable"):c.removeClass("select2-disabled").addClass("select2-result-selectable")});c.each2(function(a,b){0==b.find(".select2-result-selectable").length?b.addClass("select2-disabled"):
b.removeClass("select2-disabled")});b.each2(function(a,b){if(!b.hasClass("select2-disabled")&&b.hasClass("select2-result-selectable"))return d.highlight(0),!1})},resizeSearch:function(){var a,b,c,d,e=this.search.outerWidth()-this.search.width();a=this.search;r||(c=a[0].currentStyle||window.getComputedStyle(a[0],null),r=f("<div></div>").css({position:"absolute",left:"-10000px",top:"-10000px",display:"none",fontSize:c.fontSize,fontFamily:c.fontFamily,fontStyle:c.fontStyle,fontWeight:c.fontWeight,letterSpacing:c.letterSpacing,
textTransform:c.textTransform,whiteSpace:"nowrap"}),f("body").append(r));r.text(a.val());a=r.width()+10;b=this.search.offset().left;c=this.selection.width();d=this.selection.offset().left;b=c-(b-d)-e;b<a&&(b=c-e);40>b&&(b=c-e);this.search.width(b)},getVal:function(){var a;if(this.select)return a=this.select.val(),null===a?[]:a;a=this.opts.element.val();return y(a,this.opts.separator)},setVal:function(a){var b;this.select?this.select.val(a):(b=[],f(a).each(function(){0>j(this,b)&&b.push(this)}),this.opts.element.val(0===
b.length?"":b.join(this.opts.separator)))},val:function(){var a,b=[],c=this;if(0===arguments.length)return this.getVal();if(a=arguments[0])if(this.setVal(a),this.select)this.select.find(":selected").each(function(){b.push({id:f(this).attr("value"),text:f(this).text()})}),this.updateSelection(b);else{if(this.opts.initSelection===h)throw Error("val() cannot be called if initSelection() is not defined");this.opts.initSelection(this.opts.element,function(a){var b=f(a).map(c.id);c.setVal(b);c.updateSelection(a);
c.clearSearch()})}else this.opts.element.val(""),this.updateSelection([]);this.clearSearch()},onSortStart:function(){if(this.select)throw Error("Sorting of elements is not supported when attached to <select>. Attach to <input type='hidden'/> instead.");this.search.width(0);this.searchContainer.hide()},onSortEnd:function(){var a=[],b=this;this.searchContainer.show();this.searchContainer.appendTo(this.searchContainer.parent());this.resizeSearch();this.selection.find(".select2-search-choice").each(function(){a.push(b.opts.id(f(this).data("select2-data")))});
this.setVal(a);this.triggerChange()},data:function(a){var b=this,c;if(0===arguments.length)return this.selection.find(".select2-search-choice").map(function(){return f(this).data("select2-data")}).get();a||(a=[]);c=f.map(a,function(a){return b.opts.id(a)});this.setVal(c);this.updateSelection(a);this.clearSearch()}});f.fn.select2=function(){var a=Array.prototype.slice.call(arguments,0),b,c,d,e,i="val destroy open close focus isFocused container onSortStart onSortEnd enable disable positionDropdown data".split(" ");
this.each(function(){if(0===a.length||"object"===typeof a[0])b=0===a.length?{}:f.extend({},a[0]),b.element=f(this),"select"===b.element.get(0).tagName.toLowerCase()?e=b.element.attr("multiple"):(e=b.multiple||!1,"tags"in b&&(b.multiple=e=!0)),c=e?new x:new w,c.init(b);else if("string"===typeof a[0]){if(0>j(a[0],i))throw"Unknown method: "+a[0];d=h;c=f(this).data("select2");if(c!==h&&(d="container"===a[0]?c.container:c[a[0]].apply(c,a.slice(1)),d!==h))return!1}else throw"Invalid arguments to select2 plugin: "+
a;});return d===h?this:d};f.fn.select2.defaults={width:"copy",closeOnSelect:!0,containerCss:{},dropdownCss:{},containerCssClass:"",dropdownCssClass:"",formatResult:function(a,b,c){b=[];A(a.text,c.term,b);return b.join("")},formatSelection:function(a){return a.text},formatNoMatches:function(){return"Nada foi encontrado"},formatInputTooShort:function(a,b){return"Please enter "+(b-a.length)+" more characters"},formatSelectionTooBig:function(a){return"You can only select "+a+" items"},formatLoadMore:function(){return"Loading more results..."},
minimumResultsForSearch:0,minimumInputLength:0,maximumSelectionSize:0,id:function(a){return a.id},matcher:function(a,b){return 0<=b.toUpperCase().indexOf(a.toUpperCase())}};window.Select2={query:{ajax:B,local:C,tags:D},util:{debounce:z,markMatch:A},"class":{"abstract":u,single:w,multi:x}}}})(jQuery);
