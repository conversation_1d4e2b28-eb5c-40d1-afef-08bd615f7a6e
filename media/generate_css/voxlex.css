.bg-pc { background-color: #EF9B11 !important; }
.text-pc { color: #EF9B11 !important; }

.bg-sc { background-color: #E77817 !important; }
.text-sc { color: #E77817 !important; }

.text-base { color: #4D4948 !important; }

/* Dashboard */
/* ********************************************************* */

    /* Tela */
    .dashboard-content .main-header {  background: #EF9B11;  }
    .dashboard-content .btn-access-ebooks ul li:hover { background-color: #E77817; } 
    .line-1 h2 .fa {  color: #EF9B11 }

    /* Abas */
    .dashboard-content .dashboard-abas > .nav { background-color: #EF9B11 }

    /* Loader */
    .dashboard-content .sk-circle .sk-child:before { background-color: #EF9B11; } 

    /* Scrollbar */
    .dashboard-content .line-1 .ps-container > .ps-scrollbar-y-rail > .ps-scrollbar-y { background-color: #EF9B11; opacity: 0.7;}

    /* Modal */
    .dashboard-content .be-modal h3 { color: #EF9B11; }
    .dashboard-content .be-modal .info-resume { border-left-color: #EF9B11;}
    .dashboard-content .be-modal .about-boxe-info h4 { color: #EF9B11;}
    .dashboard-content .be-modal .btn-primary { background: #EF9B11; border-bottom-color: #E77817; }
    .dashboard-content  .be-modal .btn-primary:hover { background: #E77817;}

    .dashboard-content span.checkbox.on { background-color: #E77817 } 


/* Login */
/* ********************************************************* */

    .login-content .jb-login-btn { background-color: #EF9B11; }
    .login-content .jb-login-btn:hover { background-color: #E77817; }

    .login-content.jb-login-bg { background: #EF9B11; height: 100%; }

     .requisitos-content .center-line { border-bottom-color:#4D4948;}
     .requisitos-content .center-line span { 
        background: #EF9B11; 
    }
    .requisitos-content, .requisitos-content a, .requisitos-content h3, .requisitos-content h4, .requisitos-content p {
        color: #4D4948;
    }

    img.inverted { -webkit-filter: invert(100%); }


