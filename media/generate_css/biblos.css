.bg-pc { background-color: #88b0bf !important; }
.text-pc { color: #88b0bf !important; }

.bg-sc { background-color: #2b697e !important; }
.text-sc { color: #2b697e !important; }

.text-base { color: #134152 !important; }

/* Dashboard */
/* ********************************************************* */

    /* Tela */
    .dashboard-content .main-header {  background: #88b0bf;  }
    .dashboard-content .btn-access-ebooks ul li:hover { background-color: #2b697e; } 
    .line-1 h2 .fa {  color: #88b0bf }

    /* Abas */
    .dashboard-content .dashboard-abas > .nav { background-color: #88b0bf }

    /* Loader */
    .dashboard-content .sk-circle .sk-child:before { background-color: #88b0bf; } 

    /* Scrollbar */
    .dashboard-content .line-1 .ps-container > .ps-scrollbar-y-rail > .ps-scrollbar-y { background-color: #88b0bf; opacity: 0.7;}

    /* Modal */
    .dashboard-content .be-modal h3 { color: #88b0bf; }
    .dashboard-content .be-modal .info-resume { border-left-color: #88b0bf;}
    .dashboard-content .be-modal .about-boxe-info h4 { color: #88b0bf;}
    .dashboard-content .be-modal .btn-primary { background: #88b0bf; border-bottom-color: #2b697e; }
    .dashboard-content  .be-modal .btn-primary:hover { background: #2b697e;}

    .dashboard-content span.checkbox.on { background-color: #2b697e } 


/* Login */
/* ********************************************************* */

    .login-content .jb-login-btn { background-color: #88b0bf; }
    .login-content .jb-login-btn:hover { background-color: #2b697e; }

    .login-content.jb-login-bg { background: #88b0bf; height: 100%; }

     .requisitos-content .center-line { border-bottom-color:#134152;}
     .requisitos-content .center-line span { 
        background: #88b0bf; 
    }
    .requisitos-content, .requisitos-content a, .requisitos-content h3, .requisitos-content h4, .requisitos-content p {
        color: #134152;
    }

    img.inverted { -webkit-filter: invert(100%); }


