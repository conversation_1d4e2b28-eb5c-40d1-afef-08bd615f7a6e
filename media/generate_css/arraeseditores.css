.bg-pc { background-color: #000000 !important; }
.text-pc { color: #000000 !important; }

.bg-sc { background-color: #dc9814 !important; }
.text-sc { color: #dc9814 !important; }

.text-base { color: #FFFFFF !important; }

/* Dashboard */
/* ********************************************************* */

    /* Tela */
    .dashboard-content .main-header {  background: #000000;  }
    .dashboard-content .btn-access-ebooks ul li:hover { background-color: #dc9814; } 
    .line-1 h2 .fa {  color: #000000 }

    /* Abas */
    .dashboard-content .dashboard-abas > .nav { background-color: #000000 }

    /* Loader */
    .dashboard-content .sk-circle .sk-child:before { background-color: #000000; } 

    /* Scrollbar */
    .dashboard-content .line-1 .ps-container > .ps-scrollbar-y-rail > .ps-scrollbar-y { background-color: #000000; opacity: 0.7;}

    /* Modal */
    .dashboard-content .be-modal h3 { color: #000000; }
    .dashboard-content .be-modal .info-resume { border-left-color: #000000;}
    .dashboard-content .be-modal .about-boxe-info h4 { color: #000000;}
    .dashboard-content .be-modal .btn-primary { background: #000000; border-bottom-color: #dc9814; }
    .dashboard-content  .be-modal .btn-primary:hover { background: #dc9814;}

    .dashboard-content span.checkbox.on { background-color: #dc9814 } 


/* Login */
/* ********************************************************* */

    .login-content .jb-login-btn { background-color: #000000; }
    .login-content .jb-login-btn:hover { background-color: #dc9814; }

    .login-content.jb-login-bg { background: #000000; height: 100%; }

     .requisitos-content .center-line { border-bottom-color:#FFFFFF;}
     .requisitos-content .center-line span { 
        background: #000000; 
    }
    .requisitos-content, .requisitos-content a, .requisitos-content h3, .requisitos-content h4, .requisitos-content p {
        color: #FFFFFF;
    }



