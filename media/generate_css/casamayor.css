.bg-pc { background-color: #99242d !important; }
.text-pc { color: #99242d !important; }

.bg-sc { background-color: #7f0404 !important; }
.text-sc { color: #7f0404 !important; }

.text-base { color: #FFFFFF !important; }

/* Dashboard */
/* ********************************************************* */

    /* Tela */
    .dashboard-content .main-header {  background: #99242d;  }
    .dashboard-content .btn-access-ebooks ul li:hover { background-color: #7f0404; } 
    .line-1 h2 .fa {  color: #99242d }

    /* Abas */
    .dashboard-content .dashboard-abas > .nav { background-color: #99242d }

    /* Loader */
    .dashboard-content .sk-circle .sk-child:before { background-color: #99242d; } 

    /* Scrollbar */
    .dashboard-content .line-1 .ps-container > .ps-scrollbar-y-rail > .ps-scrollbar-y { background-color: #99242d; opacity: 0.7;}

    /* Modal */
    .dashboard-content .be-modal h3 { color: #99242d; }
    .dashboard-content .be-modal .info-resume { border-left-color: #99242d;}
    .dashboard-content .be-modal .about-boxe-info h4 { color: #99242d;}
    .dashboard-content .be-modal .btn-primary { background: #99242d; border-bottom-color: #7f0404; }
    .dashboard-content  .be-modal .btn-primary:hover { background: #7f0404;}

    .dashboard-content span.checkbox.on { background-color: #7f0404 } 


/* Login */
/* ********************************************************* */

    .login-content .jb-login-btn { background-color: #99242d; }
    .login-content .jb-login-btn:hover { background-color: #7f0404; }

    .login-content.jb-login-bg { background: #99242d; height: 100%; }

     .requisitos-content .center-line { border-bottom-color:#FFFFFF;}
     .requisitos-content .center-line span { 
        background: #99242d; 
    }
    .requisitos-content, .requisitos-content a, .requisitos-content h3, .requisitos-content h4, .requisitos-content p {
        color: #FFFFFF;
    }



