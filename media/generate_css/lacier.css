.bg-pc { background-color: #286a3c !important; }
.text-pc { color: #286a3c !important; }

.bg-sc { background-color: #33904f !important; }
.text-sc { color: #33904f !important; }

.text-base { color: #FFFFFF !important; }

/* Dashboard */
/* ********************************************************* */

    /* Tela */
    .dashboard-content .main-header {  background: #286a3c;  }
    .dashboard-content .btn-access-ebooks ul li:hover { background-color: #33904f; } 
    .line-1 h2 .fa {  color: #286a3c }

    /* Abas */
    .dashboard-content .dashboard-abas > .nav { background-color: #286a3c }

    /* Loader */
    .dashboard-content .sk-circle .sk-child:before { background-color: #286a3c; } 

    /* Scrollbar */
    .dashboard-content .line-1 .ps-container > .ps-scrollbar-y-rail > .ps-scrollbar-y { background-color: #286a3c; opacity: 0.7;}

    /* Modal */
    .dashboard-content .be-modal h3 { color: #286a3c; }
    .dashboard-content .be-modal .info-resume { border-left-color: #286a3c;}
    .dashboard-content .be-modal .about-boxe-info h4 { color: #286a3c;}
    .dashboard-content .be-modal .btn-primary { background: #286a3c; border-bottom-color: #33904f; }
    .dashboard-content  .be-modal .btn-primary:hover { background: #33904f;}

    .dashboard-content span.checkbox.on { background-color: #33904f } 


/* Login */
/* ********************************************************* */

    .login-content .jb-login-btn { background-color: #286a3c; }
    .login-content .jb-login-btn:hover { background-color: #33904f; }

    .login-content.jb-login-bg { background: #286a3c; height: 100%; }

     .requisitos-content .center-line { border-bottom-color:#FFFFFF;}
     .requisitos-content .center-line span { 
        background: #286a3c; 
    }
    .requisitos-content, .requisitos-content a, .requisitos-content h3, .requisitos-content h4, .requisitos-content p {
        color: #FFFFFF;
    }



