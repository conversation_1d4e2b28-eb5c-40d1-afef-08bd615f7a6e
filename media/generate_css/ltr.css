.bg-pc { background-color: #7b0a09 !important; }
.text-pc { color: #7b0a09 !important; }

.bg-sc { background-color: #680706 !important; }
.text-sc { color: #680706 !important; }

.text-base { color: #FFFFFF !important; }

/* Dashboard */
/* ********************************************************* */

    /* Tela */
    .dashboard-content .main-header {  background: #7b0a09;  }
    .dashboard-content .btn-access-ebooks ul li:hover { background-color: #680706; } 
    .line-1 h2 .fa {  color: #7b0a09 }

    /* Abas */
    .dashboard-content .dashboard-abas > .nav { background-color: #7b0a09 }

    /* Loader */
    .dashboard-content .sk-circle .sk-child:before { background-color: #7b0a09; } 

    /* Scrollbar */
    .dashboard-content .line-1 .ps-container > .ps-scrollbar-y-rail > .ps-scrollbar-y { background-color: #7b0a09; opacity: 0.7;}

    /* Modal */
    .dashboard-content .be-modal h3 { color: #7b0a09; }
    .dashboard-content .be-modal .info-resume { border-left-color: #7b0a09;}
    .dashboard-content .be-modal .about-boxe-info h4 { color: #7b0a09;}
    .dashboard-content .be-modal .btn-primary { background: #7b0a09; border-bottom-color: #680706; }
    .dashboard-content  .be-modal .btn-primary:hover { background: #680706;}

    .dashboard-content span.checkbox.on { background-color: #680706 } 


/* Login */
/* ********************************************************* */

    .login-content .jb-login-btn { background-color: #7b0a09; }
    .login-content .jb-login-btn:hover { background-color: #680706; }

    .login-content.jb-login-bg { background: #7b0a09; height: 100%; }

     .requisitos-content .center-line { border-bottom-color:#FFFFFF;}
     .requisitos-content .center-line span { 
        background: #7b0a09; 
    }
    .requisitos-content, .requisitos-content a, .requisitos-content h3, .requisitos-content h4, .requisitos-content p {
        color: #FFFFFF;
    }



