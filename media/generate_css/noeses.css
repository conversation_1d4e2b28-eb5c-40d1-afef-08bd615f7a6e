.bg-pc { background-color: #308ccb !important; }
.text-pc { color: #308ccb !important; }

.bg-sc { background-color: #2879b0 !important; }
.text-sc { color: #2879b0 !important; }

.text-base { color: #FFFFFF !important; }

/* Dashboard */
/* ********************************************************* */

    /* Tela */
    .dashboard-content .main-header {  background: #308ccb;  }
    .dashboard-content .btn-access-ebooks ul li:hover { background-color: #2879b0; } 
    .line-1 h2 .fa {  color: #308ccb }

    /* Abas */
    .dashboard-content .dashboard-abas > .nav { background-color: #308ccb }

    /* Loader */
    .dashboard-content .sk-circle .sk-child:before { background-color: #308ccb; } 

    /* Scrollbar */
    .dashboard-content .line-1 .ps-container > .ps-scrollbar-y-rail > .ps-scrollbar-y { background-color: #308ccb; opacity: 0.7;}

    /* Modal */
    .dashboard-content .be-modal h3 { color: #308ccb; }
    .dashboard-content .be-modal .info-resume { border-left-color: #308ccb;}
    .dashboard-content .be-modal .about-boxe-info h4 { color: #308ccb;}
    .dashboard-content .be-modal .btn-primary { background: #308ccb; border-bottom-color: #2879b0; }
    .dashboard-content  .be-modal .btn-primary:hover { background: #2879b0;}

    .dashboard-content span.checkbox.on { background-color: #2879b0 } 


/* Login */
/* ********************************************************* */

    .login-content .jb-login-btn { background-color: #308ccb; }
    .login-content .jb-login-btn:hover { background-color: #2879b0; }

    .login-content.jb-login-bg { background: #308ccb; height: 100%; }

     .requisitos-content .center-line { border-bottom-color:#FFFFFF;}
     .requisitos-content .center-line span { 
        background: #308ccb; 
    }
    .requisitos-content, .requisitos-content a, .requisitos-content h3, .requisitos-content h4, .requisitos-content p {
        color: #FFFFFF;
    }



