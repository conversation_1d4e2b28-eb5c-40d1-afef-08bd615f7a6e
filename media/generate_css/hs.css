.bg-pc { background-color: #eaae45 !important; }
.text-pc { color: #eaae45 !important; }

.bg-sc { background-color: #d3972d !important; }
.text-sc { color: #d3972d !important; }

.text-base { color: #000000 !important; }

/* Dashboard */
/* ********************************************************* */

    /* Tela */
    .dashboard-content .main-header {  background: #eaae45;  }
    .dashboard-content .btn-access-ebooks ul li:hover { background-color: #d3972d; } 
    .line-1 h2 .fa {  color: #eaae45 }

    /* Abas */
    .dashboard-content .dashboard-abas > .nav { background-color: #eaae45 }

    /* Loader */
    .dashboard-content .sk-circle .sk-child:before { background-color: #eaae45; } 

    /* Scrollbar */
    .dashboard-content .line-1 .ps-container > .ps-scrollbar-y-rail > .ps-scrollbar-y { background-color: #eaae45; opacity: 0.7;}

    /* Modal */
    .dashboard-content .be-modal h3 { color: #eaae45; }
    .dashboard-content .be-modal .info-resume { border-left-color: #eaae45;}
    .dashboard-content .be-modal .about-boxe-info h4 { color: #eaae45;}
    .dashboard-content .be-modal .btn-primary { background: #eaae45; border-bottom-color: #d3972d; }
    .dashboard-content  .be-modal .btn-primary:hover { background: #d3972d;}

    .dashboard-content span.checkbox.on { background-color: #d3972d } 


/* Login */
/* ********************************************************* */

    .login-content .jb-login-btn { background-color: #eaae45; }
    .login-content .jb-login-btn:hover { background-color: #d3972d; }

    .login-content.jb-login-bg { background: #eaae45; height: 100%; }

     .requisitos-content .center-line { border-bottom-color:#000000;}
     .requisitos-content .center-line span { 
        background: #eaae45; 
    }
    .requisitos-content, .requisitos-content a, .requisitos-content h3, .requisitos-content h4, .requisitos-content p {
        color: #000000;
    }

    img.inverted { -webkit-filter: invert(100%); }


