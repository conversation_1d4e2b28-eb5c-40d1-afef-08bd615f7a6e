.bg-pc { background-color: #ce171f !important; }
.text-pc { color: #ce171f !important; }

.bg-sc { background-color: #a40f15 !important; }
.text-sc { color: #a40f15 !important; }

.text-base { color: #ffffff !important; }

/* Dashboard */
/* ********************************************************* */

    /* Tela */
    .dashboard-content .main-header {  background: #ce171f;  }
    .dashboard-content .btn-access-ebooks ul li:hover { background-color: #a40f15; } 
    .line-1 h2 .fa {  color: #ce171f }

    /* Abas */
    .dashboard-content .dashboard-abas > .nav { background-color: #ce171f }

    /* Loader */
    .dashboard-content .sk-circle .sk-child:before { background-color: #ce171f; } 

    /* Scrollbar */
    .dashboard-content .line-1 .ps-container > .ps-scrollbar-y-rail > .ps-scrollbar-y { background-color: #ce171f; opacity: 0.7;}

    /* Modal */
    .dashboard-content .be-modal h3 { color: #ce171f; }
    .dashboard-content .be-modal .info-resume { border-left-color: #ce171f;}
    .dashboard-content .be-modal .about-boxe-info h4 { color: #ce171f;}
    .dashboard-content .be-modal .btn-primary { background: #ce171f; border-bottom-color: #a40f15; }
    .dashboard-content  .be-modal .btn-primary:hover { background: #a40f15;}

    .dashboard-content span.checkbox.on { background-color: #a40f15 } 


/* Login */
/* ********************************************************* */

    .login-content .jb-login-btn { background-color: #ce171f; }
    .login-content .jb-login-btn:hover { background-color: #a40f15; }

    .login-content.jb-login-bg { background: #ce171f; height: 100%; }

     .requisitos-content .center-line { border-bottom-color:#ffffff;}
     .requisitos-content .center-line span { 
        background: #ce171f; 
    }
    .requisitos-content, .requisitos-content a, .requisitos-content h3, .requisitos-content h4, .requisitos-content p {
        color: #ffffff;
    }

    img.inverted { -webkit-filter: invert(100%); }


