$(document).ready(function()
{

    // Define os elementos utilizados
    var login_email = $('input[name="login-email"]');
    var login_senha = $('input[name="login-senha"]');
    var login_submit =  $('[name="login-submit"]');
    var login_form =  $('form[name="login-form"]');
    var login_mensagem =  $('div.login-alerta');
        
    // Foca o cursos no elemento que será utilizado
    if(login_email.val().length == 0)
        login_email.focus();
    else
        login_senha.focus();


    // Submit do Formulário
    login_form.submit(function(event)
    {

        login_submit.button('loading');

        // Desabilita o submit.
        login_submit.attr("disabled","disabled");

        // Serializa os campos do formulário
        var Mensagem = '';
        var Dados = login_form.serializeArray();
        var Url = login_form.attr("action");

        $.cookie("loginemail", login_email.val() );

        // --- 1. Sucesso
        $.post( Url, Dados, function(Retorno) {
            
            // --- 1.1. Requisição ocorreu corretamente
            if( Retorno.erro == 0 ) {
                //console.log(Retorno.erro_Msg);

                if(Retorno.url) $(location).attr('href', Retorno.url);
                else $(location).attr('href','/obras');

            }

            // --- 1.2. Erro no lado do servidor
            else
            {
                if( Retorno.erro == '-1' ) login_email.focus();
                if( Retorno.erro == '-2' ) login_senha.focus();
                if( Retorno.erro == '-3' ) login_email.focus();

                Mensagem = Retorno.mensagem;
            }

        // --- 2. Erro na requisição
        },"json").error(function() {

            Mensagem = 'Ops, ocorreu algum problema estranho!';  // Define a mensagem de erro

        // --- 3. Após o sucesso e Após o Erro
        }).complete(function(){

            // --- 3.1. Exibe a Mensagem de erro, caso tenha uma.
            if( Mensagem ) {

                login_mensagem.html(Mensagem);
                login_mensagem.fadeIn(100);
                login_mensagem.removeClass("hide");
                
                login_submit.removeAttr('disabled');
                login_submit.button('reset');

            }


        });

        // Impede a ação padrão de submit
        event.preventDefault();
        return false;
    });

});
