setInterval(function(){ 
	if($('#aupdate').is(':checked')){
		console.log('reload');
		$('#table-obras').hide();
		$('#loading').delay(3000).show();
		$('#loading').css('display','block');
		var status =  $(".filtro-status").find(".active").data("status");
	        obras.inicializa_filtros();
	        obras.filtros.grupo_status = status;
		obras.listar_obras();
	}
}, 9900);

$('#aupdate').click(function(){
	var aupdate = 0;
	if($(this).is(':checked')){
            aupdate = 1;
	}
	$.post('/obra/aupdate',{aupdate:aupdate},function(data){
		console.log('aupdate changed');
	});
});

var obras = (function() {

    var obras = {};


    // Definindo o filtros e os valores padrão
    // que serao reiniciados pela funcao inicializa_filtros
    obras.filtros = {};
    obras.filtros.porpagina = 30;
    obras.filtros.status = "";
    obras.filtros.fase = "";
    obras.filtros.busca = "";
    obras.filtros.grupo_status = 3;

    obras.inicializa_filtros = function() {

        obras.filtros.pagina = 1;
    }

    // Holders da requisicao ajax de listar_obras
    obras.xhr = null;

    obras.init = function() {


        $(".filtro-status").change(function() {
            obras.filtros.status = $(this).val();
            obras.inicializa_filtros();
            obras.listar_obras();
        });        

        $(".filtro-etapa").change(function() {
            obras.filtros.etapa = $(this).val();
            obras.inicializa_filtros();
            obras.listar_obras();
        });

        $(".filtro-busca").keyup(function() {

            $(".filtro-status").find('.label').removeClass("active");
            $(".filtro-status").find('.label[data-status="3"]').addClass("active");


            obras.filtros.busca = $(this).val();
            obras.filtros.grupo_status = 3;
            obras.inicializa_filtros();
            obras.listar_obras();
        });


        $(".filtro-status").on("click", ".label", function() {


            $(".filtro-status").find(".label").removeClass("active");
            $(this).addClass("active");

            var status = $(this).data("status");
            obras.inicializa_filtros();

            obras.filtros.grupo_status = status;
            obras.listar_obras();


            return false;
        });

	
        $("#obras_listar").on("click", ".link-ajax", function() {
            var url = $(this).attr("href");
            $.get(url, function() {
                obras.listar_obras();
            });
            return false;
        });

        // Bind de eventos
        obras.inicializa_filtros();
        obras.inicializa_paginacao();

        // Carrega a listagem de obras
        obras.listar_obras();

    };

    /**
     * Paginação
     *
     * Funcao que inicializa a paginação da obra e 
     * binda os elementos da class .obras-pagina-link para quando clicador
     * chamerem a listar_obras e atualizar a pagina.
     *
     * Esses links sempre vao possuir um data-pagina, que irá conter o valor da pagina.
     *     
     */
    obras.inicializa_paginacao = function() {

        $("#obras").on("click", ".obras-pagina-link", function(e) {
            obras.filtros.pagina = $(this).data("pagina");
            obras.listar_obras();
            return false;
        });

    };

    /**
     * Lista as obras disponiveis.
     *
     * Essa função vai trazer dois templates, um é a listagem de obras e o outro
     * é o html da paginacão. 
     * 
     * Essa função envia o objeto filtros, contento todos os filtros selecionados
     * e também as informações de paginacao e quantidade de capas por página.
     *     
     */      
     obras.listar_obras = function() {

        if(obras.xhr) {
            obras.xhr.abort();
            clearInterval(obras.loading_time);
        }

	$.get("/obra/aupdate",{},function(data){
		if(data == 1){
			$('#aupdate').prop('checked',true);
		}else{
			$('#aupdate').prop('checked',false);
		}
	});

        obras.loading_time = setInterval(function() {
            $("#obras .loading").removeClass("hide");
            $("#obras_listar").html('');
        }, 300);

        obras.xhr = $.post("/obras/listar", obras.filtros, function(data) {
            
            if(data.erro == 0) {

                clearInterval(obras.loading_time);
                $("#obras .loading").addClass("hide");
                $("#obras_listar").html(data.html);

            }

        }).complete(function(data) {
		bind_obras_listar();
		$('#loading').delay(3000).hide();
		$('#table-obras').show();
	});

    };

    /**
     * Ticker de login
     * Para o usuário permanecer na dashaboard, logado, ele precisa estar
     * sempre se comunicando com o servidor. Essa funcao faz um tick a cada
     * 45 segundos.
     *     
     */    
     obras.ticker = function() {

        sessaoTimer = setInterval(function() {
            $.post("/login/ticker", function(data) {
              if(data.erro == -1) window.location.href = '/';
            }, "json");
        }, (1000 * 45) );

    };

    return obras;

})();

function bind_obras_listar() {
	$(".excluir_obra").unbind();
	$(".excluir_obra").on("click", function(e) {
		e.preventDefault();
		var etapa = $(this).data("etapa");
		var idobra = $(this).data("idobra");

		if (etapa == 10) {
 		        var modalbox = $("#modal_excluirobra_naopode");
            		modalbox.modal("show");
			return false;
		}

 		var modalbox = $("#modal_excluirobra_confirma");
            	modalbox.modal("show");

		modalbox.find(".btn-excluir").unbind();
		modalbox.find(".btn-excluir").click(function() {
			$.post("/obra/excluir", { idobra: idobra }, function(res) {

			}).success(function() {
				obras.listar_obras();
			}).complete(function() {
				modalbox.modal("hide");
				modalbox.find(".btn-excluir").unbind();
			});
		});
	});
}
