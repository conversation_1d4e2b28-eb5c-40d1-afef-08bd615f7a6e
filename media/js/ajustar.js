var pagina_atual_carregada = null;
var pagina_atual_carregada_texto_backup = null;
var grifo_atual_selecionado = null;
var last_nivel = 1;


var last_marcacao = { "tipo": "link", "url": "http://teste" };

(function($){

    $.fn.serializeObject = function(){

        var self = this,
            json = {},
            push_counters = {},
            patterns = {
                "validate": /^[a-zA-Z][a-zA-Z0-9_]*(?:\[(?:\d*|[a-zA-Z0-9_]+)\])*$/,
                "key":      /[a-zA-Z0-9_]+|(?=\[\])/g,
                "push":     /^$/,
                "fixed":    /^\d+$/,
                "named":    /^[a-zA-Z0-9_]+$/
            };


        this.build = function(base, key, value){
            base[key] = value;
            return base;
        };

        this.push_counter = function(key){
            if(push_counters[key] === undefined){
                push_counters[key] = 0;
            }
            return push_counters[key]++;
        };

        $.each($(this).serializeArray(), function(){

            // skip invalid keys
            if(!patterns.validate.test(this.name)){
                return;
            }

            var k,
                keys = this.name.match(patterns.key),
                merge = this.value,
                reverse_key = this.name;

            while((k = keys.pop()) !== undefined){

                // adjust reverse_key
                reverse_key = reverse_key.replace(new RegExp("\\[" + k + "\\]$"), '');

                // push
                if(k.match(patterns.push)){
                    merge = self.build([], self.push_counter(reverse_key), merge);
                }

                // fixed
                else if(k.match(patterns.fixed)){
                    merge = self.build([], k, merge);
                }

                // named
                else if(k.match(patterns.named)){
                    merge = self.build({}, k, merge);
                }
            }

            json = $.extend(true, json, merge);
        });

        return json;
    };
})(jQuery);

// Create Base64 Object
var Base64={_keyStr:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",encode:function(e){var t="";var n,r,i,s,o,u,a;var f=0;e=Base64._utf8_encode(e);while(f<e.length){n=e.charCodeAt(f++);r=e.charCodeAt(f++);i=e.charCodeAt(f++);s=n>>2;o=(n&3)<<4|r>>4;u=(r&15)<<2|i>>6;a=i&63;if(isNaN(r)){u=a=64}else if(isNaN(i)){a=64}t=t+this._keyStr.charAt(s)+this._keyStr.charAt(o)+this._keyStr.charAt(u)+this._keyStr.charAt(a)}return t},decode:function(e){var t="";var n,r,i;var s,o,u,a;var f=0;e=e.replace(/[^A-Za-z0-9\+\/\=]/g,"");while(f<e.length){s=this._keyStr.indexOf(e.charAt(f++));o=this._keyStr.indexOf(e.charAt(f++));u=this._keyStr.indexOf(e.charAt(f++));a=this._keyStr.indexOf(e.charAt(f++));n=s<<2|o>>4;r=(o&15)<<4|u>>2;i=(u&3)<<6|a;t=t+String.fromCharCode(n);if(u!=64){t=t+String.fromCharCode(r)}if(a!=64){t=t+String.fromCharCode(i)}}t=Base64._utf8_decode(t);return t},_utf8_encode:function(e){e=e.replace(/\r\n/g,"\n");var t="";for(var n=0;n<e.length;n++){var r=e.charCodeAt(n);if(r<128){t+=String.fromCharCode(r)}else if(r>127&&r<2048){t+=String.fromCharCode(r>>6|192);t+=String.fromCharCode(r&63|128)}else{t+=String.fromCharCode(r>>12|224);t+=String.fromCharCode(r>>6&63|128);t+=String.fromCharCode(r&63|128)}}return t},_utf8_decode:function(e){var t="";var n=0;var r=c1=c2=0;while(n<e.length){r=e.charCodeAt(n);if(r<128){t+=String.fromCharCode(r);n++}else if(r>191&&r<224){c2=e.charCodeAt(n+1);t+=String.fromCharCode((r&31)<<6|c2&63);n+=2}else{c2=e.charCodeAt(n+1);c3=e.charCodeAt(n+2);t+=String.fromCharCode((r&15)<<12|(c2&63)<<6|c3&63);n+=3}}return t}}


function busca_pagina_obra() {

  var idobra = $('input[name="idobra"]').val();
  var pagina = $('input[name="pagina"]').val();

  $.get("/obra/pagina", {"idobra": idobra, "pagina": pagina}, function(data) {

    if(data.erro == 0) {

        // Salvo a pagina carregada
        pagina_atual_carregada = data;
        pagina_atual_carregada_texto_backup = data.pagina.texto;

        // Preenche a div
        preenche_pagina_obra();

        // Removo o drag-and-drop da imagem
        $(".pagina-obra").find("img").each(function() {
            $(this).get(0).ondragstart = function(){ return false; }
        });

        // Desabilito o botão de salvar.
        $(".btn-salvar-pagina").addClass("disabled");

    }

  }, "json");

}

function preenche_pagina_obra(dontrebuild) {

    var data = pagina_atual_carregada;

    var holder = $("<div/>", {"class":"holder"});

    html = data.pagina.texto.replace(/<img[^>]*>/g,"");

    // Parseio o conteudo
    var formato2_editor = $.parseHTML(html);

    // Coloco dentro do holder, para trabalhar de uma maneira correta.
    $(formato2_editor).appendTo( holder );

    // Colocando a imagem no fundo
    var imagem = $("<img/>");


    if( data.imagem.imagem.length > 0) {
        imagem.attr("src", "data:image/png;base64," + data.imagem.imagem );
    }
    else {
        imagem.attr("src", "/" + data.imagem.imagem_src);
    }

    imagem.appendTo( holder.find("#formato2") );
    imagem.get(0).ondragstart = function(){ return false; }


    // Ajsutando os sumarios

        // Colocando o text dentro da div
        holder.find(".nivel").each(function() {
            $(this).html( $(this).attr("title") );
        });

        // Colocando os niveis, sempre dentro do #formato2
        holder.children(".nivel").appendTo( holder.find("#formato2")  );

         // Criando um pequeno indice na barra de controle.
        $("#holder-sumario-itens").html('');

        holder.find(".nivel").each(function() {

            var palavras = $(this).attr("title");

            // Vou pegar todas as palavras, que esse sumário comporta.

            var nivel = $(this);
            var numero_nivel = retorna_numero_nivel($(this).get(0).className);
            var item = $('<li><strong class="item-nivel' + numero_nivel +  '"> ' + numero_nivel +  '</strong> ' + palavras + '<span class="pull-right glyphicon glyphicon-remove"></span></li>');

            item.find("span").click(function() {
                nivel.remove();
                item.remove();
                altera_pagina_obra();
            });

            $("#holder-sumario-itens").append(item);
        });


    // Colocando no HTML
    $(".pagina-obra").html( holder.children() );

}

function altera_pagina_obra() {

    var holder = $("<div/>", {"class":"holder" });

    // Clono o conteudo em tela.
    var code = $(".pagina-obra").html();

    holder.html( $.parseHTML(code)  );

    holder.find(".ui-resizable-handle").each(function() { $(this).remove(); });
    holder.find(".ui-resizable-handle").each(function() { $(this).remove();  });
    holder.find(".ui-resizable").each(function() { $(this).removeClass("ui-resizable"); });
    holder.find(".ui-draggable").each(function() { $(this).removeClass("ui-draggable"); });
    holder.find(".ui-draggable-dragging").each(function() { $(this).removeClass("ui-draggable-dragging"); });
    holder.find(".ui-resizable-resizing").each(function() { $(this).removeClass("ui-resizable-resizing"); });

    // Remove a holder da dom

    // Removend o resizable
    holder.find(".ui-draggable").each(function() {
        $(this).draggable("destroy");
    });

    holder.find(".ui-resizable").each(function() {
        $(this).resizable("destroy");
    });

    // Jogo todas as palavra p/ dentro do formato2
    holder.find(".palavra").each(function() {
        $(this).appendTo( holder.find("#formato2") );
    });

    // Jogo todas as palavra p/ dentro do formato2
    holder.find(".nivel").each(function() {
        $(this).html('');
        $(this).appendTo( holder.find("#formato2") );
    });

    // Jogo todas as palavra p/ dentro do formato2
    holder.find(".marcacao").each(function() {
        $(this).html('');
        $(this).appendTo( holder.find("#formato2") );
    });

    // Faço alguma coisa com aquela img??
    holder.find("img").remove();

    pagina_atual_carregada.pagina.texto = holder.html();

    preenche_pagina_obra();

    $(".btn-salvar-pagina").removeClass("disabled");

    gravarPaginaObra();

}

function gravarPaginaObra() {

    var pagina = pagina_atual_carregada.pagina;

    $.post("/obra/pagina", {"action":"salvar-pagina", "pagina": JSON.stringify(pagina) }, function(data) {
        $(".btn-salvar-pagina").addClass("disabled");
    });


}

function retorna_numero_nivel(str) {
    var res = str.match(/nivel nivel([0-9]+)/);

    if(!res) return 1;

    return res[1];
}

function retorna_xy(event) {

  var delegate = $(event.currentTarget);

  var offsetX = event.offsetX;
  var offsetY = event.offsetY;

  var offsetX = event.pageX - delegate.offset().left;
  var offsetY = event.pageY - delegate.offset().top;;

  return {
    x : offsetX,
    y : offsetY
  };

}

function reposiciona_grifo(grifo) {
    var data = grifo.data();
    grifo.css("left", data.x);
    grifo.css("top", data.y);
    grifo.css("width", data.w);
    grifo.css("height", data.h);
    grifo.css("background-color", "#006");
}

function retorna_texto_do_grifo(pos) {

    var palavras = '';

    $(".pagina-obra #formato2").find(".palavra").each(function() {

        var xp = parseInt($(this).css("left"));
        var yp = parseInt($(this).css("top"));
        var wp = xp + parseInt($(this).css("width"));
        var hp = yp + parseInt($(this).css("height"));

        if(xp >= pos.x && yp >= pos.y)
            if(wp <= (pos.x + pos.w) && hp <= (pos.y + pos.h) )
                palavras += $(this).text() + " ";

    });

    return palavras;

}

function editar_nivel(nivel) {

        $.get("/lightbox-nivel", function(data) {

            var lightbox = $("#modal");
            lightbox.html(data);
            lightbox.modal("show");


            lightbox.find('[name="nivel"]').val( retorna_numero_nivel(nivel.attr("class")) );
            lightbox.find('[name="texto"]').val( nivel.text() );

            lightbox.find(".btn-excluir").click(function() {
                nivel.remove();
                lightbox.modal("hide");

                altera_pagina_obra();
            });


            lightbox.find(".btn-salvar").click(function() {

                nivel.attr("class", "nivel nivel" + lightbox.find('[name="nivel"]').val() );
                nivel.attr("title", lightbox.find('[name="texto"]').val() );
                nivel.html(  lightbox.find('[name="texto"]').val()  );


                last_nivel = lightbox.find('[name="nivel"]').val();

                nivel.appendTo(".pagina-obra #formato2");

                lightbox.modal("hide");

                altera_pagina_obra();

            });

        });

}


function editar_palavra(palavra) {

        $.get("/lightbox-palavra", function(data) {

            var lightbox = $("#modal");
            lightbox.html(data);
            lightbox.modal("show");

            lightbox.find('[name="texto"]').val( palavra.text() );

            lightbox.find(".btn-excluir").click(function() {
                palavra.remove();
                lightbox.modal("hide");

                altera_pagina_obra();


            });


            lightbox.find(".btn-salvar").click(function() {

                palavra.attr("class", "palavra");
                palavra.html(  lightbox.find('[name="texto"]').val()  );
                palavra.appendTo(".pagina-obra #formato2");
                lightbox.modal("hide");


                altera_pagina_obra();
            });

        });

}

function editar_marcacao(marcacao, obj) {


        if(!obj) {

            // Vou pegar todo o data da marcacao, que esta na propriedade
            // em base64 e converter.
            var base = marcacao.attr("data-json");

            if(!base) {
                var obj = {};
            } else {
                var json = Base64.decode(base);
                var obj = $.parseJSON(json);
            }
        }

        // Chamo a lightbox passando o conteudo que ja teno no elemento
        $.get("/lightbox-marcacao", {"marcacao": obj }, function(data) {

            // Crio e exibo a modal
            var lightbox = $("#modal");
            lightbox.html(data);
            lightbox.modal("show");

            lightbox.find('select[name="tipo"]').change(function() {
                lightbox.find("form").addClass("hide");
                lightbox.find("form.marcacao-" + $(this).val() ).removeClass("hide");
            });

            lightbox.find('select[name="tipo"]').trigger("change");


            lightbox.find(".btn-excluir").click(function() {
                marcacao.remove();
                lightbox.modal("hide");

                altera_pagina_obra();

            });

            // Aqui, nas outras lightbox eu preenchi os campos por JS
            // mas nessa caso eu ja passei o obj pro backeend, que fez todo o servico.

            // Acao do botao salvar,q ue tera que encodar denovo em bas64 e colocar no campo especifico
            // que é o data-json do elemento
            // lembrnaod que nao pode ser no data, é sempre no ATTR-DATA
            lightbox.find(".btn-salvar").click(function() {

                // Pego o tipo selecionado
                var tipo = lightbox.find('select[name="tipo"]').val();

                // Pego o objeto gerado pelo form do tipo
                var obj = $("form.marcacao-" + tipo).serializeObject();
                obj.tipo = tipo;

                // Adicionando as coordenadas
                obj.coordenadas = {};
                obj.coordenadas.x = marcacao.css("left").replace(/[^-\d\.]/g, '');
                obj.coordenadas.y = marcacao.css("top").replace(/[^-\d\.]/g, '');
                obj.coordenadas.w = marcacao.css("width").replace(/[^-\d\.]/g, '');
                obj.coordenadas.h = marcacao.css("height").replace(/[^-\d\.]/g, '');

                obj.texto = retorna_texto_do_grifo( marcacao.data() );

                // Adicionando a classe
                marcacao.attr("class", "marcacao " + tipo);

                // Adicionando o conteudo
                marcacao.attr("data-json", Base64.encode( JSON.stringify(obj)) );

                marcacao.appendTo(".pagina-obra #formato2");

                last_marcacao = obj;

                lightbox.modal("hide");

                altera_pagina_obra();

            });

        });

}


$(document).ready(function() {

     $(".btn-salvar-pagina").click(function() {
        altera_pagina_obra();
        gravarPaginaObra();
     });

        // Ir para a página anterior
    $(".controles").on("click", ".pagina-anterior", function() {

        // Página tual e primeira página
        var pagina = parseInt( $('input[name="pagina"]').val() );
        var paginaum = parseInt( $('input[name="paginaum"]').val() );

        if(pagina == paginaum) return;

        $('input[name="pagina"]').val( pagina - 1 );
        busca_pagina_obra();
    });

    $(".controles").on("click", ".pagina-proxima", function() {

        var pagina = parseInt($('input[name="pagina"]').val());
        var qtdpaginas = parseInt( $('input[name="qtdpaginas"]').val() );

        if(pagina == qtdpaginas) return;

        $('input[name="pagina"]').val( pagina + 1 );
        busca_pagina_obra();
    });

    // Bloqueando a selecao de conteudo
    $(".controles").on("click", ".btn-marcacao", function() {
        grifo_atual_selecionado = $(this).data();

        $(".pagina-obra").addClass("ocultar-palavra ocultar-marcacao ocultar-sumario");
        $(".pagina-obra").removeClass( "ocultar-" + grifo_atual_selecionado.tipo );

        $(".btn-marcacao").removeClass("active");
        $(this).addClass("active");
    });

    $(".btn-marcacao").first().trigger("click");


    busca_pagina_obra()

    ////////////////////////////////


    $(".pagina-obra").on("mousedown", "#formato2", function(event) {

        if ( !$(event.target).is("img") ) return;

        // Removo todos os grifos incompletos
        $(".pagina-obra").find(".grifo").remove();

        // Pego as informacoes
        var pos = retorna_xy(event);

        // Criando o grifo
        var grifo = $("<div/>", {"class":"grifo"} ).data( {x: pos.x, y: pos.y, w: 0, h: 0, moved: false, grifo: grifo_atual_selecionado  } ).appendTo( $(".pagina-obra").find("#formato2") );

        // Reposicionando o grifo
        reposiciona_grifo(grifo);
    });

    // Mexendo o mouse, ou seja, mudando o tamanho do grifo
    $(".pagina-obra").on("mousemove", "#formato2", function(event) {
        if(! $(".pagina-obra").find(".grifo").size() ) return;
        // Pego informacoes
        var pos = retorna_xy(event);
        var data = $(".pagina-obra").find(".grifo").data();
        var grifo = $(".pagina-obra").find(".grifo");

        // Atualizo o tamanho do grifo
        grifo.data("w", pos.x - data.x);
        grifo.data("h", pos.y - data.y);

        if( grifo.data("w") > 5 && grifo.data("h")) grifo.data("moved", true);

        // Reposicionando o grifo
        reposiciona_grifo(grifo);
    });

    // Finalizando o grifo
    $(".pagina-obra").on("mouseup", "#formato2", function(event) {

        if(! $(".pagina-obra").find(".grifo").size() ) return;

        if(!$(".pagina-obra").find(".grifo").data("moved")) {
            $(".pagina-obra").find(".grifo").remove();
            return;
        }

        // Pego informacoes
        var pos = retorna_xy(event);
        var data = $(".pagina-obra").find(".grifo").data();
        var grifo = $(".pagina-obra").find(".grifo");

        // Reposicionando o grifo
        reposiciona_grifo(grifo);

        // Removo o grifo.
        grifo.remove();
        grifo.css("background-color", "");


        if(data.grifo.tipo == 'palavra') {
            editar_palavra(grifo);
        }

        if(data.grifo.tipo == 'sumario') {
            grifo.html( retorna_texto_do_grifo(data) );

            grifo.attr("class", "nivel nivel" + last_nivel);

            editar_nivel(grifo);
        }

        if(data.grifo.tipo == 'marcacao') {
            editar_marcacao(grifo, last_marcacao);
        }

        altera_pagina_obra();

    });

    $(".pagina-obra").on('dblclick', '.nivel', function() {
        editar_nivel( $(this) );
    });

    $(".pagina-obra").on('dblclick', '.palavra', function() {
        editar_palavra( $(this) );
    });


    $(".pagina-obra").on('dblclick', '.marcacao', function() {
        editar_marcacao( $(this) );
    });



});
