var pagina_atual_carregada = null;
var pagina_atual_carregada_texto_backup = null;
var grifo_atual_selecionado = null;


function ativar_dragg() {

    function callback_resize() {
        recuperar_html_para_editor();
        gravarPaginaObra();
    }

    $(".pagina-obra").find(".palavra, .nivel").each(function() {

        var ratio = $(this).width() / $(this).height();

        $(this).draggable(
                          {
                            stop:callback_resize,
                          }

              ).resizable(
                          {
                            aspectRatio: ratio,
                            stop:callback_resize,
                          }

              ).css("position", "absolute");
    });

}


function paginaObra() {

  var idobra = $('input[name="idobra"]').val();
  var pagina = $('input[name="pagina"]').val();

  $.get("/obra/pagina", {"idobra": idobra, "pagina": pagina}, function(data) {

    if(data.erro == 0) {

        // Desabilito o botao de ajustes de coord.
        $(".exibir-dragg, .exibir-dragg-all").removeClass("active");

        // Salvo a pagina carregada
        pagina_atual_carregada = data;
        pagina_atual_carregada_texto_backup = data.pagina.texto;

        // Exibo em tela
        exibir_html_para_editor();

        //$(".pagina-obra").get(0).onselectstart = function(){ return false; }
        //$(".pagina-obra").get(0).oncontextmenu = function(){ return false; }

        $(".pagina-obra").find("img").each(function() {
            $(this).get(0).ondragstart = function(){ return false; }
        });

        $(".btn-salvar-pagina").addClass("disabled");


    }

  }, "json");

}

function gravarPaginaObra() {

    var pagina = pagina_atual_carregada.pagina;

    $.post("/obra/pagina", {"action":"salvar-pagina", "pagina": JSON.stringify(pagina) }, function(data) {
        $(".btn-salvar-pagina").addClass("disabled");

    });


}

function exibir_html_para_editor(dontrebuild) {

    var data = pagina_atual_carregada;

    // Preenchendo o conteudo
    if(!dontrebuild) {
        $(".pagina-obra").html( formata_html_para_editor(data) );
        if( $(".exibir-dragg").hasClass("active") )  ativar_dragg();
        if( $(".exibir-dragg-all").hasClass("active") )  ativar_dragg_all();
    }
    else {
        formata_html_para_editor(data, dontrebuild);
    }



}

function recuperar_html_para_editor(dontrebuild) {


    // Eu vou remover todo mundo que está fora da pagina
    var h = $("#formato2").height();
    var w = $("#formato2").width();

    $(".palavra, .nivel").each(function() {

        if( $(this).position().left < 0 ||
            $(this).position().left > w ||
            $(this).position().top > h  ||
            $(this).position().top < 0
          )

        $(this).remove();

    });

    pagina_atual_carregada.pagina.texto = desformata_html_para_editor();
    exibir_html_para_editor(dontrebuild);

    // Verifico se houve mudancas, e habilito o botao de salvar

    if(pagina_atual_carregada_texto_backup != pagina_atual_carregada.pagina.texto) {
        $(".btn-salvar-pagina").removeClass("disabled");
    }
    else $(".btn-salvar-pagina").addClass("disabled");

}


function remove_palavras_repetidas(holder) {

    var arr = holder.find(".palavra").toArray();

    // Percorro todas as palavras
    for(i=0; i < (arr.length); i++) {

        // Mas vou verificar isso só nas 10 palavras adiante.
        var el = $(arr[i]);

        if( el.hasClass("toDelete") ) continue;

        for(k=1; (k < 10 && (k+i) < arr.length); k++) {

            var el2 = $(arr[k]);

            if( el2.get(0) == el.get(0) ) continue;

            if (el.html() == el2.html() &&
                (Math.abs( parseInt(el.css("top")) - parseInt(el2.css("top")) ) < 5) &&
                (Math.abs( parseInt(el.css("left")) - parseInt(el2.css("left")) ) < 5) &&
                (Math.abs( parseInt(el.css("width")) - parseInt(el2.css("width")) ) < 5) &&
                (Math.abs( parseInt(el.css("height")) - parseInt(el2.css("height")) ) < 5) ) {
                el2.addClass("toDelete");
            }

        }


    }

    holder.find(".toDelete").remove();

    return holder;
}


function remove_palavras_quebradas(holder) {

    // Juntando próximas palavras
    var arr = holder.find(".palavra").toArray();

    // Apenas se tivar mais que 2 palavras
    if(arr.length >= 3) {
        for(i=0; i < (arr.length - 2); i++) {
            if( $(arr[i+2]).html() == ( $(arr[i]).html() + $(arr[i+1]).html() )  ) {



                var top = Math.abs( parseInt( $(arr[i]).css("top") ) );
                var left = Math.abs( parseInt( $(arr[i]).css("left") ));
                var width = Math.abs( parseInt( $(arr[i]).css("width")) + parseInt( $(arr[i+1]).css("width") ));
                var height = Math.abs( parseInt( $(arr[i]).css("height") ));

                if (
                    (Math.abs( parseInt( $(arr[i+2]).css("top")) - top ) < 5) &&
                    (Math.abs( parseInt( $(arr[i+2]).css("left")) - left ) < 5) &&
                    (Math.abs( parseInt( $(arr[i+2]).css("width")) - width ) < 5) &&
                    (Math.abs( parseInt( $(arr[i+2]).css("height")) - height ) < 5) ) {
                        arr[i].remove();
                        arr[i+1].remove();
                  }


            }
        }
    }

    return holder;

}


// Essa funcao faz modificacoes no html formato2
// para facilitar a exibicao no editor. Esse html modificado, nao deve ser usado para salvar no banco.
// Quando for fazer alguma mudanca, sempre fazer no original e passar por essa funcao para exibir em tela.
function formata_html_para_editor(data, dontrebuild) {

    // Crio um holder para todo o conteudo
    var holder = $("<div/>", {"class":"holder"});

    if(!dontrebuild) {

        // Removendo img do mauricio
        html = data.pagina.texto.replace(/<img[^>]*>/g,"");

        // Parseio o conteudo
        var formato2_editor = $.parseHTML(html);

        // Coloco dentro do holder, para trabalhar de uma maneira correta.
        $(formato2_editor).appendTo( holder );

        // Colocando a imagem no fundo
        var imagem = $("<img/>");


        if( data.imagem.imagem.length > 0) {
            imagem.attr("src", "data:image/png;base64," + data.imagem.imagem );
        }
        else {
            imagem.attr("src", "/" + data.imagem.imagem_src);
        }

        imagem.appendTo( holder.find("#formato2") );
        imagem.get(0).ondragstart = function(){ return false; }

    }

        // Transportando os niveis para dentro da div formato2
        holder.find(".nivel").each(function() {
            $(this).html( $(this).attr("title") );
        });

        holder.children(".nivel").appendTo( holder.find("#formato2")  );

        // Criando um pequeno indice na barra de controle.
        $("#holder-sumario-itens").html('');
        holder.find(".nivel").each(function() {

            var palavras = $(this).attr("title");

            // Vou pegar todas as palavras, que esse sumário comporta.

            var nivel = $(this);
            var numero_nivel = retorna_numero_nivel($(this).get(0).className);
            var item = $('<li><strong class="item-nivel' + numero_nivel +  '"> ' + numero_nivel +  '</strong> ' + palavras + '<span class="pull-right glyphicon glyphicon-remove"></span></li>');

            item.find("span").click(function() {
                nivel.remove();
                item.remove();
                recuperar_html_para_editor();
            });

            $("#holder-sumario-itens").append(item);
        });

    if(!pagina_atual_carregada.rewords)
    {

        holder = remove_palavras_repetidas(holder);
        holder = remove_palavras_quebradas(holder);

        pagina_atual_carregada.rewords = 1;
    }

    return holder.children();

}

function desformata_html_para_editor() {

    var holder = $("<div/>", {"class":"holder" });

    // Clono o conteudo em tela.
    var code = $(".pagina-obra").html();

    holder.html( $.parseHTML(code)  );

    holder.find(".ui-resizable-handle").each(function() { $(this).remove(); });
    holder.find(".ui-resizable-handle").each(function() { $(this).remove();  });
    holder.find(".ui-resizable").each(function() { $(this).removeClass("ui-resizable"); });
    holder.find(".ui-draggable").each(function() { $(this).removeClass("ui-draggable"); });
    holder.find(".ui-draggable-dragging").each(function() { $(this).removeClass("ui-draggable-dragging"); });
    holder.find(".ui-resizable-resizing").each(function() { $(this).removeClass("ui-resizable-resizing"); });

    // Remove a holder da dom

    // Removend o resizable
    holder.find(".ui-draggable").each(function() {
        $(this).draggable("destroy");
    });

    holder.find(".ui-resizable").each(function() {
        $(this).resizable("destroy");
    });

    // Jogo todas as palavra p/ dentro do formato2
    holder.find(".palavra").each(function() {
        $(this).appendTo( holder.find("#formato2") );
    });

    // Jogo todos os sumários para fora do formato2
    holder.find(".nivel").each(function() {
        $(this).appendTo( holder );
        $(this).html('');
    });

    holder.find(".palavra, .nivel").each(function() {
        $(this).css("position", "");
    });

    // Faço alguma coisa com aquela img??
    holder.find("img").remove();

    return holder.html();

}


$(document).ready(function() {


    $(".btn-salvar-pagina").click(function() {
        gravarPaginaObra();
    });


    $(".btn-cancelar-pagina").click(function() {
        paginaObra();
    });

    // Bloqueando a selecao de conteudo
    $(".controles").on("click", ".btn-marcacao", function() {
        grifo_atual_selecionado = $(this).data();
    });

    // Ir para a página anterior
    $(".controles").on("click", ".pagina-anterior", function() {

        // Página tual e primeira página
        var pagina = parseInt( $('input[name="pagina"]').val() );
        var paginaum = parseInt( $('input[name="paginaum"]').val() );

        if(pagina == paginaum) return;

        $('input[name="pagina"]').val( pagina - 1 );
        paginaObra();
    });

    $(".controles").on("click", ".pagina-proxima", function() {

        var pagina = parseInt($('input[name="pagina"]').val());
        var qtdpaginas = parseInt( $('input[name="qtdpaginas"]').val() );

        if(pagina == qtdpaginas) return;

        $('input[name="pagina"]').val( pagina + 1 );
        paginaObra();
    });

    $(".controles").on("click", ".exibir-palavras", function() {
        $(".pagina-obra").toggleClass("ocultar-palavras");
    });

    $(".controles").on("click", ".exibir-sumario", function() {
        $(".pagina-obra").toggleClass("ocultar-sumario");
    });


    $(".controles").on("click", ".exibir-dragg", function() {

        $(".exibir-dragg-all").removeClass("active");

        if( !$(this).hasClass("active") ) {
            ativar_dragg();
        }
        else {
            $(".pagina-obra").find(".palavra, .nivel").each(function() {
               $(this).draggable("destroy").resizable("destroy").css("position", "");
            });
        }

    });

    $(".controles").on("click", ".exibir-dragg-all", function() {

        $(".exibir-dragg").removeClass("active");

        if( !$(this).hasClass("active") ) {
            ativar_dragg_all();
        }
        else {
            $(".pagina-obra").find(".palavra, .nivel").each(function() {
               $(this).draggable("destroy").resizable("destroy").css("position", "");
            });
        }

    });

    $(".pagina-obra").on('dblclick', '.palavra', function() {

            var cache = $(this).children();

            var texto = prompt("Digite a correção da palavra: ", $(this).text() );

            if(texto != null) {
               $(this).text(texto).append(cache);
               recuperar_html_para_editor();
                       gravarPaginaObra();

            }

    });


    $(".controles").find(".btn-nivel").first().click();


    paginaObra();

});
