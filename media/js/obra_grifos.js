function retorna_xy(event) {

  var delegate = $(event.currentTarget);

  var offsetX = event.offsetX;
  var offsetY = event.offsetY;

  var offsetX = event.pageX - delegate.offset().left;
  var offsetY = event.pageY - delegate.offset().top;;

  return {
    x : offsetX,
    y : offsetY
  };
  
}

function reposiciona_grifo(grifo) {
    var data = grifo.data();
    grifo.css("left", data.x);
    grifo.css("top", data.y);
    grifo.css("width", data.w);
    grifo.css("height", data.h);
    grifo.css("background-color", data.color);
}

function retorna_numero_nivel(str) {
    var res = str.match(/nivel nivel([0-9]+)/);
    return res[1];
}

function retorna_texto_do_grifo(pos) {

    var palavras = '';

    $(".pagina-obra #formato2").find(".palavra").each(function() {

        var xp = parseInt($(this).css("left"));
        var yp = parseInt($(this).css("top"));
        var wp = xp + parseInt($(this).css("width"));
        var hp = yp + parseInt($(this).css("height"));

        if(xp >= pos.x && yp >= pos.y)
            if(wp <= (pos.x + pos.w) && hp <= (pos.y + pos.h) ) 
                palavras += $(this).text() + " ";

    });

    return palavras;

}


$(document).ready(function() {

    // Aqui eu inicio uma selecao
    $(".pagina-obra").on("mousedown", "#formato2", function(event) {

        if ( !$(event.target).is("img") ) return;

        // Removo todos os grifos incompletos
        $(".pagina-obra").find(".grifo").remove();
        // Pego as informacoes
        var pos = retorna_xy(event);

        if( grifo_atual_selecionado.class == 'tipo-palavra' ) {
            var grifo = $("<div/>", {"class":"grifo 2palavra"} ).data( {x: pos.x, y: pos.y, w: 0, h: 0, moved: false, nivel: grifo_atual_selecionado.class, color: grifo_atual_selecionado.color } ).appendTo( $(".pagina-obra").find("#formato2") );
        }
        else { 
            // Criando o grifo
            var grifo = $("<div/>", {"class":"grifo"} ).data( {x: pos.x, y: pos.y, w: 0, h: 0, moved: false, nivel: grifo_atual_selecionado.class, color: grifo_atual_selecionado.color } ).appendTo( $(".pagina-obra").find("#formato2") );
        }

        // Reposicionando o grifo
        reposiciona_grifo(grifo);
    });

    // Mexendo o mouse, ou seja, mudando o tamanho do grifo
    $(".pagina-obra").on("mousemove", "#formato2", function(event) {
        if(! $(".pagina-obra").find(".grifo").size() ) return;
        // Pego informacoes
        var pos = retorna_xy(event);
        var data = $(".pagina-obra").find(".grifo").data();
        var grifo = $(".pagina-obra").find(".grifo");
        // Atualizo o tamanho do grifo
        grifo.data("w", pos.x - data.x);
        grifo.data("h", pos.y - data.y);

        if( grifo.data("w") > 5 && grifo.data("h")) grifo.data("moved", true);

        // Reposicionando o grifo
        reposiciona_grifo(grifo);
    });    

    // Finalizando o grifo
    $(".pagina-obra").on("mouseup", "#formato2", function(event) {

        if(! $(".pagina-obra").find(".grifo").size() ) return;

        if(!$(".pagina-obra").find(".grifo").data("moved")) {
            $(".pagina-obra").find(".grifo").remove();
            return;
        }

        // Pego informacoes
        var pos = retorna_xy(event);
        var data = $(".pagina-obra").find(".grifo").data();
        var grifo = $(".pagina-obra").find(".grifo");
        
        // Reposicionando o grifo
        reposiciona_grifo(grifo);
        
        // Removo o grifo.
        grifo.remove();

        if(grifo.hasClass("2palavra")) {

            grifo.removeClass("2palavra");
            grifo.removeClass("grifo");
            grifo.addClass("palavra");
            grifo.appendTo(".pagina-obra #formato2");

        }

        else {

            // Removendo coisas desnecessárias do grifo
            grifo.css({'background-color' : ''});
            grifo.removeClass("grifo");
            grifo.addClass("nivel");
            grifo.addClass(data.nivel);

            // Vou criar um nivel e adicionar no final da página.
            grifo.appendTo(".pagina-obra #formato2");

            // Buscando as palavra
            var palavras = retorna_texto_do_grifo(data);

            grifo.attr("title", palavras);
            grifo.html(palavras);

        }


        recuperar_html_para_editor();

        gravarPaginaObra();
        

    });


    $(".pagina-obra").on('dblclick', '.nivel', function() {
        var texto = prompt("Digite a descrição do sumário", $(this).attr("title") );
        $(this).attr("title", texto);
        $(this).html(texto);
        recuperar_html_para_editor();
        gravarPaginaObra();
    });

});