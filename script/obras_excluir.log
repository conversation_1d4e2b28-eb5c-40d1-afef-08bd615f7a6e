

[2021-01-19 15:21:16] ----- 
S<PERSON> obras_excluir: SELECT * FROM obras_excluir WHERE data <= '2021-01-19 01:21:16' AND status = 1
Total de obras encontradas p/ exclusao: 1
Arquivo de indice nao encontrado: /mnt/dados/www/editor/indices_montados//ltr/11558.idx

Diretorio de uploads nao encontrado: /mnt/dados/www/editor/uploads//ltr/11558

Excluindo arquivo no S3... ltr/11558.pdf
Excluindo arquivo no S3... ltr/11558.idx
Excluindo arquivo no S3... ltr/11558.jbe
Exluindo dados da tabela obras ... 
	DELETE FROM obras WHERE id=11558
Exluindo dados da tabela imagens ... 
	DELETE FROM imagens WHERE idobra=11558
Exluindo dados da tabela marcacoes ... 
	DELETE FROM marcacoes WHERE idobra=11558
Exluindo dados da tabela paginas ... 
	DELETE FROM paginas WHERE idobra=11558
Exluindo dados da tabela sumarios ... 
	DELETE FROM sumarios WHERE idobra=11558
 Obra ltr/11558 excluida! 

 ---- FIM DA EXECUCAO ---- 

[2021-01-19 15:28:41] ----- 
SQL obras_excluir: SELECT * FROM obras_excluir WHERE data <= '2021-01-19 01:28:40' AND status = 1
Total de obras encontradas p/ exclusao: 1
Arquivo de indice nao encontrado: /mnt/dados/www/editor/indices_montados//ltr/11558.idx

Diretorio de uploads nao encontrado: /mnt/dados/www/editor/uploads//ltr/11558

Excluindo arquivo no S3... ltr/11558.pdf
Excluindo arquivo no S3... ltr/11558.idx
Excluindo arquivo no S3... ltr/11558.jbe
Exluindo dados da tabela obras ... 
	DELETE FROM obras WHERE id=11558
Exluindo dados da tabela imagens ... 
	DELETE FROM imagens WHERE idobra=11558
Exluindo dados da tabela marcacoes ... 
	DELETE FROM marcacoes WHERE idobra=11558
Exluindo dados da tabela paginas ... 
	DELETE FROM paginas WHERE idobra=11558
Exluindo dados da tabela sumarios ... 
	DELETE FROM sumarios WHERE idobra=11558
 Obra ltr/11558 excluida! 

Atualizando obra como executada: UPDATE obras_excluir SET status=2 AND data_execucao=now() WHERE idobra=11558 AND tenant=ltr
 ---- FIM DA EXECUCAO ---- 

[2021-01-19 12:45:56] ----- 
SQL obras_excluir: SELECT * FROM obras_excluir WHERE data <= '2021-01-19 00:45:56' AND status = 1
Total de obras encontradas p/ exclusao: 0
 ---- FIM DA EXECUCAO ---- 

[2021-01-19 12:47:48] ----- 
SQL obras_excluir: SELECT * FROM obras_excluir WHERE data <= '2021-01-19 00:47:48' AND status = 1
Total de obras encontradas p/ exclusao: 1
Arquivo de indice nao encontrado: /mnt/dados/www/editor/indices_montados//ltr/11558.idx

Diretorio de uploads nao encontrado: /mnt/dados/www/editor/uploads//ltr/11558

Excluindo arquivo no S3... ltr/11558.pdf
Excluindo arquivo no S3... ltr/11558.idx
Excluindo arquivo no S3... ltr/11558.jbe
Exluindo dados da tabela obras ... 
	DELETE FROM obras WHERE id=11558
Exluindo dados da tabela imagens ... 
	DELETE FROM imagens WHERE idobra=11558
Exluindo dados da tabela marcacoes ... 
	DELETE FROM marcacoes WHERE idobra=11558
Exluindo dados da tabela paginas ... 
	DELETE FROM paginas WHERE idobra=11558
Exluindo dados da tabela sumarios ... 
	DELETE FROM sumarios WHERE idobra=11558
 Obra ltr/11558 excluida! 

Atualizando obra como executada: UPDATE obras_excluir SET status=2 AND data_execucao=now() WHERE idobra=11558 AND tenant='ltr'
 ---- FIM DA EXECUCAO ---- 

[2021-01-19 12:52:06] ----- 
SQL obras_excluir: SELECT * FROM obras_excluir WHERE data <= '2021-01-19 00:52:06' AND status = 1
Total de obras encontradas p/ exclusao: 0
 ---- FIM DA EXECUCAO ---- 

[2021-01-19 12:52:27] ----- 
SQL obras_excluir: SELECT * FROM obras_excluir WHERE data <= '2021-01-19 00:52:27' AND status = 1
Total de obras encontradas p/ exclusao: 1
Arquivo de indice nao encontrado: /mnt/dados/www/editor/indices_montados//ltr/11558.idx

Excluindo: /mnt/dados/www/editor/uploads//ltr/11558
 --- ( rm -rf /mnt/dados/www/editor/uploads//ltr/11558 )
Excluindo arquivo no S3... ltr/11558.pdf
Excluindo arquivo no S3... ltr/11558.idx
Excluindo arquivo no S3... ltr/11558.jbe
Exluindo dados da tabela obras ... 
	DELETE FROM obras WHERE id=11558
Exluindo dados da tabela imagens ... 
	DELETE FROM imagens WHERE idobra=11558
Exluindo dados da tabela marcacoes ... 
	DELETE FROM marcacoes WHERE idobra=11558
Exluindo dados da tabela paginas ... 
	DELETE FROM paginas WHERE idobra=11558
Exluindo dados da tabela sumarios ... 
	DELETE FROM sumarios WHERE idobra=11558
 Obra ltr/11558 excluida! 

Atualizando obra como executada: UPDATE obras_excluir SET status=2 AND data_execucao=now() WHERE idobra=11558 AND tenant='ltr'
 ---- FIM DA EXECUCAO ---- 

[2021-01-19 13:02:47] ----- 
SQL obras_excluir: SELECT * FROM obras_excluir WHERE data <= '2021-01-19 01:02:46' AND status = 1
Total de obras encontradas p/ exclusao: 1
Arquivo de indice nao encontrado: /mnt/dados/www/editor/indices_montados//ltr/11558.idx

Excluindo: /mnt/dados/www/editor/uploads//ltr/11558
 --- ( rm -rf /mnt/dados/www/editor/uploads//ltr/11558 )
Excluindo arquivo no S3... ltr/11558.pdf
... ERRO: Nao foi possivel excluir o arquivo: ltr/11558.pdf
Excluindo arquivo no S3... ltr/11558.idx
... ERRO: Nao foi possivel excluir o arquivo: ltr/11558.idx
Excluindo arquivo no S3... ltr/11558.jbe
... ERRO: Nao foi possivel excluir o arquivo: ltr/11558.jbe
Excluindo dados da tabela obras ... 
	DELETE FROM obras WHERE id=11558
Excluindo dados da tabela imagens ... 
	DELETE FROM imagens WHERE idobra=11558
Excluindo dados da tabela marcacoes ... 
	DELETE FROM marcacoes WHERE idobra=11558
Excluindo dados da tabela paginas ... 
	DELETE FROM paginas WHERE idobra=11558
Excluindo dados da tabela sumarios ... 
	DELETE FROM sumarios WHERE idobra=11558
 Obra ltr/11558 excluida! 

Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=11558 AND tenant='ltr'
 ---- FIM DA EXECUCAO ---- 

[2021-07-09 13:53:41] ----- 
SQL obras_excluir: SELECT * FROM obras_excluir WHERE data <= '2021-07-09 01:53:41' AND status = 1
Total de obras encontradas p/ exclusao: 11
Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=11556 AND tenant='ltr'
Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=39 AND tenant='noeses'
Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=99 AND tenant='noeses'
Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=49 AND tenant='gz'
Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=9 AND tenant='vanguarda'
Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=119 AND tenant='noeses'
Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=45 AND tenant='gz'
Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=149 AND tenant='noeses'
Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=163 AND tenant='noeses'
Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=50 AND tenant='gz'
Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=42 AND tenant='lexmagister'
 ---- FIM DA EXECUCAO ---- 

[2021-07-09 13:55:00] ----- 
SQL obras_excluir: SELECT * FROM obras_excluir WHERE data <= '2021-07-09 01:54:46' AND status = 1
Total de obras encontradas p/ exclusao: 11
Arquivo de indice nao encontrado: /mnt/dados/www/editor/indices_montados//ltr/11556.idx

Excluindo: /mnt/dados/www/editor/uploads//ltr/11556
 --- ( rm -rf /mnt/dados/www/editor/uploads//ltr/11556 )
Excluindo arquivo no S3... ltr/11556.pdf
... ERRO: Nao foi possivel excluir o arquivo: ltr/11556.pdf
Excluindo arquivo no S3... ltr/11556.idx
... ERRO: Nao foi possivel excluir o arquivo: ltr/11556.idx
Excluindo arquivo no S3... ltr/11556.jbe
... ERRO: Nao foi possivel excluir o arquivo: ltr/11556.jbe
Excluindo dados da tabela obras ... 
	DELETE FROM obras WHERE id=11556
Excluindo dados da tabela imagens ... 
	DELETE FROM imagens WHERE idobra=11556
Excluindo dados da tabela marcacoes ... 
	DELETE FROM marcacoes WHERE idobra=11556
Excluindo dados da tabela paginas ... 
	DELETE FROM paginas WHERE idobra=11556
Excluindo dados da tabela sumarios ... 
	DELETE FROM sumarios WHERE idobra=11556
 Obra ltr/11556 excluida! 

Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=11556 AND tenant='ltr'
Arquivo de indice nao encontrado: /mnt/dados/www/editor/indices_montados//noeses/39.idx

Excluindo: /mnt/dados/www/editor/uploads//noeses/39
 --- ( rm -rf /mnt/dados/www/editor/uploads//noeses/39 )
Excluindo arquivo no S3... noeses/39.pdf
... ERRO: Nao foi possivel excluir o arquivo: noeses/39.pdf
Excluindo arquivo no S3... noeses/39.idx
... ERRO: Nao foi possivel excluir o arquivo: noeses/39.idx
Excluindo arquivo no S3... noeses/39.jbe
... ERRO: Nao foi possivel excluir o arquivo: noeses/39.jbe
Excluindo dados da tabela obras ... 
	DELETE FROM obras WHERE id=39
Excluindo dados da tabela imagens ... 
	DELETE FROM imagens WHERE idobra=39
Excluindo dados da tabela marcacoes ... 
	DELETE FROM marcacoes WHERE idobra=39
Excluindo dados da tabela paginas ... 
	DELETE FROM paginas WHERE idobra=39
Excluindo dados da tabela sumarios ... 
	DELETE FROM sumarios WHERE idobra=39
 Obra noeses/39 excluida! 

Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=39 AND tenant='noeses'
Arquivo de indice nao encontrado: /mnt/dados/www/editor/indices_montados//noeses/99.idx

Excluindo: /mnt/dados/www/editor/uploads//noeses/99
 --- ( rm -rf /mnt/dados/www/editor/uploads//noeses/99 )
Excluindo arquivo no S3... noeses/99.pdf
... ERRO: Nao foi possivel excluir o arquivo: noeses/99.pdf
Excluindo arquivo no S3... noeses/99.idx
... ERRO: Nao foi possivel excluir o arquivo: noeses/99.idx
Excluindo arquivo no S3... noeses/99.jbe
... ERRO: Nao foi possivel excluir o arquivo: noeses/99.jbe
Excluindo dados da tabela obras ... 
	DELETE FROM obras WHERE id=99
Excluindo dados da tabela imagens ... 
	DELETE FROM imagens WHERE idobra=99
Excluindo dados da tabela marcacoes ... 
	DELETE FROM marcacoes WHERE idobra=99
Excluindo dados da tabela paginas ... 
	DELETE FROM paginas WHERE idobra=99
Excluindo dados da tabela sumarios ... 
	DELETE FROM sumarios WHERE idobra=99
 Obra noeses/99 excluida! 

Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=99 AND tenant='noeses'
Arquivo de indice nao encontrado: /mnt/dados/www/editor/indices_montados//gz/49.idx

Excluindo: /mnt/dados/www/editor/uploads//gz/49
 --- ( rm -rf /mnt/dados/www/editor/uploads//gz/49 )
Excluindo arquivo no S3... gz/49.pdf
... ERRO: Nao foi possivel excluir o arquivo: gz/49.pdf
Excluindo arquivo no S3... gz/49.idx
... ERRO: Nao foi possivel excluir o arquivo: gz/49.idx
Excluindo arquivo no S3... gz/49.jbe
... ERRO: Nao foi possivel excluir o arquivo: gz/49.jbe
Excluindo dados da tabela obras ... 
	DELETE FROM obras WHERE id=49
Excluindo dados da tabela imagens ... 
	DELETE FROM imagens WHERE idobra=49
Excluindo dados da tabela marcacoes ... 
	DELETE FROM marcacoes WHERE idobra=49
Excluindo dados da tabela paginas ... 
	DELETE FROM paginas WHERE idobra=49
Excluindo dados da tabela sumarios ... 
	DELETE FROM sumarios WHERE idobra=49
 Obra gz/49 excluida! 

Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=49 AND tenant='gz'
Arquivo de indice nao encontrado: /mnt/dados/www/editor/indices_montados//vanguarda/9.idx

Excluindo: /mnt/dados/www/editor/uploads//vanguarda/9
 --- ( rm -rf /mnt/dados/www/editor/uploads//vanguarda/9 )
Excluindo arquivo no S3... vanguarda/9.pdf
... ERRO: Nao foi possivel excluir o arquivo: vanguarda/9.pdf
Excluindo arquivo no S3... vanguarda/9.idx
... ERRO: Nao foi possivel excluir o arquivo: vanguarda/9.idx
Excluindo arquivo no S3... vanguarda/9.jbe
... ERRO: Nao foi possivel excluir o arquivo: vanguarda/9.jbe
Excluindo dados da tabela obras ... 
	DELETE FROM obras WHERE id=9
Excluindo dados da tabela imagens ... 
	DELETE FROM imagens WHERE idobra=9
Excluindo dados da tabela marcacoes ... 
	DELETE FROM marcacoes WHERE idobra=9
Excluindo dados da tabela paginas ... 
	DELETE FROM paginas WHERE idobra=9
Excluindo dados da tabela sumarios ... 
	DELETE FROM sumarios WHERE idobra=9
 Obra vanguarda/9 excluida! 

Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=9 AND tenant='vanguarda'
Arquivo de indice nao encontrado: /mnt/dados/www/editor/indices_montados//noeses/119.idx

Excluindo: /mnt/dados/www/editor/uploads//noeses/119
 --- ( rm -rf /mnt/dados/www/editor/uploads//noeses/119 )
Excluindo arquivo no S3... noeses/119.pdf
... ERRO: Nao foi possivel excluir o arquivo: noeses/119.pdf
Excluindo arquivo no S3... noeses/119.idx
... ERRO: Nao foi possivel excluir o arquivo: noeses/119.idx
Excluindo arquivo no S3... noeses/119.jbe
... ERRO: Nao foi possivel excluir o arquivo: noeses/119.jbe
Excluindo dados da tabela obras ... 
	DELETE FROM obras WHERE id=119
Excluindo dados da tabela imagens ... 
	DELETE FROM imagens WHERE idobra=119
Excluindo dados da tabela marcacoes ... 
	DELETE FROM marcacoes WHERE idobra=119
Excluindo dados da tabela paginas ... 
	DELETE FROM paginas WHERE idobra=119
Excluindo dados da tabela sumarios ... 
	DELETE FROM sumarios WHERE idobra=119
 Obra noeses/119 excluida! 

Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=119 AND tenant='noeses'
Arquivo de indice nao encontrado: /mnt/dados/www/editor/indices_montados//gz/45.idx

Excluindo: /mnt/dados/www/editor/uploads//gz/45
 --- ( rm -rf /mnt/dados/www/editor/uploads//gz/45 )
Excluindo arquivo no S3... gz/45.pdf
... ERRO: Nao foi possivel excluir o arquivo: gz/45.pdf
Excluindo arquivo no S3... gz/45.idx
... ERRO: Nao foi possivel excluir o arquivo: gz/45.idx
Excluindo arquivo no S3... gz/45.jbe
... ERRO: Nao foi possivel excluir o arquivo: gz/45.jbe
Excluindo dados da tabela obras ... 
	DELETE FROM obras WHERE id=45
Excluindo dados da tabela imagens ... 
	DELETE FROM imagens WHERE idobra=45
Excluindo dados da tabela marcacoes ... 
	DELETE FROM marcacoes WHERE idobra=45
Excluindo dados da tabela paginas ... 
	DELETE FROM paginas WHERE idobra=45
Excluindo dados da tabela sumarios ... 
	DELETE FROM sumarios WHERE idobra=45
 Obra gz/45 excluida! 

Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=45 AND tenant='gz'
Arquivo de indice nao encontrado: /mnt/dados/www/editor/indices_montados//noeses/149.idx

Excluindo: /mnt/dados/www/editor/uploads//noeses/149
 --- ( rm -rf /mnt/dados/www/editor/uploads//noeses/149 )
Excluindo arquivo no S3... noeses/149.pdf
... ERRO: Nao foi possivel excluir o arquivo: noeses/149.pdf
Excluindo arquivo no S3... noeses/149.idx
... ERRO: Nao foi possivel excluir o arquivo: noeses/149.idx
Excluindo arquivo no S3... noeses/149.jbe
... ERRO: Nao foi possivel excluir o arquivo: noeses/149.jbe
Excluindo dados da tabela obras ... 
	DELETE FROM obras WHERE id=149
Excluindo dados da tabela imagens ... 
	DELETE FROM imagens WHERE idobra=149
Excluindo dados da tabela marcacoes ... 
	DELETE FROM marcacoes WHERE idobra=149
Excluindo dados da tabela paginas ... 
	DELETE FROM paginas WHERE idobra=149
Excluindo dados da tabela sumarios ... 
	DELETE FROM sumarios WHERE idobra=149
 Obra noeses/149 excluida! 

Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=149 AND tenant='noeses'
Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=163 AND tenant='noeses'
Arquivo de indice nao encontrado: /mnt/dados/www/editor/indices_montados//gz/50.idx

Excluindo: /mnt/dados/www/editor/uploads//gz/50
 --- ( rm -rf /mnt/dados/www/editor/uploads//gz/50 )
Excluindo arquivo no S3... gz/50.pdf
... ERRO: Nao foi possivel excluir o arquivo: gz/50.pdf
Excluindo arquivo no S3... gz/50.idx
... ERRO: Nao foi possivel excluir o arquivo: gz/50.idx
Excluindo arquivo no S3... gz/50.jbe
... ERRO: Nao foi possivel excluir o arquivo: gz/50.jbe
Excluindo dados da tabela obras ... 
	DELETE FROM obras WHERE id=50
Excluindo dados da tabela imagens ... 
	DELETE FROM imagens WHERE idobra=50
Excluindo dados da tabela marcacoes ... 
	DELETE FROM marcacoes WHERE idobra=50
Excluindo dados da tabela paginas ... 
	DELETE FROM paginas WHERE idobra=50
Excluindo dados da tabela sumarios ... 
	DELETE FROM sumarios WHERE idobra=50
 Obra gz/50 excluida! 

Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=50 AND tenant='gz'
Arquivo de indice nao encontrado: /mnt/dados/www/editor/indices_montados//lexmagister/42.idx

Excluindo: /mnt/dados/www/editor/uploads//lexmagister/42
 --- ( rm -rf /mnt/dados/www/editor/uploads//lexmagister/42 )
Excluindo arquivo no S3... lexmagister/42.pdf
... ERRO: Nao foi possivel excluir o arquivo: lexmagister/42.pdf
Excluindo arquivo no S3... lexmagister/42.idx
... ERRO: Nao foi possivel excluir o arquivo: lexmagister/42.idx
Excluindo arquivo no S3... lexmagister/42.jbe
... ERRO: Nao foi possivel excluir o arquivo: lexmagister/42.jbe
Excluindo dados da tabela obras ... 
	DELETE FROM obras WHERE id=42
Excluindo dados da tabela imagens ... 
	DELETE FROM imagens WHERE idobra=42
Excluindo dados da tabela marcacoes ... 
	DELETE FROM marcacoes WHERE idobra=42
Excluindo dados da tabela paginas ... 
	DELETE FROM paginas WHERE idobra=42
Excluindo dados da tabela sumarios ... 
	DELETE FROM sumarios WHERE idobra=42
 Obra lexmagister/42 excluida! 

Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=42 AND tenant='lexmagister'
 ---- FIM DA EXECUCAO ---- 

[2021-07-09 14:03:59] ----- 
SQL obras_excluir: SELECT * FROM obras_excluir WHERE data <= '2021-07-09 02:03:59' AND status = 1
Total de obras encontradas p/ exclusao: 0
 ---- FIM DA EXECUCAO ---- 

[2021-07-09 14:05:36] ----- 
SQL obras_excluir: SELECT * FROM obras_excluir WHERE data <= '2021-07-09 14:04:34' AND status = 1
Total de obras encontradas p/ exclusao: 2
Arquivo de indice nao encontrado: /mnt/dados/www/editor/indices_montados//ltr/11560.idx

Excluindo: /mnt/dados/www/editor/uploads//ltr/11560
 --- ( rm -rf /mnt/dados/www/editor/uploads//ltr/11560 )
Excluindo arquivo no S3... ltr/11560.pdf
... ERRO: Nao foi possivel excluir o arquivo: ltr/11560.pdf
Excluindo arquivo no S3... ltr/11560.idx
... ERRO: Nao foi possivel excluir o arquivo: ltr/11560.idx
Excluindo arquivo no S3... ltr/11560.jbe
... ERRO: Nao foi possivel excluir o arquivo: ltr/11560.jbe
Excluindo dados da tabela obras ... 
	DELETE FROM obras WHERE id=11560
Excluindo dados da tabela imagens ... 
	DELETE FROM imagens WHERE idobra=11560
Excluindo dados da tabela marcacoes ... 
	DELETE FROM marcacoes WHERE idobra=11560
Excluindo dados da tabela paginas ... 
	DELETE FROM paginas WHERE idobra=11560
Excluindo dados da tabela sumarios ... 
	DELETE FROM sumarios WHERE idobra=11560
 Obra ltr/11560 excluida! 

Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=11560 AND tenant='ltr'
Arquivo de indice nao encontrado: /mnt/dados/www/editor/indices_montados//ltr/11561.idx

Excluindo: /mnt/dados/www/editor/uploads//ltr/11561
 --- ( rm -rf /mnt/dados/www/editor/uploads//ltr/11561 )
Excluindo arquivo no S3... ltr/11561.pdf
... ERRO: Nao foi possivel excluir o arquivo: ltr/11561.pdf
Excluindo arquivo no S3... ltr/11561.idx
... ERRO: Nao foi possivel excluir o arquivo: ltr/11561.idx
Excluindo arquivo no S3... ltr/11561.jbe
... ERRO: Nao foi possivel excluir o arquivo: ltr/11561.jbe
Excluindo dados da tabela obras ... 
	DELETE FROM obras WHERE id=11561
Excluindo dados da tabela imagens ... 
	DELETE FROM imagens WHERE idobra=11561
Excluindo dados da tabela marcacoes ... 
	DELETE FROM marcacoes WHERE idobra=11561
Excluindo dados da tabela paginas ... 
	DELETE FROM paginas WHERE idobra=11561
Excluindo dados da tabela sumarios ... 
	DELETE FROM sumarios WHERE idobra=11561
 Obra ltr/11561 excluida! 

Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=11561 AND tenant='ltr'
 ---- FIM DA EXECUCAO ---- 

[2021-08-09 19:27:09] ----- 
SQL obras_excluir: SELECT * FROM obras_excluir WHERE data <= '2021-08-09 19:26:07' AND status = 1
Total de obras encontradas p/ exclusao: 3
Arquivo de indice nao encontrado: /mnt/dados/www/editor/indices_montados//lexmagister/43.idx

Excluindo: /mnt/dados/www/editor/uploads//lexmagister/43
 --- ( rm -rf /mnt/dados/www/editor/uploads//lexmagister/43 )
Excluindo arquivo no S3... lexmagister/43.pdf
... ERRO: Nao foi possivel excluir o arquivo: lexmagister/43.pdf
Excluindo arquivo no S3... lexmagister/43.idx
... ERRO: Nao foi possivel excluir o arquivo: lexmagister/43.idx
Excluindo arquivo no S3... lexmagister/43.jbe
... ERRO: Nao foi possivel excluir o arquivo: lexmagister/43.jbe
Excluindo dados da tabela obras ... 
	DELETE FROM obras WHERE id=43
Excluindo dados da tabela imagens ... 
	DELETE FROM imagens WHERE idobra=43
Excluindo dados da tabela marcacoes ... 
	DELETE FROM marcacoes WHERE idobra=43
Excluindo dados da tabela paginas ... 
	DELETE FROM paginas WHERE idobra=43
Excluindo dados da tabela sumarios ... 
	DELETE FROM sumarios WHERE idobra=43
 Obra lexmagister/43 excluida! 

Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=43 AND tenant='lexmagister'
Arquivo de indice nao encontrado: /mnt/dados/www/editor/indices_montados//lexmagister/87.idx

Excluindo: /mnt/dados/www/editor/uploads//lexmagister/87
 --- ( rm -rf /mnt/dados/www/editor/uploads//lexmagister/87 )
Excluindo arquivo no S3... lexmagister/87.pdf
... ERRO: Nao foi possivel excluir o arquivo: lexmagister/87.pdf
Excluindo arquivo no S3... lexmagister/87.idx
... ERRO: Nao foi possivel excluir o arquivo: lexmagister/87.idx
Excluindo arquivo no S3... lexmagister/87.jbe
... ERRO: Nao foi possivel excluir o arquivo: lexmagister/87.jbe
Excluindo dados da tabela obras ... 
	DELETE FROM obras WHERE id=87
Excluindo dados da tabela imagens ... 
	DELETE FROM imagens WHERE idobra=87
Excluindo dados da tabela marcacoes ... 
	DELETE FROM marcacoes WHERE idobra=87
Excluindo dados da tabela paginas ... 
	DELETE FROM paginas WHERE idobra=87
Excluindo dados da tabela sumarios ... 
	DELETE FROM sumarios WHERE idobra=87
 Obra lexmagister/87 excluida! 

Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=87 AND tenant='lexmagister'
Atualizando obra como executada: UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=55 AND tenant='gz'
 ---- FIM DA EXECUCAO ---- 
