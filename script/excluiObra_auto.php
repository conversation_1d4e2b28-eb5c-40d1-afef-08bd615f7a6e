<?PHP

 /***
   * Script para excluir obras de forma definitiva. Deve ser executado de forma agendada, como root.
   * Varredura em obras_excluir
   * Looping:
   * 	conecta na base do tenant
   * 	verifica status/etapa da obra (se estiver publicada nao exclui, gera log, return false), etapa tem que ser negativa
   * 	se status = 0 e sincronizar = 1, nao excluir, return false, "aguardar sincronizar 'despublicar'", gera log
   * 	executa exclusao definitiva por etapas
  ***/

##############################
#### CONFIGURACOES BASICAS ###
##############################

$editorPath = "/mnt/dados/www/editor/";
$uploads = "uploads/";
$indices = "indices_montados/";

# $prazo_dias = 2; # Fora de uso
# $prazo_segundos = 43200; // (12horas);
$prazo_segundos = 60; // (1 Min);
$dbconn_monitordeobras = mysqli_connect("*********","jurid",'!kashmir$s!',"monitordeobras");

##############################

// Carrega autoload (AWS-SDK)
require("../vendor/autoload.php");

// Carrega cofiguracoes dos Tenants, do editor
define('ROOT_SYS', '');
include_once("../classes/tenants.inc.php");

use Aws\S3\S3Client;
use Aws\Exception\AwsException;
use Aws\Credentials\Credentials;

# $data_prazo = $prazo_dias * 86400;

$tz= new DateTimeZone("America/Sao_Paulo");
$date = new DateTime("NOW", $tz);
$newdate = $date->sub(new DateInterval('PT'.$prazo_segundos.'S'));
$data_prazo = date_format($date, 'Y-m-d H:i:s');

$log = '';
$dbconn_tenant = [];

$sql = "SELECT * FROM obras_excluir WHERE data <= '".$data_prazo."' AND status = 1";
$log .= "\nSQL obras_excluir: ".$sql;
$res = mysqli_query($dbconn_monitordeobras, $sql);
$log .= "\nTotal de obras encontradas p/ exclusao: ".mysqli_num_rows($res);
while ( ($row=mysqli_fetch_array($res)) ) {
	$tenant = $row['tenant'];
	$idobra = $row['idobra'];

	if (!isset($TENANTS_CONFIG[$tenant]['mysql'])) {
		$log .= "\nConfiguracao do TENANT ".$tenant." nao encontrada";
		continue;
	}

	if (!isset($dbconn_tenant[$tenant]) OR !mysqli_ping($dbconn_tenant[$tenant])) {
		$dbtenant = $TENANTS_CONFIG[$tenant]['mysql'];
		$dbconn_tenant[$tenant] = mysqli_connect($dbtenant['host'], $dbtenant['user'], base64_decode($dbtenant['pass']), $dbtenant['base']);
	}

	if (mysqli_connect_errno() || !mysqli_ping($dbconn_tenant[$tenant])) {
		$log .= "\n - Erro ao tentar conectar na base de dados ".$tenant." / ".$dbtenant['base'];
		continue; // pula esta iteracao/exclusao pois nao foi possivel conectar na base de dados do tenant desta obra
	}

	excluir_obra($idobra, $tenant);

	// - Marcar status = 2 (excluida) e data_execucao = now()
	$sql = sprintf("UPDATE obras_excluir SET status=2,data_execucao=now() WHERE idobra=%d AND tenant='%s'", $idobra, $tenant);
	$log .= "\nAtualizando obra como executada: ".$sql;
	mysqli_query($dbconn_monitordeobras, $sql);

}

$log .= "\n ---- FIM DA EXECUCAO ---- \n";
file_put_contents("obras_excluir.log","\n[".date("Y-m-d H:i:s")."] ----- ".$log, FILE_APPEND);


#####################
###### FUNCOES ######
#####################

function excluir_obra($idobra,$tenant) {
	global $dbconn_tenant, $log;

	if (!isset($dbconn_tenant[$tenant]) OR !mysqli_ping($dbconn_tenant[$tenant]))
		return false;

	// confere se a obra esta mesmo marcada para ser excluida
	$sql = "SELECT id,etapa,status,titulo FROM obras WHERE id=$idobra AND etapa < 0 AND ( ( status = 0 AND sincronizar = 0) OR ( status = 1) )";
	$res = mysqli_query($dbconn_tenant[$tenant], $sql);
	if ( ($row=mysqli_fetch_object($res))) {
		$etapa = $row->etapa;
		if ($idobra != $row->id) return false;
	}
	else return false;

	// Executar exclusao.
	excluir_indices($idobra, $tenant);
	excluir_uploadsdir($idobra, $tenant);
	excluir_s3files($idobra, $tenant);
	excluir_dados_db($idobra, $tenant);
	$log .= "\n Obra ".$tenant."/".$idobra." excluida! \n";
}

function excluir_indices($idobra, $tenant) {
	global $log, $editorPath, $indices, $uploads;

	if (!is_numeric($idobra) || $idobra <= 0)
		return false;

	$arquivoIndice = $editorPath.$indices."/".$tenant."/".$idobra.".idx";
	if (is_file($arquivoIndice)) {
		$log .= "\nExcluindo: ".$arquivoIndice;
		$log .= "\n --- ( rm -rf $arquivoIndice )";
		shell_exec("rm -rf $arquivoIndice");
	}
	else $log .= "\nArquivo de indice nao encontrado: ".$arquivoIndice."\n";
}

function excluir_uploadsdir($idobra, $tenant) {
	global $log, $editorPath, $uploads;

	if (!is_numeric($idobra) || $idobra <= 0)
		return false;

	$uploadsDir = $editorPath.$uploads."/".$tenant."/".$idobra;
	if (is_dir($uploadsDir)) {
		$log .= "\nExcluindo: ".$uploadsDir;
		$log .= "\n --- ( rm -rf $uploadsDir )";
		shell_exec("rm -rf $uploadsDir");
	}
	else $log .= "\nDiretorio de uploads nao encontrado: ".$uploadsDir."\n";
}

function excluir_s3files($idobra, $tenant) {
	global $log;
	// excluir arquivos do S3
	$credentials = new Credentials('********************','v5LU6LMJ9I1WkjQj6KdajUp74GvbWzMxDcdMBNUc');
	$s3Client = new S3Client([
		'version' => 'latest',
		'region' => 'sa-east-1',
		'credentials' => $credentials
	]);
	$arquivos_s3 = [
		$tenant."/".$idobra.".pdf",
		$tenant."/".$idobra.".idx",
		$tenant."/".$idobra.".jbe",
	];

	foreach($arquivos_s3 as $arquivo) {
		try {
			$log .= "\nExcluindo arquivo no S3... ".$arquivo;

			$res = $s3Client->deleteObject([
				'Bucket' => 'lettore-ebooks',
				'Key' => $arquivo,
			]);
			if ($res['DeleteMarker']) {
				$log .= "\n... Arquivo ".$arquivo." excluido do S3";
			}
			else {
				$log .= "\n... ERRO: Nao foi possivel excluir o arquivo: ".$arquivo;
			}
		}
		catch (S3Exception $e) {
			$log .= "\n - ERRO ao tentar excluir arquivo (".$arquivo."): ".$e->getAwsErrorMessage();
		}
	}
}

function excluir_dados_db($idobra, $tenant) {
	global $log, $dbconn_tenant;

	if (!isset($dbconn_tenant[$tenant]) OR !mysqli_ping($dbconn_tenant[$tenant]))
		return false;

	if (!is_numeric($idobra) OR $idobra <= 0)
		return false;

	// excluir dados do banco
	$log .= "\nExcluindo dados da tabela obras ... ";
	$sql = "DELETE FROM obras WHERE id=$idobra";
	$log .= "\n\t".$sql;
	mysqli_query($dbconn_tenant[$tenant], $sql);

	$log .= "\nExcluindo dados da tabela imagens ... ";
	$sql = "DELETE FROM imagens WHERE idobra=$idobra";
	$log .= "\n\t".$sql;
	mysqli_query($dbconn_tenant[$tenant], $sql);

	$log .= "\nExcluindo dados da tabela marcacoes ... ";
	$sql = "DELETE FROM marcacoes WHERE idobra=$idobra";
	$log .= "\n\t".$sql;
	mysqli_query($dbconn_tenant[$tenant], $sql);

	$log .= "\nExcluindo dados da tabela paginas ... ";
	$sql = "DELETE FROM paginas WHERE idobra=$idobra";
	$log .= "\n\t".$sql;
	mysqli_query($dbconn_tenant[$tenant], $sql);

	$log .= "\nExcluindo dados da tabela sumarios ... ";
	$sql = "DELETE FROM sumarios WHERE idobra=$idobra";
	$log .= "\n\t".$sql;
	mysqli_query($dbconn_tenant[$tenant], $sql);
}

?>
