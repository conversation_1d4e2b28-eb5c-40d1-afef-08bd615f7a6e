# Documentação Técnica - Editor Lettore

## 📋 Visão Geral do Sistema

O **Editor Lettore** é uma aplicação web PHP completa para gestão e processamento de e-books, projetada para operar em um ambiente multi-tenant. O sistema permite upload, processamento, edição e publicação de obras digitais, com foco em livros jurídicos e técnicos.

### Características Principais
- **Arquitetura Multi-tenant**: Suporte a múltiplas editoras com isolamento de dados
- **Processamento de PDF**: Conversão e tratamento de documentos PDF
- **Geração de Sumário Automática**: Análise e estruturação de conteúdo
- **Sistema de Templates**: Interface personalizada por tenant
- **Performance otimizada**: Suporte ao Swoole para alta performance
- **Multi-arquitetura**: Compatível com AMD64 e ARM64

---

## 🏗️ Arquitetura do Sistema

### Stack Tecnológica
- **Backend**: PHP 8.2+ com Swoole
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap
- **Banco de Dados**: MySQL 8.0
- **Cache**: Redis 7
- **Proxy**: Nginx 1.24
- **Containerização**: Docker + Docker Compose
- **Templates**: Twig 3.0 (via sistema customizado TwigWrapper)
- **AWS SDK**: Para integração com serviços AWS

### Padrão de Arquitetura
- **MVC**: Model-View-Controller
- **Router personalizado**: Sistema de roteamento em `index.php`
- **Middleware**: Sistema de before/after para autenticação
- **Singleton**: ConfigManager para gerenciamento de configurações
- **Factory Pattern**: Para criação de conexões de banco

---

## 🗂️ Estrutura de Diretórios

```
lettore/editor/
├── .claude/                          # Documentação técnica
├── classes/                          # Classes PHP principais
│   ├── ConfigManager.php             # Gerenciador de configurações (Singleton)
│   ├── SwooleBootstrap.php          # Bootstrap para Swoole
│   ├── TwigWrapper.php              # Wrapper personalizado do Twig
│   ├── geral.class.php              # Classe principal da aplicação
│   ├── comum.inc.php                # Configurações globais
│   ├── conecta.class.php            # Classe de conexão com BD
│   ├── obras.class.php              # Lógica de negócios para obras
│   ├── dom.class.php                # Manipulação de DOM/HTML
│   ├── canvas.class.php             # Processamento de canvas/imagens
│   └── tenants.inc.php              # Configurações de tenants
├── views/                           # Controllers (Views no padrão MVC)
│   ├── login.php                    # Controladores de login
│   ├── obras.php                    # Controladores de obras
│   ├── obra.php                     # Controlador de obra individual
│   └── etapas.php                   # Controladores de etapas de processamento
├── templates/                       # Templates Twig
│   ├── bases/                       # Templates base
│   ├── includes/                    # Componentes reutilizáveis
│   ├── modals/                      # Modais da interface
│   └── errors/                      # Páginas de erro
├── tenants/                         # Configurações por tenant
│   └── [tenant]/                    # Cada tenant tem sua pasta
│       ├── media/                   # Assets específicos da tenant
│       └── templates/               # Templates personalizados
├── media/                           # Assets globais
├── docker/                          # Configurações Docker
├── docs/                            # Documentação e schemas SQL
├── obras_montadas/                  # Obras processadas
├── indices_montados/                # Índices full-text
├── capas_temp/                      # Capas temporárias
├── downloads/                       # Downloads gerados
├── logs/                            # Logs da aplicação
├── vendor/                          # Dependências Composer
├── index.php                        # Ponto de entrada principal
├── swoole-server.php               # Servidor Swoole
├── docker-compose.yml              # Configuração Docker Compose
└── Dockerfile                       # Configuração Docker
```

---

## ⚙️ Componentes Principais

### 1. ConfigManager (classes/ConfigManager.php)
**Padrão**: Singleton
**Responsabilidade**: Gerenciamento centralizado de configurações

```php
class ConfigManager
{
    private static $instance = null;
    private $settings = null;
    private $tenant = null;
    
    // Funcionalidades:
    // - Carregamento de configurações de tenant
    // - Detecção automática de tenant por hostname
    // - Cache de configurações para Swoole
    // - Compatibilidade com variáveis globais
}
```

**Características**:
- Detecção automática de tenant baseada no hostname
- Cache persistente para ambiente Swoole
- Configuração de paths por tenant
- Validação de integridade de configurações

### 2. Classe Geral (classes/geral.class.php)
**Responsabilidade**: Controlador principal da aplicação

```php
class Geral 
{
    public $settings = NULL;
    public $tenant = null;
    public $usuarioLogado = NULL;
    
    // Funcionalidades principais:
    // - Sistema de roteamento
    // - Gerenciamento de sessão
    // - Sistema de templates
    // - Comunicação com API
    // - Tratamento de erros
}
```

**Funcionalidades**:
- **Roteamento**: Sistema personalizado com regex para URLs
- **Templates**: Integração com Twig via TwigWrapper
- **Sessões**: Gerenciamento de sessão com compatibilidade Swoole
- **API**: Cliente para comunicação com APIs externas
- **Middleware**: Sistema before/after para interceptação

### 3. Sistema Multi-tenant

**Arquivo**: `classes/tenants.inc.php`
**Estrutura**:
```php
$TENANTS_CONFIG = array(
    "tenant_name" => array(
        "tenant" => "tenant_name",
        "nome" => "Nome da Editora",
        "host" => "editor.tenant.lettore.com.br",
        "style" => array(
            "primary_color" => "#308ccb",
            "secondary_color" => "#2879b0"
        ),
        "mysql" => array(
            "host" => "database_host",
            "user" => "db_user",
            "base" => "tenant_database"
        )
    )
);
```

**Detecção de Tenant**:
1. Extrai hostname da requisição
2. Remove porta se presente
3. Usa regex `/\.?([A-Za-z0-9-]+)\.lettore/` para extrair tenant
4. Localiza configuração no array `$TENANTS_CONFIG`
5. Configura paths específicos da tenant

### 4. Sistema de Templates (TwigWrapper)

**Integração**: Twig 3.0 com wrapper personalizado
**Localização**: `classes/TwigWrapper.php`

**Hierarquia de Templates**:
1. `/tenants/{tenant}/templates/` (específico da tenant)
2. `/templates/` (templates globais)

**Variáveis Globais Disponíveis**:
- `tenant`: Dados da tenant atual
- `work_dir`: Diretório de trabalho
- `media_dir`: Diretório de assets
- `usuario`: Usuário logado
- `SESSION`, `SERVER`, `COOKIE`, `POST`, `GET`: Superglobais PHP

### 5. Sistema de Banco de Dados

**Classe**: `conecta.class.php`
**Pattern**: Factory + Query Object

```php
class Conecta {
    private $host, $usuario, $password, $database;
    
    // Funcionalidades:
    // - Conexão MySQL com configuração por tenant
    // - Sistema de transações
    // - Query builder simples
    // - Encoding automático (UTF-8)
}

class Query {
    // Execução de queries
    // Fetch de resultados
    // Encoding automático de strings
}
```

---

## 🔄 Fluxo de Processamento de Obras

### Etapas do Processamento

1. **Upload** (Etapa 0)
   - Upload do arquivo PDF
   - Validação de formato
   - Extração de metadados básicos

2. **Mastigação** (Etapa 1-4)
   - Conversão de PDF para formato processável
   - Extração de texto e estrutura
   - Análise de layout e formatação
   - **Classe responsável**: `masterroboto.class.php`

3. **Tratamento** (Etapa 5)
   - Limpeza e normalização do conteúdo
   - Identificação de elementos estruturais
   - **Classe responsável**: `dom.class.php`

4. **Geração de Sumário** (Etapa 6-7)
   - Análise automática de títulos e seções
   - Criação de estrutura hierárquica
   - Geração de links internos

5. **Processamento Final** (Etapa 8-9)
   - Geração de índices FTS
   - Criação de arquivos JBE
   - **Classes responsáveis**: `indicefts.class.php`, `canvas.class.php`

6. **Publicação** (Etapa 10)
   - Validação final
   - Disponibilização para venda
   - Sincronização com API

### Classes de Negócio Principais

**obras.class.php**: Lógica central de obras
```php
class Obras extends conecta {
    // Listagem e filtragem de obras
    // CRUD de obras
    // Gerenciamento de etapas
    // Controle de status
}

class Obra {
    // Modelo de dados da obra
    // Propriedades: título, autor, ISBN, etc.
}
```

---

## 🐋 Infraestrutura Docker

### Docker Compose (docker-compose.yml)

**Serviços**:
1. **lettore-app** (PHP 8.2 + Swoole)
   - Base: `php:8.2-cli`
   - Swoole para alta performance
   - Volumes persistentes para dados

2. **lettore-db** (MySQL 8.0)
   - Inicialização com `schema_montagem.sql`
   - Volume persistente para dados

3. **lettore-nginx** (Nginx 1.24)
   - Proxy reverso
   - Serve arquivos estáticos

4. **redis** (Redis 7)
   - Cache de sessões
   - Cache de aplicação

### Configuração de Volumes
```yaml
volumes:
  obras_montadas:     # Obras processadas
  indices_montados:   # Índices full-text
  capas_temp:         # Capas temporárias  
  downloads:          # Downloads gerados
  logs:              # Logs da aplicação
```

### Suporte Multi-arquitetura
- **AMD64**: Dockerfile padrão
- **ARM64**: Dockerfile.arm64 (otimizado)
- Detecção automática via scripts
- Performance 20-50% melhor em ARM64

---

## 🔐 Sistema de Autenticação e Autorização

### Middleware de Autenticação
**Arquivo**: `middlewares/before.php`

```php
function is_auth($app) {
    // Verifica sessão ativa
    // Valida usuário no banco
    // Redireciona para login se necessário
}
```

### Fluxo de Autenticação
1. **Login** (`/login/autenticar`)
   - Validação de credenciais
   - Criação de sessão
   - Armazenamento de dados do usuário

2. **Middleware** (`before = ["is_auth"]`)
   - Interceptação de rotas protegidas
   - Validação de sessão ativa
   - Renovação automática de sessão

3. **Logout** (`/logout`)
   - Destruição de sessão
   - Redirecionamento para login

---

## 📊 Sistema de Roteamento

### Configuração (index.php)
```php
$urls = [
    "/"                                     => "\Views\Login\Login",
    "/obras"                                => "\Views\Obras\Listar", 
    "/obra/cadastro"                        => "\Views\Obras\Cadastro",
    "/obra/sumario/(?P<idobra>\d+)"        => "\Views\Obra\Sumario",
    "/obra/mastigar/?(?P<idobra>\d+)?"     => "\Views\Etapas\Mastigar",
    // ... demais rotas
];
```

### Sistema de Dispatch
1. **URL Parsing**: Remoção de parâmetros GET
2. **Pattern Matching**: Regex com grupos nomeados
3. **Class Loading**: Instanciação dinâmica de controllers
4. **Method Dispatch**: Chamada do método HTTP correspondente
5. **Middleware Execution**: Before/After hooks

### Controllers (Views)
**Estrutura padrão**:
```php
namespace Views\Obras;

class Listar {
    public $before = array("is_auth");  // Middlewares before
    public $after = array();            // Middlewares after
    
    function GET($matches, $app) {
        // Lógica do GET
    }
    
    function POST($matches, $app) {
        // Lógica do POST  
    }
}
```

---

## 🚀 Ambiente de Desenvolvimento vs Produção

### Detecção de Ambiente
**Arquivo**: `classes/local.php` (presente apenas em desenvolvimento)

**Desenvolvimento**:
- Debug ativado
- Todos os tenants disponíveis
- Tenant padrão: "ltr"
- Logs verbosos
- Auto-reload de configurações

**Produção**:
- Debug desativado
- Tenant específico por hostname
- Cache agressivo
- Logs otimizados
- Configurações estáticas

### Configuração por Ambiente
```php
// ConfigManager->loadTenantConfig()
if (file_exists($local_file)) {
    $this->settings["debug"] = true;
    $this->settings["work_dir"] = "";
    $key_tenant = "ltr"; // Tenant padrão para dev
} else {
    // Produção - detecção por hostname
    $this->tenant = $this->findTenant($host_without_port, $key_tenant);
}
```

---

## 📈 Sistema de Performance (Swoole)

### Configuração do Servidor
**Arquivo**: `swoole-server.php`

```php
$server = new Swoole\Http\Server('0.0.0.0', 9501);
$server->set([
    'worker_num' => 4,
    'max_request' => 10000,
    'enable_static_handler' => true,
    'enable_gzip' => true
]);
```

### Bootstrap Integration
**Arquivo**: `classes/SwooleBootstrap.php`

**Funcionalidades**:
- Inicialização de workers
- Setup de superglobais ($_GET, $_POST, etc.)
- Gerenciamento de configurações persistentes
- Detecção de mudanças de host
- Cleanup automático

### Otimizações
- **Persistent Connections**: Reutilização de conexões DB
- **Memory Management**: Cleanup automático após requests
- **Static File Serving**: Nginx serve assets, Swoole serve PHP
- **Worker Processes**: 4 workers para paralelização

---

## 📚 Sistema de Templates e UI

### Hierarquia de Templates
1. **Base**: `templates/bases/base.html`
   - Layout principal
   - Includes CSS/JS globais
   - Headers e meta tags

2. **Tenant Override**: `tenants/{tenant}/templates/`
   - Templates personalizados por tenant
   - Fallback para templates globais

3. **Includes**: `templates/includes/`
   - Componentes reutilizáveis
   - Header, footer, navegação

### Sistema de CSS Dinâmico
**Geração**: `geral.class.php->generate_css()`

```php
// Processo:
// 1. Carrega template css/colors.css
// 2. Aplica cores da tenant
// 3. Gera arquivo /media/generate_css/{tenant}.css
// 4. Inclui no HTML via template base
```

### Assets por Tenant
```
tenants/{tenant}/media/
├── img/
│   ├── logo.png
│   ├── logo-login.png
│   └── favico.png
└── colors.css (opcional)
```

---

## 🔍 Sistema de Busca e Indexação

### Full-Text Search
**Classe**: `indicefts.class.php`
**Localização**: `indices_montados/{tenant}/`

**Processo**:
1. Extração de texto das obras processadas
2. Tokenização e normalização
3. Criação de índices invertidos
4. Armazenamento em arquivos `.idx`

### Estrutura de Índices
```
indices_montados/
└── {tenant}/
    ├── 1.idx      # Índice da obra ID 1
    ├── 2.idx      # Índice da obra ID 2
    └── ...
```

---

## 🛠️ Comandos e Deployment

### Docker Commands
```bash
# Build multi-arquitetura
make build

# Build específico ARM64  
make build-arm64

# Start aplicação
docker-compose up -d

# Monitoramento
docker stats

# Health check
curl http://localhost:8080/health.php
```

### Health Checks
**Arquivo**: `health.php`
- Status da aplicação
- Conectividade com banco
- Status do Redis
- Verificação de volumes

---

## 🔐 Segurança

### Medidas Implementadas

1. **Autenticação Obrigatória**
   - Sessões seguras
   - Middleware de autenticação
   - Timeout automático

2. **Validação de Entrada**
   - Escape de SQL (mysqli_escape_string)
   - Validação de tipos
   - Sanitização de uploads

3. **Isolamento Multi-tenant**
   - Bancos separados por tenant
   - Paths isolados
   - Configurações independentes

4. **Controle de Acesso**
   - Verificação de permissões por rota
   - Headers de segurança
   - Rate limiting (via Nginx)

### Configurações de Segurança
- HTTPS obrigatório em produção
- Headers de segurança no Nginx
- Validação de uploads
- Logs de auditoria

---

## 📋 Modelos de Dados

### Principais Tabelas (schema_montagem.sql)

**obras**:
```sql
CREATE TABLE `obras` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `titulo` varchar(500) NOT NULL,
  `autor` varchar(500) NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '10',
  `etapa` tinyint(4) NOT NULL DEFAULT '0',
  `datapublicacao` datetime,
  `datalancamento` date,
  -- ... demais campos
);
```

**Status da Obra**:
- `10`: Rascunho
- `20`: Publicada
- `30`: Em venda

**Etapas de Processamento**:
- `0`: Upload inicial
- `1-4`: Mastigação
- `5`: Tratamento
- `6-7`: Sumário
- `8-9`: Processamento final
- `10`: Publicada

---

## 🚨 Tratamento de Erros

### Sistema de Erros
**Classe**: `handleerrors.class.php`

**Tipos de Erro**:
- Erros de aplicação
- Erros de banco de dados
- Erros de API
- Erros 404/403/500

### Logging
**Localização**: `/logs/`
- `swoole.log`: Logs do servidor Swoole
- `debug.log`: Logs de debug da aplicação
- `error.log`: Logs de erro

### Páginas de Erro
**Templates**: `templates/errors/`
- `404.html`: Página não encontrada
- `500.html`: Erro interno
- `maintenance.html`: Manutenção

---

## 🔄 APIs e Integrações

### Cliente API (geral.class.php)
```php
function acessaAPI($metodo, $dados = [], $decode_json = true) {
    // Comunicação com API externa
    // Autenticação via HTTP Basic
    // Header de tenant
    // Tratamento de erros JSON
}
```

### Configuração por Tenant
```php
// tenants.inc.php
"api_login" => "usuario_api",
"api_senha" => "senha_api_hash",
```

### Endpoints Comuns
- `api.lettore.com.br/{metodo}`: API principal
- Autenticação: HTTP Basic Auth
- Header: `Tenant: {tenant_name}`
- Formato: JSON

---

## 💡 Boas Práticas e Padrões

### Padrões de Código
1. **Naming Convention**:
   - Classes: PascalCase
   - Métodos: camelCase
   - Variáveis: snake_case para compatibilidade

2. **Estrutura de Classes**:
   - Construtor com parent call
   - Destructor com cleanup
   - Propriedades públicas documentadas

3. **Error Handling**:
   - Try-catch em operações críticas
   - Logs detalhados
   - Fallbacks para produção

### Otimizações
1. **Cache**:
   - ConfigManager singleton
   - Templates compilados (Twig)
   - Sessões em Redis

2. **Database**:
   - Prepared statements
   - Connection pooling
   - Query optimization

3. **Assets**:
   - CSS/JS minificado
   - Compressão gzip
   - Cache headers

---

## 🔮 Considerações Futuras

### Melhorias Sugeridas

1. **Modernização**:
   - Migração para PSR-4 autoloading
   - Implementação de PSR-7 (HTTP Messages)
   - Uso de frameworks modernos (Symfony Components)

2. **Performance**:
   - Cache de queries
   - CDN para assets
   - Database sharding

3. **Monitoramento**:
   - APM integration
   - Metrics collection
   - Alert system

4. **Segurança**:
   - JWT authentication
   - RBAC implementation
   - Audit logging

### Roadmap Técnico
- [ ] Containerização completa
- [ ] CI/CD pipeline
- [ ] Monitoring stack
- [ ] Backup automatizado
- [ ] Disaster recovery
- [ ] Load balancing

---

## 📞 Referências e Contatos

### Documentação Relacionada
- `README.md`: Visão geral do projeto
- `ARM64-DEPLOY.md`: Deployment em ARM64
- `ARQUITETURA-MULTI.md`: Suporte multi-arquitetura
- `docker-compose.yml`: Configuração de containers

### Arquivos Chave para Manutenção
- `classes/ConfigManager.php`: Configurações centralizadas
- `classes/geral.class.php`: Lógica principal
- `classes/tenants.inc.php`: Configuração de tenants
- `index.php`: Roteamento central
- `swoole-server.php`: Servidor de produção

**Versão do Sistema**: ********  
**Data da Documentação**: 2025-08-25  
**Autor**: Documentação gerada via análise técnica automatizada