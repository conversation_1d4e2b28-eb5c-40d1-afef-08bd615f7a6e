# Migração para PHP 8.3 com Swoole - Correção de Escopo Global

## Problema Identificado

Durante a migração de PHP 5.6 para PHP 8.3 com Swoole, as variáveis globais `$TENANT` e `$SETTINGS` eram perdidas quando a classe `Geral` recarregava o `comum.inc.php`. Isso acontecia porque:

1. **Escopo Global no Swoole**: O Swoole mantém estado persistente em memória, mas `include_once` dentro de métodos de classe não cria variáveis no escopo global verdadeiro
2. **PHP 8.3 Changes**: Mudanças no gerenciamento de escopo tornaram-se mais rigorosas
3. **Contexto de Execução**: Variáveis eram criadas no escopo local do método, não globalmente

## Solução Implementada

### 1. Classe ConfigManager (Singleton)

Criada a classe `classes/ConfigManager.php` que:
- Gerencia configurações usando padrão Singleton
- Mantém estado consistente entre requisições
- Oferece compatibilidade com código existente
- Detecta mudanças de host dinamicamente
- Valida integridade das configurações

### 2. SwooleBootstrap

Criada a classe `classes/SwooleBootstrap.php` que:
- Inicializa aplicação para ambiente Swoole
- Configura superglobais corretamente
- Gerencia ciclo de vida das configurações
- Oferece hooks para eventos do Swoole

### 3. Refatoração do comum.inc.php

O arquivo foi simplificado para:
- Usar ConfigManager em vez de variáveis globais diretas
- Manter compatibilidade com código existente
- Definir variáveis globais para retrocompatibilidade

### 4. Atualização da Classe Geral

O construtor foi modificado para:
- Usar ConfigManager em vez de include_once
- Inicializar SwooleBootstrap quando disponível
- Garantir configurações consistentes

## Arquivos Modificados

1. **classes/ConfigManager.php** (novo)
2. **classes/SwooleBootstrap.php** (novo)
3. **classes/comum.inc.php** (refatorado)
4. **classes/geral.class.php** (atualizado)
5. **swoole-server.php** (implementado)

## Como Usar

### Desenvolvimento Local (PHP tradicional)

O código continua funcionando normalmente:

```bash
php -S localhost:8000
```

### Produção com Swoole

```bash
php swoole-server.php
```

### Verificar Funcionamento

```php
// Em qualquer arquivo da aplicação
$geral = new Geral();
var_dump($geral->settings); // Deve mostrar configurações
var_dump($geral->tenant);   // Deve mostrar tenant
```

## Benefícios

1. **Compatibilidade**: Código existente continua funcionando
2. **Performance**: Configurações são mantidas em memória no Swoole
3. **Robustez**: Detecção automática de problemas de configuração
4. **Flexibilidade**: Suporte a múltiplos tenants dinamicamente
5. **Debugging**: Logs detalhados para troubleshooting

## Monitoramento

Para monitorar o funcionamento:

```bash
# Ver logs do Swoole
tail -f logs/swoole.log

# Ver logs do PHP
tail -f /var/log/php_errors.log
```

## Debugging

As seguintes mensagens de log indicam funcionamento correto:

```
ConfigManager Debug: Configurações inicializadas com sucesso
Geral Debug: Settings carregados via ConfigManager
SwooleBootstrap: Inicialização concluída
```

## Troubleshooting

### Problema: Tenant não encontrado
- Verificar configuração de DNS/hosts
- Conferir arquivo tenants.inc.php
- Validar host no arquivo local.php (desenvolvimento)

### Problema: Configurações vazias
- Verificar se ConfigManager está sendo inicializado
- Conferir logs para mensagens de erro
- Validar permissões de arquivo

### Problema: Performance
- Verificar número de workers Swoole
- Monitorar uso de memória
- Ajustar configurações do servidor

## Teste

Para testar as mudanças:

1. **Teste local**: Verificar que aplicação funciona em modo tradicional
2. **Teste Swoole**: Iniciar servidor Swoole e testar requisições
3. **Teste multi-tenant**: Verificar diferentes hosts/tenants
4. **Teste recarregamento**: Verificar que configurações se mantêm entre requisições

## Compatibilidade

- ✅ PHP 8.3
- ✅ Swoole 4.x+
- ✅ Código legado existente
- ✅ Múltiplos tenants
- ✅ Desenvolvimento local
- ✅ Ambiente Docker